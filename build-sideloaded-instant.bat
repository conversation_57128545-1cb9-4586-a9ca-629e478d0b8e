@echo off
setlocal enabledelayedexpansion

echo Super Fast Sideloaded Plugin Builder
echo ====================================

REM Set paths
set PROJECT_ROOT=C:\Users\<USER>\Documents\git\microbot
set CLASSES_DIR=%PROJECT_ROOT%\runelite-client\target\classes
set TARGET_DIR=%PROJECT_ROOT%\runelite-client\target

REM Change to project directory
cd /d "%PROJECT_ROOT%"

echo [INFO] Checking if classes are compiled...

REM Check if classes directory exists and has basic classes
if not exist "%CLASSES_DIR%\net\runelite\client\plugins\microbot" (
    echo [ERROR] Classes not found. Please run a full Maven build first:
    echo mvn compile -pl runelite-client -DskipTests
    pause
    exit /b 1
)

echo [INFO] Classes found. Building sideloaded plugin JARs...

REM Run only the plugin builder (assumes classes are already compiled)
mvn -pl runelite-client exec:java@build-dynamic-sideloaded-plugins -q

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Plugin building failed!
    echo [INFO] Try running full compilation first:
    echo mvn compile -pl runelite-client -DskipTests
    pause
    exit /b 1
)

echo [SUCCESS] Super fast build completed!
echo [INFO] If you made code changes and they're not reflected, run:
echo mvn compile -pl runelite-client -DskipTests
pause
