@echo off
echo Quick Sideloaded Plugin Builder
echo ================================

REM Set working directory to microbot root
cd /d "C:\Users\<USER>\Documents\git\microbot"

echo [INFO] Building sideloaded plugins (optimized)...

REM Skip most phases and only run what's needed
mvn -pl runelite-client ^
    -Dmaven.test.skip=true ^
    -Dcheckstyle.skip=true ^
    -Dlombok.delombok.skip=true ^
    -Dgit.commit.id.skip=true ^
    -Dmaven.javadoc.skip=true ^
    -Dmaven.source.skip=true ^
    compile exec:java@build-dynamic-sideloaded-plugins

echo [INFO] Build completed!
