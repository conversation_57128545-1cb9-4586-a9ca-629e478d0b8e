- **Start botting**
    - Install Microbot
- [**Development**](Development.md)
    - **API**
      * [Rs2Bank](api/apidocs/net/runelite/client/plugins/microbot/util/bank/Rs2Bank.html)
      * [Rs2Camera](api/apidocs/net/runelite/client/plugins/microbot/util/camera/Rs2Camera.html)
      * [Rs2Combat](api/apidocs/net/runelite/client/plugins/microbot/util/combat/Rs2Combat.html)
      * [Rs2Dialogues](api/apidocs/net/runelite/client/plugins/microbot/util/dialogues/Rs2Dialogues.html)
      * [Rs2Equipment](api/apidocs/net/runelite/client/plugins/microbot/util/equipment/Rs2Equipment.html)
      * [Rs2Cannon](api/apidocs/net/runelite/client/plugins/microbot/util/gameobject/Rs2Cannon.html)
      * [Rs2GameObject](api/apidocs/net/runelite/client/plugins/microbot/util/gameobject/Rs2GameObject.html)
      * [Rs2GroundItem](api/apidocs/net/runelite/client/plugins/microbot/util/grounditem/Rs2GroundItem.html)
      * [Rs2Inventory](api/apidocs/net/runelite/client/plugins/microbot/util/inventory/Rs2Inventory.html)
      * [Rs2KeyBoard](api/apidocs/net/runelite/client/plugins/microbot/util/keyboard/Rs2Keyboard.html)
      * [Rs2Magic](api/apidocs/net/runelite/client/plugins/microbot/util/magic/Rs2Magic.html)
      * [Rs2Spells](api/apidocs/net/runelite/client/plugins/microbot/util/magic/Rs2Spells.html)
      * [Rs2Food](api/apidocs/net/runelite/client/plugins/microbot/util/misc/Rs2Food.html)
      * [Rs2Npc](api/apidocs/net/runelite/client/plugins/microbot/util/npc/Rs2Npc.html)
      * [Rs2Player](api/apidocs/net/runelite/client/plugins/microbot/util/player/Rs2Player.html)
      * [Rs2Pvp](api/apidocs/net/runelite/client/plugins/microbot/util/player/Rs2Pvp.html)
      * [Rs2Prayer](api/apidocs/net/runelite/client/plugins/microbot/util/prayer/Rs2Prayer.html)
      * [Rs2Reflection](api/apidocs/net/runelite/client/plugins/microbot/util/prayer/Rs2Reflection.html)
      * [Rs2Settings](api/apidocs/net/runelite/client/plugins/microbot/util/settings/Rs2Settings.html)
      * [Rs2Tab](api/apidocs/net/runelite/client/plugins/microbot/util/tabs/Rs2Settings.html)
      * [Rs2Walker](api/apidocs/net/runelite/client/plugins/microbot/util/walker/Rs2Settings.html)
      * [Rs2Minimap](api/apidocs/net/runelite/client/plugins/microbot/util/walker/Rs2Settings.html)
      * [Rs2Widget](api/apidocs/net/runelite/client/plugins/microbot/util/widget/Rs2Widget.html)
    - **Script Examples**
        * [Fighter Script](combat.md)
    - **Plugin Scheduler**
      * [Overview](scheduler/README.md)
      * [User Guide](scheduler/user-guide.md)
      * [Defining Conditions](scheduler/defining-conditions.md)
      * [PluginScheduleEntry](scheduler/plugin-schedule-entry-merged.md)
      * [SchedulerPlugin](scheduler/scheduler-plugin.md)
      * [PluginScheduleEntryFinishedEvent](scheduler/event/plugin-schedule-entry-finished-event.md)
      * [PluginScheduleEntrySoftStopEvent](scheduler/event/plugin-schedule-entry-soft-stop-event.md)
      * [Time Conditions](scheduler/time-conditions.md)
      * [Logical Conditions](scheduler/logical-conditions.md)
      * [Resource Conditions](scheduler/resource-conditions.md)
      * [Skill Conditions](scheduler/skill-conditions.md)
      * [Location Conditions](scheduler/location-conditions.md)
      * [NPC Conditions](scheduler/npc-conditions.md)