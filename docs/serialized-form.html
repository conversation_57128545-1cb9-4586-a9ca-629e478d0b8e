<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>Serialized Form (RuneLite Client 1.10.28-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="serialized forms">
<meta name="generator" content="javadoc/SerializedFormWriterImpl">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="serialized-form-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="api/apidocs/index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#serialized-form">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Serialized Form" class="title">Serialized Form</h1>
</div>
<ul class="block-list">
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/package-summary.html">net.runelite.client.plugins</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.PluginInstantiationException">
<h3>Exception&nbsp;<a href="net/runelite/client/plugins/PluginInstantiationException.html" title="class in net.runelite.client.plugins">net.runelite.client.plugins.PluginInstantiationException</a></h3>
<div class="type-signature">class PluginInstantiationException extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/devtools/package-summary.html">net.runelite.client.plugins.devtools</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.devtools.DevToolsButton">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/devtools/DevToolsButton.html" title="class in net.runelite.client.plugins.devtools">net.runelite.client.plugins.devtools.DevToolsButton</a></h3>
<div class="type-signature">class DevToolsButton extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JButton.html" title="class or interface in javax.swing" class="external-link">JButton</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>active</h5>
<pre>boolean active</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.devtools.DevToolsFrame">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/devtools/DevToolsFrame.html" title="class in net.runelite.client.plugins.devtools">net.runelite.client.plugins.devtools.DevToolsFrame</a></h3>
<div class="type-signature">class DevToolsFrame extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JFrame.html" title="class or interface in javax.swing" class="external-link">JFrame</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>devToolsButton</h5>
<pre><a href="net/runelite/client/plugins/devtools/DevToolsButton.html" title="class in net.runelite.client.plugins.devtools">DevToolsButton</a> devToolsButton</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.devtools.ScriptInspector">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/devtools/ScriptInspector.html" title="class in net.runelite.client.plugins.devtools">net.runelite.client.plugins.devtools.ScriptInspector</a></h3>
<div class="type-signature">class ScriptInspector extends <a href="net/runelite/client/plugins/devtools/DevToolsFrame.html" title="class in net.runelite.client.plugins.devtools">DevToolsFrame</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>blacklist</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt; blacklist</pre>
</li>
<li class="block-list">
<h5>client</h5>
<pre>net.runelite.api.Client client</pre>
</li>
<li class="block-list">
<h5>configManager</h5>
<pre><a href="net/runelite/client/config/ConfigManager.html" title="class in net.runelite.client.config">ConfigManager</a> configManager</pre>
</li>
<li class="block-list">
<h5>currentNode</h5>
<pre>net.runelite.client.plugins.devtools.ScriptInspector.ScriptTreeNode currentNode</pre>
</li>
<li class="block-list">
<h5>eventBus</h5>
<pre><a href="net/runelite/client/eventbus/EventBus.html" title="class in net.runelite.client.eventbus">EventBus</a> eventBus</pre>
</li>
<li class="block-list">
<h5>highlights</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt; highlights</pre>
</li>
<li class="block-list">
<h5>jList</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JList.html" title="class or interface in javax.swing" class="external-link">JList</a> jList</pre>
</li>
<li class="block-list">
<h5>lastTick</h5>
<pre>int lastTick</pre>
</li>
<li class="block-list">
<h5>listModel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/DefaultListModel.html" title="class or interface in javax.swing" class="external-link">DefaultListModel</a> listModel</pre>
</li>
<li class="block-list">
<h5>state</h5>
<pre>net.runelite.client.plugins.devtools.ScriptInspector.ListState state</pre>
</li>
<li class="block-list">
<h5>tracker</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> tracker</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.devtools.WidgetInfoTableModel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/devtools/WidgetInfoTableModel.html" title="class in net.runelite.client.plugins.devtools">net.runelite.client.plugins.devtools.WidgetInfoTableModel</a></h3>
<div class="type-signature">class WidgetInfoTableModel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/table/AbstractTableModel.html" title="class or interface in javax.swing.table" class="external-link">AbstractTableModel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>client</h5>
<pre>net.runelite.api.Client client</pre>
</li>
<li class="block-list">
<h5>clientThread</h5>
<pre><a href="net/runelite/client/callback/ClientThread.html" title="class in net.runelite.client.callback">ClientThread</a> clientThread</pre>
</li>
<li class="block-list">
<h5>fields</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/plugins/devtools/WidgetField.html" title="class in net.runelite.client.plugins.devtools">WidgetField</a>&lt;?&gt;&gt; fields</pre>
</li>
<li class="block-list">
<h5>values</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="net/runelite/client/plugins/devtools/WidgetField.html" title="class in net.runelite.client.plugins.devtools">WidgetField</a>&lt;?&gt;,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt; values</pre>
</li>
<li class="block-list">
<h5>widget</h5>
<pre>net.runelite.api.widgets.Widget widget</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/grandexchange/package-summary.html">net.runelite.client.plugins.grandexchange</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.grandexchange.GrandExchangeOfferSlot">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/grandexchange/GrandExchangeOfferSlot.html" title="class in net.runelite.client.plugins.grandexchange">net.runelite.client.plugins.grandexchange.GrandExchangeOfferSlot</a></h3>
<div class="type-signature">class GrandExchangeOfferSlot extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>cardLayout</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/CardLayout.html" title="class or interface in java.awt" class="external-link">CardLayout</a> cardLayout</pre>
</li>
<li class="block-list">
<h5>container</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> container</pre>
</li>
<li class="block-list">
<h5>grandExchangePlugin</h5>
<pre><a href="net/runelite/client/plugins/grandexchange/GrandExchangePlugin.html" title="class in net.runelite.client.plugins.grandexchange">GrandExchangePlugin</a> grandExchangePlugin</pre>
</li>
<li class="block-list">
<h5>itemIcon</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> itemIcon</pre>
</li>
<li class="block-list">
<h5>itemName</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> itemName</pre>
</li>
<li class="block-list">
<h5>itemPrice</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> itemPrice</pre>
</li>
<li class="block-list">
<h5>offerInfo</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> offerInfo</pre>
</li>
<li class="block-list">
<h5>offerSpent</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> offerSpent</pre>
</li>
<li class="block-list">
<h5>progressBar</h5>
<pre><a href="net/runelite/client/ui/components/ThinProgressBar.html" title="class in net.runelite.client.ui.components">ThinProgressBar</a> progressBar</pre>
</li>
<li class="block-list">
<h5>showingFace</h5>
<pre>boolean showingFace</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/hiscore/package-summary.html">net.runelite.client.plugins.hiscore</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.hiscore.HiscorePanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/hiscore/HiscorePanel.html" title="class in net.runelite.client.plugins.hiscore">net.runelite.client.plugins.hiscore.HiscorePanel</a></h3>
<div class="type-signature">class HiscorePanel extends <a href="net/runelite/client/ui/PluginPanel.html" title="class in net.runelite.client.ui">PluginPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>config</h5>
<pre><a href="net/runelite/client/plugins/hiscore/HiscoreConfig.html" title="interface in net.runelite.client.plugins.hiscore">HiscoreConfig</a> config</pre>
</li>
<li class="block-list">
<h5>hiscoreClient</h5>
<pre><a href="net/runelite/client/hiscore/HiscoreClient.html" title="class in net.runelite.client.hiscore">HiscoreClient</a> hiscoreClient</pre>
</li>
<li class="block-list">
<h5>loading</h5>
<pre>boolean loading</pre>
</li>
<li class="block-list">
<h5>nameAutocompleter</h5>
<pre>net.runelite.client.plugins.hiscore.NameAutocompleter nameAutocompleter</pre>
</li>
<li class="block-list">
<h5>plugin</h5>
<pre><a href="net/runelite/client/plugins/hiscore/HiscorePlugin.html" title="class in net.runelite.client.plugins.hiscore">HiscorePlugin</a> plugin</pre>
</li>
<li class="block-list">
<h5>searchBar</h5>
<pre><a href="net/runelite/client/ui/components/IconTextField.html" title="class in net.runelite.client.ui.components">IconTextField</a> searchBar</pre>
</li>
<li class="block-list">
<h5>selectedEndPoint</h5>
<pre><a href="net/runelite/client/hiscore/HiscoreEndpoint.html" title="enum class in net.runelite.client.hiscore">HiscoreEndpoint</a> selectedEndPoint</pre>
</li>
<li class="block-list">
<h5>skillLabels</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="net/runelite/client/hiscore/HiscoreSkill.html" title="enum class in net.runelite.client.hiscore">HiscoreSkill</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a>&gt; skillLabels</pre>
</li>
<li class="block-list">
<h5>tabGroup</h5>
<pre><a href="net/runelite/client/ui/components/materialtabs/MaterialTabGroup.html" title="class in net.runelite.client.ui.components.materialtabs">MaterialTabGroup</a> tabGroup</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/info/package-summary.html">net.runelite.client.plugins.info</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.info.InfoPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/info/InfoPanel.html" title="class in net.runelite.client.plugins.info">net.runelite.client.plugins.info.InfoPanel</a></h3>
<div class="type-signature">class InfoPanel extends <a href="net/runelite/client/ui/PluginPanel.html" title="class in net.runelite.client.ui">PluginPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>actionsContainer</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> actionsContainer</pre>
</li>
<li class="block-list">
<h5>client</h5>
<pre>net.runelite.api.Client client</pre>
</li>
<li class="block-list">
<h5>discordInvite</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> discordInvite</pre>
</li>
<li class="block-list">
<h5>emailLabel</h5>
<pre><a href="net/runelite/client/plugins/info/JRichTextPane.html" title="class in net.runelite.client.plugins.info">JRichTextPane</a> emailLabel</pre>
</li>
<li class="block-list">
<h5>eventBus</h5>
<pre><a href="net/runelite/client/eventbus/EventBus.html" title="class in net.runelite.client.eventbus">EventBus</a> eventBus</pre>
</li>
<li class="block-list">
<h5>executor</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/ScheduledExecutorService.html" title="class or interface in java.util.concurrent" class="external-link">ScheduledExecutorService</a> executor</pre>
</li>
<li class="block-list">
<h5>githubLink</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> githubLink</pre>
</li>
<li class="block-list">
<h5>loggedLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> loggedLabel</pre>
</li>
<li class="block-list">
<h5>patreonLink</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> patreonLink</pre>
</li>
<li class="block-list">
<h5>runeliteVersion</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> runeliteVersion</pre>
</li>
<li class="block-list">
<h5>sessionManager</h5>
<pre><a href="net/runelite/client/account/SessionManager.html" title="class in net.runelite.client.account">SessionManager</a> sessionManager</pre>
</li>
<li class="block-list">
<h5>wikiLink</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> wikiLink</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.info.JRichTextPane">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/info/JRichTextPane.html" title="class in net.runelite.client.plugins.info">net.runelite.client.plugins.info.JRichTextPane</a></h3>
<div class="type-signature">class JRichTextPane extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JEditorPane.html" title="class or interface in javax.swing" class="external-link">JEditorPane</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>linkHandler</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/event/HyperlinkListener.html" title="class or interface in javax.swing.event" class="external-link">HyperlinkListener</a> linkHandler</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/package-summary.html">net.runelite.client.plugins.inventorysetups.ui</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsAdditionalItemsPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsAdditionalItemsPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsAdditionalItemsPanel</a></h3>
<div class="type-signature">class InventorySetupsAdditionalItemsPanel extends <a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsContainerPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsContainerPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>additionalFilteredSlots</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsSlot.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsSlot</a>&gt; additionalFilteredSlots</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsAmmunitionPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsAmmunitionPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsAmmunitionPanel</a></h3>
<div class="type-signature">class InventorySetupsAmmunitionPanel extends <a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsContainerPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsContainerPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>ammoSlots</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsSlot.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsSlot</a>&gt; ammoSlots</pre>
</li>
<li class="block-list">
<h5>ammoSlotsAddedToPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&gt; ammoSlotsAddedToPanel</pre>
</li>
<li class="block-list">
<h5>gridLayout</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/GridLayout.html" title="class or interface in java.awt" class="external-link">GridLayout</a> gridLayout</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsBoltPouchPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsBoltPouchPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsBoltPouchPanel</a></h3>
<div class="type-signature">class InventorySetupsBoltPouchPanel extends <a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsAmmunitionPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsAmmunitionPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsCompactPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsCompactPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsCompactPanel</a></h3>
<div class="type-signature">class InventorySetupsCompactPanel extends <a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsContainerPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsContainerPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsContainerPanel</a></h3>
<div class="type-signature">class InventorySetupsContainerPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>containerSlotsPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> containerSlotsPanel</pre>
</li>
<li class="block-list">
<h5>isHighlighted</h5>
<pre>boolean isHighlighted</pre>
</li>
<li class="block-list">
<h5>itemManager</h5>
<pre><a href="net/runelite/client/game/ItemManager.html" title="class in net.runelite.client.game">ItemManager</a> itemManager</pre>
</li>
<li class="block-list">
<h5>plugin</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetupsPlugin.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetupsPlugin</a> plugin</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsCycleButton">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsCycleButton.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsCycleButton</a></h3>
<div class="type-signature">class InventorySetupsCycleButton extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>currentIndex</h5>
<pre>int currentIndex</pre>
</li>
<li class="block-list">
<h5>hoverIcons</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/ImageIcon.html" title="class or interface in javax.swing" class="external-link">ImageIcon</a>&gt; hoverIcons</pre>
</li>
<li class="block-list">
<h5>icons</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/ImageIcon.html" title="class or interface in javax.swing" class="external-link">ImageIcon</a>&gt; icons</pre>
</li>
<li class="block-list">
<h5>plugin</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetupsPlugin.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetupsPlugin</a> plugin</pre>
</li>
<li class="block-list">
<h5>runnableAdapter</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/event/MouseAdapter.html" title="class or interface in java.awt.event" class="external-link">MouseAdapter</a> runnableAdapter</pre>
</li>
<li class="block-list">
<h5>states</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsCycleButton.html" title="type parameter in InventorySetupsCycleButton">T</a>&gt; states</pre>
</li>
<li class="block-list">
<h5>tooltips</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt; tooltips</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsEquipmentPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsEquipmentPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsEquipmentPanel</a></h3>
<div class="type-signature">class InventorySetupsEquipmentPanel extends <a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsContainerPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsContainerPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>equipmentSlots</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;net.runelite.api.EquipmentInventorySlot,<wbr><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsSlot.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsSlot</a>&gt; equipmentSlots</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsIconPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsIconPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsIconPanel</a></h3>
<div class="type-signature">class InventorySetupsIconPanel extends <a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsInventoryPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsInventoryPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsInventoryPanel</a></h3>
<div class="type-signature">class InventorySetupsInventoryPanel extends <a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsContainerPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsContainerPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>boltPouchPanel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsBoltPouchPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsBoltPouchPanel</a> boltPouchPanel</pre>
</li>
<li class="block-list">
<h5>inventorySlots</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsSlot.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsSlot</a>&gt; inventorySlots</pre>
</li>
<li class="block-list">
<h5>runePouchPanel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsRunePouchPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsRunePouchPanel</a> runePouchPanel</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsMoveMenu">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsMoveMenu.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsMoveMenu</a></h3>
<div class="type-signature">class InventorySetupsMoveMenu extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPopupMenu.html" title="class or interface in javax.swing" class="external-link">JPopupMenu</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>panel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsPluginPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsPluginPanel</a> panel</pre>
</li>
<li class="block-list">
<h5>plugin</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetupsPlugin.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetupsPlugin</a> plugin</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsNameActions">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsNameActions.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsNameActions</a></h3>
<div class="type-signature">class InventorySetupsNameActions extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>cancel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> cancel</pre>
</li>
<li class="block-list">
<h5>datum</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsNameActions.html" title="type parameter in InventorySetupsNameActions">T</a> extends <a href="net/runelite/client/plugins/inventorysetups/InventorySetupsDisplayAttributes.html" title="interface in net.runelite.client.plugins.inventorysetups">InventorySetupsDisplayAttributes</a> datum</pre>
</li>
<li class="block-list">
<h5>displayColorIndicator</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> displayColorIndicator</pre>
</li>
<li class="block-list">
<h5>edit</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> edit</pre>
</li>
<li class="block-list">
<h5>nameInput</h5>
<pre><a href="net/runelite/client/ui/components/FlatTextField.html" title="class in net.runelite.client.ui.components">FlatTextField</a> nameInput</pre>
</li>
<li class="block-list">
<h5>save</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> save</pre>
</li>
<li class="block-list">
<h5>validNameImplementer</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetupsValidName.html" title="interface in net.runelite.client.plugins.inventorysetups">InventorySetupsValidName</a> validNameImplementer</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsNotesPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsNotesPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsNotesPanel</a></h3>
<div class="type-signature">class InventorySetupsNotesPanel extends <a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsContainerPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsContainerPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>currentInventorySetup</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetup.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetup</a> currentInventorySetup</pre>
</li>
<li class="block-list">
<h5>notesEditor</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JTextArea.html" title="class or interface in javax.swing" class="external-link">JTextArea</a> notesEditor</pre>
</li>
<li class="block-list">
<h5>undoRedo</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/undo/UndoManager.html" title="class or interface in javax.swing.undo" class="external-link">UndoManager</a> undoRedo</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsPanel</a></h3>
<div class="type-signature">class InventorySetupsPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>inventorySetup</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetup.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetup</a> inventorySetup</pre>
</li>
<li class="block-list">
<h5>panel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsPluginPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsPluginPanel</a> panel</pre>
</li>
<li class="block-list">
<h5>plugin</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetupsPlugin.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetupsPlugin</a> plugin</pre>
</li>
<li class="block-list">
<h5>popupMenu</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPopupMenu.html" title="class or interface in javax.swing" class="external-link">JPopupMenu</a> popupMenu</pre>
</li>
<li class="block-list">
<h5>section</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetupsSection.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetupsSection</a> section</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsPluginPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsPluginPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsPluginPanel</a></h3>
<div class="type-signature">class InventorySetupsPluginPanel extends <a href="net/runelite/client/ui/PluginPanel.html" title="class in net.runelite.client.ui">PluginPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>additionalFilteredItemsPanel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsAdditionalItemsPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsAdditionalItemsPanel</a> additionalFilteredItemsPanel</pre>
</li>
<li class="block-list">
<h5>addMarker</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> addMarker</pre>
</li>
<li class="block-list">
<h5>backMarker</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> backMarker</pre>
</li>
<li class="block-list">
<h5>boltPouchPanel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsBoltPouchPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsBoltPouchPanel</a> boltPouchPanel</pre>
</li>
<li class="block-list">
<h5>contentWrapperPane</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JScrollPane.html" title="class or interface in javax.swing" class="external-link">JScrollPane</a> contentWrapperPane</pre>
</li>
<li class="block-list">
<h5>currentSelectedSetup</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetup.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetup</a> currentSelectedSetup</pre>
</li>
<li class="block-list">
<h5>equipmentPanel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsEquipmentPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsEquipmentPanel</a> equipmentPanel</pre>
</li>
<li class="block-list">
<h5>filteredInventorysetups</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/plugins/inventorysetups/InventorySetup.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetup</a>&gt; filteredInventorysetups</pre>
</li>
<li class="block-list">
<h5>helpButton</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> helpButton</pre>
</li>
<li class="block-list">
<h5>importMarker</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> importMarker</pre>
</li>
<li class="block-list">
<h5>inventoryPanel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsInventoryPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsInventoryPanel</a> inventoryPanel</pre>
</li>
<li class="block-list">
<h5>mainTitle</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> mainTitle</pre>
</li>
<li class="block-list">
<h5>northAnchoredPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> northAnchoredPanel</pre>
</li>
<li class="block-list">
<h5>noSetupsPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> noSetupsPanel</pre>
</li>
<li class="block-list">
<h5>notesPanel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsNotesPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsNotesPanel</a> notesPanel</pre>
</li>
<li class="block-list">
<h5>overviewPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> overviewPanel</pre>
</li>
<li class="block-list">
<h5>overviewPanelScrollPosition</h5>
<pre>int overviewPanelScrollPosition</pre>
</li>
<li class="block-list">
<h5>overviewTopPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> overviewTopPanel</pre>
</li>
<li class="block-list">
<h5>panelViewMarker</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsCycleButton.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsCycleButton</a>&lt;<a href="net/runelite/client/plugins/inventorysetups/InventorySetupsPanelViewID.html" title="enum class in net.runelite.client.plugins.inventorysetups">InventorySetupsPanelViewID</a>&gt; panelViewMarker</pre>
</li>
<li class="block-list">
<h5>plugin</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetupsPlugin.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetupsPlugin</a> plugin</pre>
</li>
<li class="block-list">
<h5>runePouchPanel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsRunePouchPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsRunePouchPanel</a> runePouchPanel</pre>
</li>
<li class="block-list">
<h5>searchBar</h5>
<pre><a href="net/runelite/client/ui/components/IconTextField.html" title="class in net.runelite.client.ui.components">IconTextField</a> searchBar</pre>
</li>
<li class="block-list">
<h5>sectionViewMarker</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> sectionViewMarker</pre>
</li>
<li class="block-list">
<h5>setupDisplayPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> setupDisplayPanel</pre>
</li>
<li class="block-list">
<h5>setupTitle</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> setupTitle</pre>
</li>
<li class="block-list">
<h5>setupTopPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> setupTopPanel</pre>
</li>
<li class="block-list">
<h5>sortingMarker</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> sortingMarker</pre>
</li>
<li class="block-list">
<h5>spellbookPanel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsSpellbookPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsSpellbookPanel</a> spellbookPanel</pre>
</li>
<li class="block-list">
<h5>updateMarker</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> updateMarker</pre>
</li>
<li class="block-list">
<h5>updateNewsPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> updateNewsPanel</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsRunePouchPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsRunePouchPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsRunePouchPanel</a></h3>
<div class="type-signature">class InventorySetupsRunePouchPanel extends <a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsAmmunitionPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsAmmunitionPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsSectionPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsSectionPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsSectionPanel</a></h3>
<div class="type-signature">class InventorySetupsSectionPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>allowEditable</h5>
<pre>boolean allowEditable</pre>
</li>
<li class="block-list">
<h5>forceMaximization</h5>
<pre>boolean forceMaximization</pre>
</li>
<li class="block-list">
<h5>minMaxLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> minMaxLabel</pre>
</li>
<li class="block-list">
<h5>panel</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsPluginPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsPluginPanel</a> panel</pre>
</li>
<li class="block-list">
<h5>panelWithSetups</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> panelWithSetups</pre>
</li>
<li class="block-list">
<h5>plugin</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetupsPlugin.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetupsPlugin</a> plugin</pre>
</li>
<li class="block-list">
<h5>section</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetupsSection.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetupsSection</a> section</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsSlot">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsSlot.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsSlot</a></h3>
<div class="type-signature">class InventorySetupsSlot extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>fuzzyIndicator</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> fuzzyIndicator</pre>
</li>
<li class="block-list">
<h5>imageLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> imageLabel</pre>
</li>
<li class="block-list">
<h5>indexInSlot</h5>
<pre>int indexInSlot</pre>
</li>
<li class="block-list">
<h5>parentSetup</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetup.html" title="class in net.runelite.client.plugins.inventorysetups">InventorySetup</a> parentSetup</pre>
</li>
<li class="block-list">
<h5>rightClickMenu</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPopupMenu.html" title="class or interface in javax.swing" class="external-link">JPopupMenu</a> rightClickMenu</pre>
</li>
<li class="block-list">
<h5>shiftRightClickMenu</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPopupMenu.html" title="class or interface in javax.swing" class="external-link">JPopupMenu</a> shiftRightClickMenu</pre>
</li>
<li class="block-list">
<h5>slotID</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/InventorySetupsSlotID.html" title="enum class in net.runelite.client.plugins.inventorysetups">InventorySetupsSlotID</a> slotID</pre>
</li>
<li class="block-list">
<h5>stackIndicator</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> stackIndicator</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsSpellbookPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsSpellbookPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsSpellbookPanel</a></h3>
<div class="type-signature">class InventorySetupsSpellbookPanel extends <a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsContainerPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsContainerPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>spellbookImages</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/image/BufferedImage.html" title="class or interface in java.awt.image" class="external-link">BufferedImage</a>&gt; spellbookImages</pre>
</li>
<li class="block-list">
<h5>spellbookSlot</h5>
<pre><a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsSlot.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsSlot</a> spellbookSlot</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsStandardPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsStandardPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsStandardPanel</a></h3>
<div class="type-signature">class InventorySetupsStandardPanel extends <a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">InventorySetupsPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>bankFilterIndicator</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> bankFilterIndicator</pre>
</li>
<li class="block-list">
<h5>deleteLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> deleteLabel</pre>
</li>
<li class="block-list">
<h5>exportLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> exportLabel</pre>
</li>
<li class="block-list">
<h5>favoriteIndicator</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> favoriteIndicator</pre>
</li>
<li class="block-list">
<h5>highlightColorIndicator</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> highlightColorIndicator</pre>
</li>
<li class="block-list">
<h5>highlightIndicator</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> highlightIndicator</pre>
</li>
<li class="block-list">
<h5>unorderedHighlightIndicator</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> unorderedHighlightIndicator</pre>
</li>
<li class="block-list">
<h5>viewSetupLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> viewSetupLabel</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.inventorysetups.ui.InventorySetupsUpdateNewsPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/inventorysetups/ui/InventorySetupsUpdateNewsPanel.html" title="class in net.runelite.client.plugins.inventorysetups.ui">net.runelite.client.plugins.inventorysetups.ui.InventorySetupsUpdateNewsPanel</a></h3>
<div class="type-signature">class InventorySetupsUpdateNewsPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/microbot/pvp/package-summary.html">net.runelite.client.plugins.microbot.pvp</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.microbot.pvp.JRichTextPane">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/microbot/pvp/JRichTextPane.html" title="class in net.runelite.client.plugins.microbot.pvp">net.runelite.client.plugins.microbot.pvp.JRichTextPane</a></h3>
<div class="type-signature">class JRichTextPane extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JEditorPane.html" title="class or interface in javax.swing" class="external-link">JEditorPane</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>linkHandler</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/event/HyperlinkListener.html" title="class or interface in javax.swing.event" class="external-link">HyperlinkListener</a> linkHandler</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/questhelper/package-summary.html">net.runelite.client.plugins.questhelper</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.questhelper.QuestInstantiationException">
<h3>Exception&nbsp;<a href="net/runelite/client/plugins/questhelper/QuestInstantiationException.html" title="class in net.runelite.client.plugins.questhelper">net.runelite.client.plugins.questhelper.QuestInstantiationException</a></h3>
<div class="type-signature">class QuestInstantiationException extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/questhelper/panel/package-summary.html">net.runelite.client.plugins.questhelper.panel</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.questhelper.panel.DropdownRenderer">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/questhelper/panel/DropdownRenderer.html" title="class in net.runelite.client.plugins.questhelper.panel">net.runelite.client.plugins.questhelper.panel.DropdownRenderer</a></h3>
<div class="type-signature">class DropdownRenderer extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/DefaultListCellRenderer.html" title="class or interface in javax.swing" class="external-link">DefaultListCellRenderer</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.questhelper.panel.FixedWidthPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/questhelper/panel/FixedWidthPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">net.runelite.client.plugins.questhelper.panel.FixedWidthPanel</a></h3>
<div class="type-signature">class FixedWidthPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.questhelper.panel.QuestHelperPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/questhelper/panel/QuestHelperPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">net.runelite.client.plugins.questhelper.panel.QuestHelperPanel</a></h3>
<div class="type-signature">class QuestHelperPanel extends <a href="net/runelite/client/ui/PluginPanel.html" title="class in net.runelite.client.ui">PluginPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>allDropdownSections</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> allDropdownSections</pre>
</li>
<li class="block-list">
<h5>allQuestsCompletedPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> allQuestsCompletedPanel</pre>
</li>
<li class="block-list">
<h5>difficultyDropdown</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JComboBox.html" title="class or interface in javax.swing" class="external-link">JComboBox</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="class or interface in java.lang" class="external-link">Enum</a>&gt; difficultyDropdown</pre>
</li>
<li class="block-list">
<h5>filterDropdown</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JComboBox.html" title="class or interface in javax.swing" class="external-link">JComboBox</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="class or interface in java.lang" class="external-link">Enum</a>&gt; filterDropdown</pre>
</li>
<li class="block-list">
<h5>orderDropdown</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JComboBox.html" title="class or interface in javax.swing" class="external-link">JComboBox</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="class or interface in java.lang" class="external-link">Enum</a>&gt; orderDropdown</pre>
</li>
<li class="block-list">
<h5>questActive</h5>
<pre>boolean questActive</pre>
</li>
<li class="block-list">
<h5>questHelperPlugin</h5>
<pre><a href="net/runelite/client/plugins/questhelper/QuestHelperPlugin.html" title="class in net.runelite.client.plugins.questhelper">QuestHelperPlugin</a> questHelperPlugin</pre>
</li>
<li class="block-list">
<h5>questListPanel</h5>
<pre><a href="net/runelite/client/plugins/questhelper/panel/FixedWidthPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">FixedWidthPanel</a> questListPanel</pre>
</li>
<li class="block-list">
<h5>questListWrapper</h5>
<pre><a href="net/runelite/client/plugins/questhelper/panel/FixedWidthPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">FixedWidthPanel</a> questListWrapper</pre>
</li>
<li class="block-list">
<h5>questOverviewPanel</h5>
<pre><a href="net/runelite/client/plugins/questhelper/panel/QuestOverviewPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">QuestOverviewPanel</a> questOverviewPanel</pre>
</li>
<li class="block-list">
<h5>questOverviewWrapper</h5>
<pre><a href="net/runelite/client/plugins/questhelper/panel/FixedWidthPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">FixedWidthPanel</a> questOverviewWrapper</pre>
</li>
<li class="block-list">
<h5>questSelectPanels</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/ArrayList.html" title="class or interface in java.util" class="external-link">ArrayList</a>&lt;<a href="net/runelite/client/plugins/questhelper/panel/QuestSelectPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">QuestSelectPanel</a>&gt; questSelectPanels</pre>
</li>
<li class="block-list">
<h5>scrollableContainer</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JScrollPane.html" title="class or interface in javax.swing" class="external-link">JScrollPane</a> scrollableContainer</pre>
</li>
<li class="block-list">
<h5>searchBar</h5>
<pre><a href="net/runelite/client/ui/components/IconTextField.html" title="class in net.runelite.client.ui.components">IconTextField</a> searchBar</pre>
</li>
<li class="block-list">
<h5>searchQuestsPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> searchQuestsPanel</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.questhelper.panel.QuestOverviewPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/questhelper/panel/QuestOverviewPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">net.runelite.client.plugins.questhelper.panel.QuestOverviewPanel</a></h3>
<div class="type-signature">class QuestOverviewPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>actionsContainer</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> actionsContainer</pre>
</li>
<li class="block-list">
<h5>collapseBtn</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JButton.html" title="class or interface in javax.swing" class="external-link">JButton</a> collapseBtn</pre>
</li>
<li class="block-list">
<h5>configContainer</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> configContainer</pre>
</li>
<li class="block-list">
<h5>currentQuest</h5>
<pre><a href="net/runelite/client/plugins/questhelper/questhelpers/QuestHelper.html" title="class in net.runelite.client.plugins.questhelper.questhelpers">QuestHelper</a> currentQuest</pre>
</li>
<li class="block-list">
<h5>externalQuestResourcesHeader</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> externalQuestResourcesHeader</pre>
</li>
<li class="block-list">
<h5>externalQuestResourcesPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> externalQuestResourcesPanel</pre>
</li>
<li class="block-list">
<h5>introPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> introPanel</pre>
</li>
<li class="block-list">
<h5>questCombatRequirementHeader</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questCombatRequirementHeader</pre>
</li>
<li class="block-list">
<h5>questCombatRequirementsListPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questCombatRequirementsListPanel</pre>
</li>
<li class="block-list">
<h5>questGeneralRecommendedHeader</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questGeneralRecommendedHeader</pre>
</li>
<li class="block-list">
<h5>questGeneralRecommendedListPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questGeneralRecommendedListPanel</pre>
</li>
<li class="block-list">
<h5>questGeneralRequirementsHeader</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questGeneralRequirementsHeader</pre>
</li>
<li class="block-list">
<h5>questGeneralRequirementsListPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questGeneralRequirementsListPanel</pre>
</li>
<li class="block-list">
<h5>questHelperPlugin</h5>
<pre><a href="net/runelite/client/plugins/questhelper/QuestHelperPlugin.html" title="class in net.runelite.client.plugins.questhelper">QuestHelperPlugin</a> questHelperPlugin</pre>
</li>
<li class="block-list">
<h5>questItemRecommendedHeader</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questItemRecommendedHeader</pre>
</li>
<li class="block-list">
<h5>questItemRecommendedListPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questItemRecommendedListPanel</pre>
</li>
<li class="block-list">
<h5>questItemRequirementsHeader</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questItemRequirementsHeader</pre>
</li>
<li class="block-list">
<h5>questItemRequirementsListPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questItemRequirementsListPanel</pre>
</li>
<li class="block-list">
<h5>questNameLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> questNameLabel</pre>
</li>
<li class="block-list">
<h5>questNoteHeader</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questNoteHeader</pre>
</li>
<li class="block-list">
<h5>questOverviewNotes</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> questOverviewNotes</pre>
</li>
<li class="block-list">
<h5>questOverviewNotesPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questOverviewNotesPanel</pre>
</li>
<li class="block-list">
<h5>questRewardHeader</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questRewardHeader</pre>
</li>
<li class="block-list">
<h5>questRewardPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questRewardPanel</pre>
</li>
<li class="block-list">
<h5>questStepPanelList</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/plugins/questhelper/panel/QuestStepPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">QuestStepPanel</a>&gt; questStepPanelList</pre>
</li>
<li class="block-list">
<h5>questStepsContainer</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> questStepsContainer</pre>
</li>
<li class="block-list">
<h5>requirementPanels</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/plugins/questhelper/panel/QuestRequirementPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">QuestRequirementPanel</a>&gt; requirementPanels</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.questhelper.panel.QuestRequirementPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/questhelper/panel/QuestRequirementPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">net.runelite.client.plugins.questhelper.panel.QuestRequirementPanel</a></h3>
<div class="type-signature">class QuestRequirementPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>label</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> label</pre>
</li>
<li class="block-list">
<h5>requirement</h5>
<pre><a href="net/runelite/client/plugins/questhelper/requirements/Requirement.html" title="interface in net.runelite.client.plugins.questhelper.requirements">Requirement</a> requirement</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.questhelper.panel.QuestRequirementWrapperPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/questhelper/panel/QuestRequirementWrapperPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">net.runelite.client.plugins.questhelper.panel.QuestRequirementWrapperPanel</a></h3>
<div class="type-signature">class QuestRequirementWrapperPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>questRequirementPanel</h5>
<pre><a href="net/runelite/client/plugins/questhelper/panel/QuestRequirementPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">QuestRequirementPanel</a> questRequirementPanel</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.questhelper.panel.QuestRewardPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/questhelper/panel/QuestRewardPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">net.runelite.client.plugins.questhelper.panel.QuestRewardPanel</a></h3>
<div class="type-signature">class QuestRewardPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>label</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> label</pre>
</li>
<li class="block-list">
<h5>reward</h5>
<pre><a href="net/runelite/client/plugins/questhelper/rewards/Reward.html" title="interface in net.runelite.client.plugins.questhelper.rewards">Reward</a> reward</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.questhelper.panel.QuestRewardWrapperPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/questhelper/panel/QuestRewardWrapperPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">net.runelite.client.plugins.questhelper.panel.QuestRewardWrapperPanel</a></h3>
<div class="type-signature">class QuestRewardWrapperPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>questRewardPanel</h5>
<pre><a href="net/runelite/client/plugins/questhelper/panel/QuestRewardPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">QuestRewardPanel</a> questRewardPanel</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.questhelper.panel.QuestSelectPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/questhelper/panel/QuestSelectPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">net.runelite.client.plugins.questhelper.panel.QuestSelectPanel</a></h3>
<div class="type-signature">class QuestSelectPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>keywords</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt; keywords</pre>
</li>
<li class="block-list">
<h5>questHelper</h5>
<pre><a href="net/runelite/client/plugins/questhelper/questhelpers/QuestHelper.html" title="class in net.runelite.client.plugins.questhelper.questhelpers">QuestHelper</a> questHelper</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.questhelper.panel.QuestStepPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/questhelper/panel/QuestStepPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">net.runelite.client.plugins.questhelper.panel.QuestStepPanel</a></h3>
<div class="type-signature">class QuestStepPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>bodyPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> bodyPanel</pre>
</li>
<li class="block-list">
<h5>currentlyHighlighted</h5>
<pre><a href="net/runelite/client/plugins/questhelper/steps/QuestStep.html" title="class in net.runelite.client.plugins.questhelper.steps">QuestStep</a> currentlyHighlighted</pre>
</li>
<li class="block-list">
<h5>headerLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> headerLabel</pre>
</li>
<li class="block-list">
<h5>headerPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> headerPanel</pre>
</li>
<li class="block-list">
<h5>leftTitleContainer</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> leftTitleContainer</pre>
</li>
<li class="block-list">
<h5>lockStep</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JCheckBox.html" title="class or interface in javax.swing" class="external-link">JCheckBox</a> lockStep</pre>
</li>
<li class="block-list">
<h5>panelDetails</h5>
<pre><a href="net/runelite/client/plugins/questhelper/panel/PanelDetails.html" title="class in net.runelite.client.plugins.questhelper.panel">PanelDetails</a> panelDetails</pre>
</li>
<li class="block-list">
<h5>requirementPanels</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/ArrayList.html" title="class or interface in java.util" class="external-link">ArrayList</a>&lt;<a href="net/runelite/client/plugins/questhelper/panel/QuestRequirementPanel.html" title="class in net.runelite.client.plugins.questhelper.panel">QuestRequirementPanel</a>&gt; requirementPanels</pre>
</li>
<li class="block-list">
<h5>stepAutoLocked</h5>
<pre>boolean stepAutoLocked</pre>
</li>
<li class="block-list">
<h5>steps</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/HashMap.html" title="class or interface in java.util" class="external-link">HashMap</a>&lt;<a href="net/runelite/client/plugins/questhelper/steps/QuestStep.html" title="class in net.runelite.client.plugins.questhelper.steps">QuestStep</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a>&gt; steps</pre>
</li>
<li class="block-list">
<h5>viewControls</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> viewControls</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/screenmarkers/ui/package-summary.html">net.runelite.client.plugins.screenmarkers.ui</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.screenmarkers.ui.ScreenMarkerCreationPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/screenmarkers/ui/ScreenMarkerCreationPanel.html" title="class in net.runelite.client.plugins.screenmarkers.ui">net.runelite.client.plugins.screenmarkers.ui.ScreenMarkerCreationPanel</a></h3>
<div class="type-signature">class ScreenMarkerCreationPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>confirmLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> confirmLabel</pre>
</li>
<li class="block-list">
<h5>instructionsLabel</h5>
<pre><a href="net/runelite/client/ui/components/shadowlabel/JShadowedLabel.html" title="class in net.runelite.client.ui.components.shadowlabel">JShadowedLabel</a> instructionsLabel</pre>
</li>
<li class="block-list">
<h5>lockedConfirm</h5>
<pre>boolean lockedConfirm</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.screenmarkers.ui.ScreenMarkerPluginPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/screenmarkers/ui/ScreenMarkerPluginPanel.html" title="class in net.runelite.client.plugins.screenmarkers.ui">net.runelite.client.plugins.screenmarkers.ui.ScreenMarkerPluginPanel</a></h3>
<div class="type-signature">class ScreenMarkerPluginPanel extends <a href="net/runelite/client/ui/PluginPanel.html" title="class in net.runelite.client.ui">PluginPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>addMarker</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> addMarker</pre>
</li>
<li class="block-list">
<h5>creationPanel</h5>
<pre><a href="net/runelite/client/plugins/screenmarkers/ui/ScreenMarkerCreationPanel.html" title="class in net.runelite.client.plugins.screenmarkers.ui">ScreenMarkerCreationPanel</a> creationPanel</pre>
</li>
<li class="block-list">
<h5>markerView</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> markerView</pre>
</li>
<li class="block-list">
<h5>noMarkersPanel</h5>
<pre><a href="net/runelite/client/ui/components/PluginErrorPanel.html" title="class in net.runelite.client.ui.components">PluginErrorPanel</a> noMarkersPanel</pre>
</li>
<li class="block-list">
<h5>plugin</h5>
<pre><a href="net/runelite/client/plugins/screenmarkers/ScreenMarkerPlugin.html" title="class in net.runelite.client.plugins.screenmarkers">ScreenMarkerPlugin</a> plugin</pre>
</li>
<li class="block-list">
<h5>selectedBorderThickness</h5>
<pre>int selectedBorderThickness</pre>
</li>
<li class="block-list">
<h5>selectedColor</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a> selectedColor</pre>
</li>
<li class="block-list">
<h5>selectedFillColor</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a> selectedFillColor</pre>
</li>
<li class="block-list">
<h5>title</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> title</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/timetracking/package-summary.html">net.runelite.client.plugins.timetracking</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.timetracking.TabContentPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/timetracking/TabContentPanel.html" title="class in net.runelite.client.plugins.timetracking">net.runelite.client.plugins.timetracking.TabContentPanel</a></h3>
<div class="type-signature">class TabContentPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.timetracking.TimeablePanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/timetracking/TimeablePanel.html" title="class in net.runelite.client.plugins.timetracking">net.runelite.client.plugins.timetracking.TimeablePanel</a></h3>
<div class="type-signature">class TimeablePanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>estimate</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> estimate</pre>
</li>
<li class="block-list">
<h5>farmingContractIcon</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> farmingContractIcon</pre>
</li>
<li class="block-list">
<h5>icon</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> icon</pre>
</li>
<li class="block-list">
<h5>notifyButton</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JToggleButton.html" title="class or interface in javax.swing" class="external-link">JToggleButton</a> notifyButton</pre>
</li>
<li class="block-list">
<h5>overlayIcon</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> overlayIcon</pre>
</li>
<li class="block-list">
<h5>progress</h5>
<pre><a href="net/runelite/client/ui/components/ThinProgressBar.html" title="class in net.runelite.client.ui.components">ThinProgressBar</a> progress</pre>
</li>
<li class="block-list">
<h5>text</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> text</pre>
</li>
<li class="block-list">
<h5>timeable</h5>
<pre><a href="net/runelite/client/plugins/timetracking/TimeablePanel.html" title="type parameter in TimeablePanel">T</a> timeable</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/timetracking/clocks/package-summary.html">net.runelite.client.plugins.timetracking.clocks</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.timetracking.clocks.ClockTabPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/timetracking/clocks/ClockTabPanel.html" title="class in net.runelite.client.plugins.timetracking.clocks">net.runelite.client.plugins.timetracking.clocks.ClockTabPanel</a></h3>
<div class="type-signature">class ClockTabPanel extends <a href="net/runelite/client/plugins/timetracking/TabContentPanel.html" title="class in net.runelite.client.plugins.timetracking">TabContentPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>clockManager</h5>
<pre><a href="net/runelite/client/plugins/timetracking/clocks/ClockManager.html" title="class in net.runelite.client.plugins.timetracking.clocks">ClockManager</a> clockManager</pre>
</li>
<li class="block-list">
<h5>clockPanels</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;net.runelite.client.plugins.timetracking.clocks.ClockPanel&gt; clockPanels</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/timetracking/farming/package-summary.html">net.runelite.client.plugins.timetracking.farming</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.timetracking.farming.FarmingNextTickPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/timetracking/farming/FarmingNextTickPanel.html" title="class in net.runelite.client.plugins.timetracking.farming">net.runelite.client.plugins.timetracking.farming.FarmingNextTickPanel</a></h3>
<div class="type-signature">class FarmingNextTickPanel extends <a href="net/runelite/client/plugins/timetracking/TabContentPanel.html" title="class in net.runelite.client.plugins.timetracking">TabContentPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>config</h5>
<pre><a href="net/runelite/client/plugins/timetracking/TimeTrackingConfig.html" title="interface in net.runelite.client.plugins.timetracking">TimeTrackingConfig</a> config</pre>
</li>
<li class="block-list">
<h5>configManager</h5>
<pre><a href="net/runelite/client/config/ConfigManager.html" title="class in net.runelite.client.config">ConfigManager</a> configManager</pre>
</li>
<li class="block-list">
<h5>farmingTracker</h5>
<pre><a href="net/runelite/client/plugins/timetracking/farming/FarmingTracker.html" title="class in net.runelite.client.plugins.timetracking.farming">FarmingTracker</a> farmingTracker</pre>
</li>
<li class="block-list">
<h5>infoTextArea</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JTextArea.html" title="class or interface in javax.swing" class="external-link">JTextArea</a> infoTextArea</pre>
</li>
<li class="block-list">
<h5>patchPanels</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/plugins/timetracking/TimeablePanel.html" title="class in net.runelite.client.plugins.timetracking">TimeablePanel</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="class or interface in java.lang" class="external-link">Void</a>&gt;&gt; patchPanels</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.timetracking.farming.FarmingTabPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/timetracking/farming/FarmingTabPanel.html" title="class in net.runelite.client.plugins.timetracking.farming">net.runelite.client.plugins.timetracking.farming.FarmingTabPanel</a></h3>
<div class="type-signature">class FarmingTabPanel extends <a href="net/runelite/client/plugins/timetracking/TabContentPanel.html" title="class in net.runelite.client.plugins.timetracking">TabContentPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>compostTracker</h5>
<pre><a href="net/runelite/client/plugins/timetracking/farming/CompostTracker.html" title="class in net.runelite.client.plugins.timetracking.farming">CompostTracker</a> compostTracker</pre>
</li>
<li class="block-list">
<h5>config</h5>
<pre><a href="net/runelite/client/plugins/timetracking/TimeTrackingConfig.html" title="interface in net.runelite.client.plugins.timetracking">TimeTrackingConfig</a> config</pre>
</li>
<li class="block-list">
<h5>configManager</h5>
<pre><a href="net/runelite/client/config/ConfigManager.html" title="class in net.runelite.client.config">ConfigManager</a> configManager</pre>
</li>
<li class="block-list">
<h5>farmingContractManager</h5>
<pre><a href="net/runelite/client/plugins/timetracking/farming/FarmingContractManager.html" title="class in net.runelite.client.plugins.timetracking.farming">FarmingContractManager</a> farmingContractManager</pre>
</li>
<li class="block-list">
<h5>farmingTracker</h5>
<pre><a href="net/runelite/client/plugins/timetracking/farming/FarmingTracker.html" title="class in net.runelite.client.plugins.timetracking.farming">FarmingTracker</a> farmingTracker</pre>
</li>
<li class="block-list">
<h5>itemManager</h5>
<pre><a href="net/runelite/client/game/ItemManager.html" title="class in net.runelite.client.game">ItemManager</a> itemManager</pre>
</li>
<li class="block-list">
<h5>patchPanels</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/plugins/timetracking/TimeablePanel.html" title="class in net.runelite.client.plugins.timetracking">TimeablePanel</a>&lt;net.runelite.client.plugins.timetracking.farming.FarmingPatch&gt;&gt; patchPanels</pre>
</li>
<li class="block-list">
<h5>paymentTracker</h5>
<pre><a href="net/runelite/client/plugins/timetracking/farming/PaymentTracker.html" title="class in net.runelite.client.plugins.timetracking.farming">PaymentTracker</a> paymentTracker</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/plugins/timetracking/hunter/package-summary.html">net.runelite.client.plugins.timetracking.hunter</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.plugins.timetracking.hunter.BirdHouseTabPanel">
<h3>Class&nbsp;<a href="net/runelite/client/plugins/timetracking/hunter/BirdHouseTabPanel.html" title="class in net.runelite.client.plugins.timetracking.hunter">net.runelite.client.plugins.timetracking.hunter.BirdHouseTabPanel</a></h3>
<div class="type-signature">class BirdHouseTabPanel extends <a href="net/runelite/client/plugins/timetracking/TabContentPanel.html" title="class in net.runelite.client.plugins.timetracking">TabContentPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>birdHouseTracker</h5>
<pre><a href="net/runelite/client/plugins/timetracking/hunter/BirdHouseTracker.html" title="class in net.runelite.client.plugins.timetracking.hunter">BirdHouseTracker</a> birdHouseTracker</pre>
</li>
<li class="block-list">
<h5>config</h5>
<pre><a href="net/runelite/client/plugins/timetracking/TimeTrackingConfig.html" title="interface in net.runelite.client.plugins.timetracking">TimeTrackingConfig</a> config</pre>
</li>
<li class="block-list">
<h5>configManager</h5>
<pre><a href="net/runelite/client/config/ConfigManager.html" title="class in net.runelite.client.config">ConfigManager</a> configManager</pre>
</li>
<li class="block-list">
<h5>itemManager</h5>
<pre><a href="net/runelite/client/game/ItemManager.html" title="class in net.runelite.client.game">ItemManager</a> itemManager</pre>
</li>
<li class="block-list">
<h5>spacePanels</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/plugins/timetracking/TimeablePanel.html" title="class in net.runelite.client.plugins.timetracking">TimeablePanel</a>&lt;net.runelite.client.plugins.timetracking.hunter.BirdHouseSpace&gt;&gt; spacePanels</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/ui/package-summary.html">net.runelite.client.ui</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.ContainableFrame">
<h3>Class&nbsp;<a href="net/runelite/client/ui/ContainableFrame.html" title="class in net.runelite.client.ui">net.runelite.client.ui.ContainableFrame</a></h3>
<div class="type-signature">class ContainableFrame extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JFrame.html" title="class or interface in javax.swing" class="external-link">JFrame</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>containedInScreen</h5>
<pre><a href="net/runelite/client/ui/ContainableFrame.Mode.html" title="enum class in net.runelite.client.ui">ContainableFrame.Mode</a> containedInScreen</pre>
</li>
<li class="block-list">
<h5>overrideUndecorated</h5>
<pre>boolean overrideUndecorated</pre>
</li>
<li class="block-list">
<h5>rightSideSuction</h5>
<pre>boolean rightSideSuction</pre>
</li>
<li class="block-list">
<h5>scaleMinSize</h5>
<pre>boolean scaleMinSize</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.DynamicGridLayout">
<h3>Class&nbsp;<a href="net/runelite/client/ui/DynamicGridLayout.html" title="class in net.runelite.client.ui">net.runelite.client.ui.DynamicGridLayout</a></h3>
<div class="type-signature">class DynamicGridLayout extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/GridLayout.html" title="class or interface in java.awt" class="external-link">GridLayout</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.FatalErrorDialog">
<h3>Class&nbsp;<a href="net/runelite/client/ui/FatalErrorDialog.html" title="class in net.runelite.client.ui">net.runelite.client.ui.FatalErrorDialog</a></h3>
<div class="type-signature">class FatalErrorDialog extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JDialog.html" title="class or interface in javax.swing" class="external-link">JDialog</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>font</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Font.html" title="class or interface in java.awt" class="external-link">Font</a> font</pre>
</li>
<li class="block-list">
<h5>rightColumn</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> rightColumn</pre>
</li>
<li class="block-list">
<h5>title</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> title</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.MultiplexingPluginPanel">
<h3>Class&nbsp;<a href="net/runelite/client/ui/MultiplexingPluginPanel.html" title="class in net.runelite.client.ui">net.runelite.client.ui.MultiplexingPluginPanel</a></h3>
<div class="type-signature">class MultiplexingPluginPanel extends <a href="net/runelite/client/ui/PluginPanel.html" title="class in net.runelite.client.ui">PluginPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>active</h5>
<pre>boolean active</pre>
</li>
<li class="block-list">
<h5>current</h5>
<pre><a href="net/runelite/client/ui/PluginPanel.html" title="class in net.runelite.client.ui">PluginPanel</a> current</pre>
</li>
<li class="block-list">
<h5>layout</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/CardLayout.html" title="class or interface in java.awt" class="external-link">CardLayout</a> layout</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.PluginPanel">
<h3>Class&nbsp;<a href="net/runelite/client/ui/PluginPanel.html" title="class in net.runelite.client.ui">net.runelite.client.ui.PluginPanel</a></h3>
<div class="type-signature">class PluginPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>scrollPane</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JScrollPane.html" title="class or interface in javax.swing" class="external-link">JScrollPane</a> scrollPane</pre>
</li>
<li class="block-list">
<h5>wrappedPanel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> wrappedPanel</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.SplashScreen">
<h3>Class&nbsp;<a href="net/runelite/client/ui/SplashScreen.html" title="class in net.runelite.client.ui">net.runelite.client.ui.SplashScreen</a></h3>
<div class="type-signature">class SplashScreen extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JFrame.html" title="class or interface in javax.swing" class="external-link">JFrame</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>action</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> action</pre>
</li>
<li class="block-list">
<h5>actionText</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> actionText</pre>
</li>
<li class="block-list">
<h5>overallProgress</h5>
<pre>double overallProgress</pre>
</li>
<li class="block-list">
<h5>progress</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JProgressBar.html" title="class or interface in javax.swing" class="external-link">JProgressBar</a> progress</pre>
</li>
<li class="block-list">
<h5>progressText</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> progressText</pre>
</li>
<li class="block-list">
<h5>subAction</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> subAction</pre>
</li>
<li class="block-list">
<h5>subActionText</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> subActionText</pre>
</li>
<li class="block-list">
<h5>timer</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/Timer.html" title="class or interface in javax.swing" class="external-link">Timer</a> timer</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/ui/components/package-summary.html">net.runelite.client.ui.components</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.ColorJButton">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/ColorJButton.html" title="class in net.runelite.client.ui.components">net.runelite.client.ui.components.ColorJButton</a></h3>
<div class="type-signature">class ColorJButton extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JButton.html" title="class or interface in javax.swing" class="external-link">JButton</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>color</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a> color</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.DimmableJPanel">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/DimmableJPanel.html" title="class in net.runelite.client.ui.components">net.runelite.client.ui.components.DimmableJPanel</a></h3>
<div class="type-signature">class DimmableJPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>dimmed</h5>
<pre>boolean dimmed</pre>
</li>
<li class="block-list">
<h5>dimmedBackground</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a> dimmedBackground</pre>
</li>
<li class="block-list">
<h5>dimmedForeground</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a> dimmedForeground</pre>
</li>
<li class="block-list">
<h5>undimmedBackground</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a> undimmedBackground</pre>
</li>
<li class="block-list">
<h5>undimmedForeground</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a> undimmedForeground</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.DragAndDropReorderPane">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/DragAndDropReorderPane.html" title="class in net.runelite.client.ui.components">net.runelite.client.ui.components.DragAndDropReorderPane</a></h3>
<div class="type-signature">class DragAndDropReorderPane extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLayeredPane.html" title="class or interface in javax.swing" class="external-link">JLayeredPane</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>draggingComponent</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Component.html" title="class or interface in java.awt" class="external-link">Component</a> draggingComponent</pre>
</li>
<li class="block-list">
<h5>dragIndex</h5>
<pre>int dragIndex</pre>
</li>
<li class="block-list">
<h5>dragListeners</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/ui/components/DragAndDropReorderPane.DragListener.html" title="interface in net.runelite.client.ui.components">DragAndDropReorderPane.DragListener</a>&gt; dragListeners</pre>
</li>
<li class="block-list">
<h5>dragStartPoint</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Point.html" title="class or interface in java.awt" class="external-link">Point</a> dragStartPoint</pre>
</li>
<li class="block-list">
<h5>dragYOffset</h5>
<pre>int dragYOffset</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.FlatTextField">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/FlatTextField.html" title="class in net.runelite.client.ui.components">net.runelite.client.ui.components.FlatTextField</a></h3>
<div class="type-signature">class FlatTextField extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>backgroundColor</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a> backgroundColor</pre>
</li>
<li class="block-list">
<h5>blocked</h5>
<pre>boolean blocked</pre>
</li>
<li class="block-list">
<h5>hoverBackgroundColor</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a> hoverBackgroundColor</pre>
</li>
<li class="block-list">
<h5>textField</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JTextField.html" title="class or interface in javax.swing" class="external-link">JTextField</a> textField</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.IconTextField">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/IconTextField.html" title="class in net.runelite.client.ui.components">net.runelite.client.ui.components.IconTextField</a></h3>
<div class="type-signature">class IconTextField extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>clearButton</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JButton.html" title="class or interface in javax.swing" class="external-link">JButton</a> clearButton</pre>
</li>
<li class="block-list">
<h5>clearListeners</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Runnable.html" title="class or interface in java.lang" class="external-link">Runnable</a>&gt; clearListeners</pre>
</li>
<li class="block-list">
<h5>iconWrapperLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> iconWrapperLabel</pre>
</li>
<li class="block-list">
<h5>suggestionButton</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JButton.html" title="class or interface in javax.swing" class="external-link">JButton</a> suggestionButton</pre>
</li>
<li class="block-list">
<h5>suggestionListModel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/DefaultListModel.html" title="class or interface in javax.swing" class="external-link">DefaultListModel</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt; suggestionListModel</pre>
</li>
<li class="block-list">
<h5>textField</h5>
<pre><a href="net/runelite/client/ui/components/FlatTextField.html" title="class in net.runelite.client.ui.components">FlatTextField</a> textField</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.PluginErrorPanel">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/PluginErrorPanel.html" title="class in net.runelite.client.ui.components">net.runelite.client.ui.components.PluginErrorPanel</a></h3>
<div class="type-signature">class PluginErrorPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>noResultsDescription</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> noResultsDescription</pre>
</li>
<li class="block-list">
<h5>noResultsTitle</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> noResultsTitle</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.ProgressBar">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/ProgressBar.html" title="class in net.runelite.client.ui.components">net.runelite.client.ui.components.ProgressBar</a></h3>
<div class="type-signature">class ProgressBar extends <a href="net/runelite/client/ui/components/DimmableJPanel.html" title="class in net.runelite.client.ui.components">DimmableJPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>centerLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> centerLabel</pre>
</li>
<li class="block-list">
<h5>centerLabelText</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> centerLabelText</pre>
</li>
<li class="block-list">
<h5>dimmedText</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> dimmedText</pre>
</li>
<li class="block-list">
<h5>leftLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> leftLabel</pre>
</li>
<li class="block-list">
<h5>maximumValue</h5>
<pre>int maximumValue</pre>
</li>
<li class="block-list">
<h5>positions</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt; positions</pre>
</li>
<li class="block-list">
<h5>rightLabel</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> rightLabel</pre>
</li>
<li class="block-list">
<h5>value</h5>
<pre>int value</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.ThinProgressBar">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/ThinProgressBar.html" title="class in net.runelite.client.ui.components">net.runelite.client.ui.components.ThinProgressBar</a></h3>
<div class="type-signature">class ThinProgressBar extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>maximumValue</h5>
<pre>int maximumValue</pre>
</li>
<li class="block-list">
<h5>value</h5>
<pre>int value</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.TitleCaseListCellRenderer">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/TitleCaseListCellRenderer.html" title="class in net.runelite.client.ui.components">net.runelite.client.ui.components.TitleCaseListCellRenderer</a></h3>
<div class="type-signature">class TitleCaseListCellRenderer extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/DefaultListCellRenderer.html" title="class or interface in javax.swing" class="external-link">DefaultListCellRenderer</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/ui/components/colorpicker/package-summary.html">net.runelite.client.ui.components.colorpicker</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.colorpicker.ColorPanel">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/colorpicker/ColorPanel.html" title="class in net.runelite.client.ui.components.colorpicker">net.runelite.client.ui.components.colorpicker.ColorPanel</a></h3>
<div class="type-signature">class ColorPanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>forceRedraw</h5>
<pre>boolean forceRedraw</pre>
</li>
<li class="block-list">
<h5>image</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/image/BufferedImage.html" title="class or interface in java.awt.image" class="external-link">BufferedImage</a> image</pre>
</li>
<li class="block-list">
<h5>onColorChange</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="class or interface in java.util.function" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a>&gt; onColorChange</pre>
</li>
<li class="block-list">
<h5>selectedY</h5>
<pre>int selectedY</pre>
</li>
<li class="block-list">
<h5>size</h5>
<pre>int size</pre>
</li>
<li class="block-list">
<h5>targetPosition</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Point.html" title="class or interface in java.awt" class="external-link">Point</a> targetPosition</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.colorpicker.ColorValuePanel">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/colorpicker/ColorValuePanel.html" title="class in net.runelite.client.ui.components.colorpicker">net.runelite.client.ui.components.colorpicker.ColorValuePanel</a></h3>
<div class="type-signature">class ColorValuePanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>input</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JTextField.html" title="class or interface in javax.swing" class="external-link">JTextField</a> input</pre>
</li>
<li class="block-list">
<h5>onValueChanged</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="class or interface in java.util.function" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt; onValueChanged</pre>
</li>
<li class="block-list">
<h5>slider</h5>
<pre><a href="net/runelite/client/ui/components/colorpicker/ColorValueSlider.html" title="class in net.runelite.client.ui.components.colorpicker">ColorValueSlider</a> slider</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.colorpicker.ColorValueSlider">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/colorpicker/ColorValueSlider.html" title="class in net.runelite.client.ui.components.colorpicker">net.runelite.client.ui.components.colorpicker.ColorValueSlider</a></h3>
<div class="type-signature">class ColorValueSlider extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>onValueChanged</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="class or interface in java.util.function" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt; onValueChanged</pre>
</li>
<li class="block-list">
<h5>value</h5>
<pre>int value</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.colorpicker.HuePanel">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/colorpicker/HuePanel.html" title="class in net.runelite.client.ui.components.colorpicker">net.runelite.client.ui.components.colorpicker.HuePanel</a></h3>
<div class="type-signature">class HuePanel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>height</h5>
<pre>int height</pre>
</li>
<li class="block-list">
<h5>onColorChange</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="class or interface in java.util.function" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt; onColorChange</pre>
</li>
<li class="block-list">
<h5>selectedY</h5>
<pre>int selectedY</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.colorpicker.RuneliteColorPicker">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/colorpicker/RuneliteColorPicker.html" title="class in net.runelite.client.ui.components.colorpicker">net.runelite.client.ui.components.colorpicker.RuneliteColorPicker</a></h3>
<div class="type-signature">class RuneliteColorPicker extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JDialog.html" title="class or interface in javax.swing" class="external-link">JDialog</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>afterPanel</h5>
<pre>net.runelite.client.ui.components.colorpicker.PreviewPanel afterPanel</pre>
</li>
<li class="block-list">
<h5>alphaHidden</h5>
<pre>boolean alphaHidden</pre>
</li>
<li class="block-list">
<h5>alphaSlider</h5>
<pre><a href="net/runelite/client/ui/components/colorpicker/ColorValuePanel.html" title="class in net.runelite.client.ui.components.colorpicker">ColorValuePanel</a> alphaSlider</pre>
</li>
<li class="block-list">
<h5>blueSlider</h5>
<pre><a href="net/runelite/client/ui/components/colorpicker/ColorValuePanel.html" title="class in net.runelite.client.ui.components.colorpicker">ColorValuePanel</a> blueSlider</pre>
</li>
<li class="block-list">
<h5>colorPanel</h5>
<pre><a href="net/runelite/client/ui/components/colorpicker/ColorPanel.html" title="class in net.runelite.client.ui.components.colorpicker">ColorPanel</a> colorPanel</pre>
</li>
<li class="block-list">
<h5>greenSlider</h5>
<pre><a href="net/runelite/client/ui/components/colorpicker/ColorValuePanel.html" title="class in net.runelite.client.ui.components.colorpicker">ColorValuePanel</a> greenSlider</pre>
</li>
<li class="block-list">
<h5>hexInput</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JTextField.html" title="class or interface in javax.swing" class="external-link">JTextField</a> hexInput</pre>
</li>
<li class="block-list">
<h5>huePanel</h5>
<pre><a href="net/runelite/client/ui/components/colorpicker/HuePanel.html" title="class in net.runelite.client.ui.components.colorpicker">HuePanel</a> huePanel</pre>
</li>
<li class="block-list">
<h5>onClose</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="class or interface in java.util.function" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a>&gt; onClose</pre>
</li>
<li class="block-list">
<h5>onColorChange</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="class or interface in java.util.function" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a>&gt; onColorChange</pre>
</li>
<li class="block-list">
<h5>redSlider</h5>
<pre><a href="net/runelite/client/ui/components/colorpicker/ColorValuePanel.html" title="class in net.runelite.client.ui.components.colorpicker">ColorValuePanel</a> redSlider</pre>
</li>
<li class="block-list">
<h5>selectedColor</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a> selectedColor</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/ui/components/materialtabs/package-summary.html">net.runelite.client.ui.components.materialtabs</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.materialtabs.MaterialTab">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/materialtabs/MaterialTab.html" title="class in net.runelite.client.ui.components.materialtabs">net.runelite.client.ui.components.materialtabs.MaterialTab</a></h3>
<div class="type-signature">class MaterialTab extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>content</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JComponent.html" title="class or interface in javax.swing" class="external-link">JComponent</a> content</pre>
</li>
<li class="block-list">
<h5>onSelectEvent</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/BooleanSupplier.html" title="class or interface in java.util.function" class="external-link">BooleanSupplier</a> onSelectEvent</pre>
</li>
<li class="block-list">
<h5>selected</h5>
<pre>boolean selected</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.materialtabs.MaterialTabGroup">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/materialtabs/MaterialTabGroup.html" title="class in net.runelite.client.ui.components.materialtabs">net.runelite.client.ui.components.materialtabs.MaterialTabGroup</a></h3>
<div class="type-signature">class MaterialTabGroup extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>display</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JPanel.html" title="class or interface in javax.swing" class="external-link">JPanel</a> display</pre>
</li>
<li class="block-list">
<h5>tabs</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/runelite/client/ui/components/materialtabs/MaterialTab.html" title="class in net.runelite.client.ui.components.materialtabs">MaterialTab</a>&gt; tabs</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/ui/components/shadowlabel/package-summary.html">net.runelite.client.ui.components.shadowlabel</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.components.shadowlabel.JShadowedLabel">
<h3>Class&nbsp;<a href="net/runelite/client/ui/components/shadowlabel/JShadowedLabel.html" title="class in net.runelite.client.ui.components.shadowlabel">net.runelite.client.ui.components.shadowlabel.JShadowedLabel</a></h3>
<div class="type-signature">class JShadowedLabel extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/javax/swing/JLabel.html" title="class or interface in javax.swing" class="external-link">JLabel</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>shadow</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Color.html" title="class or interface in java.awt" class="external-link">Color</a> shadow</pre>
</li>
<li class="block-list">
<h5>shadowSize</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.desktop/java/awt/Point.html" title="class or interface in java.awt" class="external-link">Point</a> shadowSize</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/ui/laf/package-summary.html">net.runelite.client.ui.laf</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.ui.laf.RuneLiteLAF">
<h3>Class&nbsp;<a href="net/runelite/client/ui/laf/RuneLiteLAF.html" title="class in net.runelite.client.ui.laf">net.runelite.client.ui.laf.RuneLiteLAF</a></h3>
<div class="type-signature">class RuneLiteLAF extends com.formdev.flatlaf.FlatDarkLaf implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/runelite/client/util/package-summary.html">net.runelite.client.util</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.runelite.client.util.VerificationException">
<h3>Exception&nbsp;<a href="net/runelite/client/util/VerificationException.html" title="class in net.runelite.client.util">net.runelite.client.util.VerificationException</a></h3>
<div class="type-signature">class VerificationException extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2014&#x2013;1970. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
