<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE RSyntaxTheme SYSTEM "theme.dtd">

<RSyntaxTheme version="1.0">
	<!--  General editor colors. -->
	<background color="2B2B2B"/>
	<caret color="BBBBBB"/>
	<selection useFG="false" bg="214283" roundedEdges="false"/>
	<currentLineHighlight color="323232" fade="false"/>
	<marginLine fg="394448"/>
	<markAllHighlight color="155221"/>
	<markOccurrencesHighlight color="32593D" border="true"/>
	<matchedBracket fg="A9B7C6" bg="2E2E2E" highlightBoth="true" animate="true"/><!--ij draws a border-->
	<hyperlinks fg="287BDE"/>
	<secondaryLanguages>
		<language index="1" bg="333344"/>
		<language index="2" bg="223322"/>
		<language index="3" bg="332222"/>
	</secondaryLanguages>

	<!-- Gutter styling. -->
	<gutterBorder color="606366"/>
	<lineNumbers fg="606366"/>
	<foldIndicator fg="6A8088" iconBg="2f383c" iconArmedBg="3f484c"/>
	<iconRowHeader activeLineRange="878787"/>

	<!-- Syntax tokens. -->
	<tokenStyles>
		<style token="IDENTIFIER" fg="A9B7C6"/>
		<style token="RESERVED_WORD" fg="CC7832" bold="false"/>
		<style token="RESERVED_WORD_2" fg="CC7832" bold="false"/>
		<style token="ANNOTATION" fg="BBB529"/>
		<style token="COMMENT_DOCUMENTATION" fg="629755"/>
		<style token="COMMENT_EOL" fg="808080"/>
		<style token="COMMENT_MULTILINE" fg="808080"/>
		<style token="COMMENT_KEYWORD" fg="629755"/>
		<style token="COMMENT_MARKUP" fg="77B767"/>
		<style token="FUNCTION" fg="A9B7C6"/><!-- any identifier magically known -->
		<style token="DATA_TYPE" fg="CC7832" bold="false"/>
		<style token="LITERAL_BOOLEAN" fg="CC7832" bold="false"/>
		<style token="LITERAL_NUMBER_DECIMAL_INT" fg="6897BB"/>
		<style token="LITERAL_NUMBER_FLOAT" fg="6897BB"/>
		<style token="LITERAL_NUMBER_HEXADECIMAL" fg="6897BB"/>
		<style token="LITERAL_STRING_DOUBLE_QUOTE" fg="6A8759"/>
		<style token="LITERAL_CHAR" fg="6A8759"/>
		<style token="LITERAL_BACKQUOTE" fg="6A8759"/>

		<!-- all wrong but nobody will write xml in this -->
		<style token="MARKUP_TAG_DELIMITER" fg="F92672"/>
		<style token="MARKUP_TAG_NAME" fg="ABBFD3" bold="true"/>
		<style token="MARKUP_TAG_ATTRIBUTE" fg="B3B689"/>
		<style token="MARKUP_TAG_ATTRIBUTE_VALUE" fg="e1e2cf"/>
		<style token="MARKUP_COMMENT" fg="878787"/>
		<style token="MARKUP_DTD" fg="A082BD"/>
		<style token="MARKUP_PROCESSING_INSTRUCTION" fg="A082BD"/>
		<style token="MARKUP_CDATA" fg="d5e6f0"/>
		<style token="MARKUP_CDATA_DELIMITER" fg="FD971F"/>
		<style token="MARKUP_ENTITY_REFERENCE" fg="F92672"/>

		<style token="OPERATOR" fg="A9B7C6"/>
		<style token="PREPROCESSOR" fg="A082BD"/>
		<style token="REGEX" fg="6A8759"/>
		<style token="SEPARATOR" fg="A9B7C6"/>
		<style token="VARIABLE" fg="A9B7C6" bold="false"/>
		<style token="WHITESPACE" fg="606060"/>

		<style token="ERROR_IDENTIFIER" fg="F9F9F9" bg="d82323"/>
		<style token="ERROR_NUMBER_FORMAT" fg="F9F9F9" bg="d82323"/>
		<style token="ERROR_STRING_DOUBLE" fg="F9F9F9" bg="d82323"/>
		<style token="ERROR_CHAR" fg="F9F9F9" bg="d82323"/>
	</tokenStyles>

</RSyntaxTheme>
