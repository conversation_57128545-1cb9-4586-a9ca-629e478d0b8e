/*
 * Copyright (c) 2020, <PERSON> <<PERSON>@sigterm.info>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.api;

/**
 * Jagex key codes
 */
public final class KeyCode
{
	public static final int KC_F1 = 1;
	public static final int KC_F2 = 2;
	public static final int KC_F3 = 3;
	public static final int KC_F4 = 4;
	public static final int KC_F5 = 5;
	public static final int KC_F6 = 6;
	public static final int KC_F7 = 7;
	public static final int KC_F8 = 8;
	public static final int KC_F9 = 9;
	public static final int KC_F10 = 10;
	public static final int KC_F11 = 11;
	public static final int KC_F12 = 12;
	public static final int KC_ESCAPE = 13;
	public static final int KC_1 = 16;
	public static final int KC_2 = 17;
	public static final int KC_3 = 18;
	public static final int KC_4 = 19;
	public static final int KC_5 = 20;
	public static final int KC_6 = 21;
	public static final int KC_7 = 22;
	public static final int KC_8 = 23;
	public static final int KC_9 = 24;
	public static final int KC_0 = 25;
	public static final int KC_MINUS = 26;
	public static final int KC_EQUALS = 27;
	public static final int KC_BACK_QUOTE = 28;
	public static final int KC_Q = 32;
	public static final int KC_W = 33;
	public static final int KC_E = 34;
	public static final int KC_R = 35;
	public static final int KC_T = 36;
	public static final int KC_Y = 37;
	public static final int KC_U = 38;
	public static final int KC_I = 39;
	public static final int KC_O = 40;
	public static final int KC_P = 41;
	public static final int KC_OPEN_BRACKET = 42;
	public static final int KC_CLOSE_BRACKET = 43;
	public static final int KC_A = 48;
	public static final int KC_S = 49;
	public static final int KC_D = 50;
	public static final int KC_F = 51;
	public static final int KC_G = 52;
	public static final int KC_H = 53;
	public static final int KC_J = 54;
	public static final int KC_K = 55;
	public static final int KC_L = 56;
	public static final int KC_SEMICOLON = 57;
	public static final int KC_QUOTE = 58;
	public static final int KC_NUMBER_SIGN = 59;
	public static final int KC_Z = 64;
	public static final int KC_X = 65;
	public static final int KC_C = 66;
	public static final int KC_V = 67;
	public static final int KC_B = 68;
	public static final int KC_N = 69;
	public static final int KC_M = 70;
	public static final int KC_COMMA = 71;
	public static final int KC_PERIOD = 72;
	public static final int KC_SLASH = 73;
	public static final int KC_BACK_SLASH = 74;
	public static final int KC_TAB = 80;
	public static final int KC_SHIFT = 81;
	public static final int KC_CONTROL = 82;
	public static final int KC_SPACE = 83;
	public static final int KC_ENTER = 84;
	public static final int KC_BACK_SPACE = 85;
	public static final int KC_ALT = 86;
	public static final int KC_ADD = 87;
	public static final int KC_SUBTRACT = 88;
	public static final int KC_MULTIPLY = 89;
	public static final int KC_DIVIDE = 90;
	public static final int KC_CLEAR = 91;
	public static final int KC_LEFT = 96;
	public static final int KC_RIGHT = 97;
	public static final int KC_UP = 98;
	public static final int KC_DOWN = 99;
	public static final int KC_INSERT = 100;
	public static final int KC_DELETE = 101;
	public static final int KC_HOME = 102;
	public static final int KC_END = 103;
	public static final int KC_PAGE_UP = 104;
	public static final int KC_PAGE_DOWN = 105;
	public static final int KC_NUMPAD5 = 219;
	public static final int KC_NUMPAD4 = 224;
	public static final int KC_NUMPAD6 = 225;
	public static final int KC_NUMPAD8 = 226;
	public static final int KC_NUMPAD2 = 227;
	public static final int KC_NUMPAD0 = 228;
	public static final int KC_DECIMAL = 229;
	public static final int KC_NUMPAD7 = 230;
	public static final int KC_NUMPAD1 = 231;
	public static final int KC_NUMPAD9 = 232;
	public static final int KC_NUMPAD3 = 233;
}
