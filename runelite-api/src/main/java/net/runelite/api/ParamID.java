/*
 * Copyright (c) 2020 Abex
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF ME<PERSON><PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.api;

/**
 * @see ParamHolder
 */
public final class ParamID
{
	public static final int OC_ITEM_OP1 = 451;
	public static final int OC_ITEM_OP2 = 452;
	public static final int OC_ITEM_OP3 = 453;
	public static final int OC_ITEM_OP4 = 454;
	public static final int OC_ITEM_OP5 = 455;
	public static final int OC_ITEM_OP6 = 456;
	public static final int OC_ITEM_OP7 = 457;
	public static final int OC_ITEM_OP8 = 458;

	public static final int OC_ITEM_OP1_SUBOP1 = 661;
	public static final int OC_ITEM_OP1_SUBOP2 = 2074;
	public static final int OC_ITEM_OP1_SUBOP3 = 2082;
	public static final int OC_ITEM_OP1_SUBOP4 = 2090;
	public static final int OC_ITEM_OP1_SUBOP5 = 2098;
	public static final int OC_ITEM_OP1_SUBOP6 = 2106;
	public static final int OC_ITEM_OP1_SUBOP7 = 2114;
	public static final int OC_ITEM_OP1_SUBOP8 = 2122;
	public static final int OC_ITEM_OP1_SUBOP9 = 2130;
	public static final int OC_ITEM_OP1_SUBOP10 = 2138;
	public static final int OC_ITEM_OP1_SUBOP11 = 2146;
	public static final int OC_ITEM_OP1_SUBOP12 = 2154;
	public static final int OC_ITEM_OP1_SUBOP13 = 2162;
	public static final int OC_ITEM_OP1_SUBOP14 = 2170;
	public static final int OC_ITEM_OP1_SUBOP15 = 2178;
	public static final int OC_ITEM_OP1_SUBOP16 = 2186;
	public static final int OC_ITEM_OP1_SUBOP17 = 2194;
	public static final int OC_ITEM_OP1_SUBOP18 = 2202;
	public static final int OC_ITEM_OP1_SUBOP19 = 2210;
	public static final int OC_ITEM_OP1_SUBOP20 = 2218;
	public static final int OC_ITEM_OP2_SUBOP1 = 662;
	public static final int OC_ITEM_OP2_SUBOP2 = 2075;
	public static final int OC_ITEM_OP2_SUBOP3 = 2083;
	public static final int OC_ITEM_OP2_SUBOP4 = 2091;
	public static final int OC_ITEM_OP2_SUBOP5 = 2099;
	public static final int OC_ITEM_OP2_SUBOP6 = 2107;
	public static final int OC_ITEM_OP2_SUBOP7 = 2115;
	public static final int OC_ITEM_OP2_SUBOP8 = 2123;
	public static final int OC_ITEM_OP2_SUBOP9 = 2131;
	public static final int OC_ITEM_OP2_SUBOP10 = 2139;
	public static final int OC_ITEM_OP2_SUBOP11 = 2147;
	public static final int OC_ITEM_OP2_SUBOP12 = 2155;
	public static final int OC_ITEM_OP2_SUBOP13 = 2163;
	public static final int OC_ITEM_OP2_SUBOP14 = 2171;
	public static final int OC_ITEM_OP2_SUBOP15 = 2179;
	public static final int OC_ITEM_OP2_SUBOP16 = 2187;
	public static final int OC_ITEM_OP2_SUBOP17 = 2195;
	public static final int OC_ITEM_OP2_SUBOP18 = 2203;
	public static final int OC_ITEM_OP2_SUBOP19 = 2211;
	public static final int OC_ITEM_OP2_SUBOP20 = 2219;
	public static final int OC_ITEM_OP3_SUBOP1 = 663;
	public static final int OC_ITEM_OP3_SUBOP2 = 2076;
	public static final int OC_ITEM_OP3_SUBOP3 = 2084;
	public static final int OC_ITEM_OP3_SUBOP4 = 2092;
	public static final int OC_ITEM_OP3_SUBOP5 = 2100;
	public static final int OC_ITEM_OP3_SUBOP6 = 2108;
	public static final int OC_ITEM_OP3_SUBOP7 = 2116;
	public static final int OC_ITEM_OP3_SUBOP8 = 2124;
	public static final int OC_ITEM_OP3_SUBOP9 = 2132;
	public static final int OC_ITEM_OP3_SUBOP10 = 2140;
	public static final int OC_ITEM_OP3_SUBOP11 = 2148;
	public static final int OC_ITEM_OP3_SUBOP12 = 2156;
	public static final int OC_ITEM_OP3_SUBOP13 = 2164;
	public static final int OC_ITEM_OP3_SUBOP14 = 2172;
	public static final int OC_ITEM_OP3_SUBOP15 = 2180;
	public static final int OC_ITEM_OP3_SUBOP16 = 2188;
	public static final int OC_ITEM_OP3_SUBOP17 = 2196;
	public static final int OC_ITEM_OP3_SUBOP18 = 2204;
	public static final int OC_ITEM_OP3_SUBOP19 = 2212;
	public static final int OC_ITEM_OP3_SUBOP20 = 2220;
	public static final int OC_ITEM_OP4_SUBOP1 = 2069;
	public static final int OC_ITEM_OP4_SUBOP2 = 2077;
	public static final int OC_ITEM_OP4_SUBOP3 = 2085;
	public static final int OC_ITEM_OP4_SUBOP4 = 2093;
	public static final int OC_ITEM_OP4_SUBOP5 = 2101;
	public static final int OC_ITEM_OP4_SUBOP6 = 2109;
	public static final int OC_ITEM_OP4_SUBOP7 = 2117;
	public static final int OC_ITEM_OP4_SUBOP8 = 2125;
	public static final int OC_ITEM_OP4_SUBOP9 = 2133;
	public static final int OC_ITEM_OP4_SUBOP10 = 2141;
	public static final int OC_ITEM_OP4_SUBOP11 = 2149;
	public static final int OC_ITEM_OP4_SUBOP12 = 2157;
	public static final int OC_ITEM_OP4_SUBOP13 = 2165;
	public static final int OC_ITEM_OP4_SUBOP14 = 2173;
	public static final int OC_ITEM_OP4_SUBOP15 = 2181;
	public static final int OC_ITEM_OP4_SUBOP16 = 2189;
	public static final int OC_ITEM_OP4_SUBOP17 = 2197;
	public static final int OC_ITEM_OP4_SUBOP18 = 2205;
	public static final int OC_ITEM_OP4_SUBOP19 = 2213;
	public static final int OC_ITEM_OP4_SUBOP20 = 2221;
	public static final int OC_ITEM_OP5_SUBOP1 = 2070;
	public static final int OC_ITEM_OP5_SUBOP2 = 2078;
	public static final int OC_ITEM_OP5_SUBOP3 = 2086;
	public static final int OC_ITEM_OP5_SUBOP4 = 2094;
	public static final int OC_ITEM_OP5_SUBOP5 = 2102;
	public static final int OC_ITEM_OP5_SUBOP6 = 2110;
	public static final int OC_ITEM_OP5_SUBOP7 = 2118;
	public static final int OC_ITEM_OP5_SUBOP8 = 2126;
	public static final int OC_ITEM_OP5_SUBOP9 = 2134;
	public static final int OC_ITEM_OP5_SUBOP10 = 2142;
	public static final int OC_ITEM_OP5_SUBOP11 = 2150;
	public static final int OC_ITEM_OP5_SUBOP12 = 2158;
	public static final int OC_ITEM_OP5_SUBOP13 = 2166;
	public static final int OC_ITEM_OP5_SUBOP14 = 2174;
	public static final int OC_ITEM_OP5_SUBOP15 = 2182;
	public static final int OC_ITEM_OP5_SUBOP16 = 2190;
	public static final int OC_ITEM_OP5_SUBOP17 = 2198;
	public static final int OC_ITEM_OP5_SUBOP18 = 2206;
	public static final int OC_ITEM_OP5_SUBOP19 = 2214;
	public static final int OC_ITEM_OP5_SUBOP20 = 2222;
	public static final int OC_ITEM_OP6_SUBOP1 = 2071;
	public static final int OC_ITEM_OP6_SUBOP2 = 2079;
	public static final int OC_ITEM_OP6_SUBOP3 = 2087;
	public static final int OC_ITEM_OP6_SUBOP4 = 2095;
	public static final int OC_ITEM_OP6_SUBOP5 = 2103;
	public static final int OC_ITEM_OP6_SUBOP6 = 2111;
	public static final int OC_ITEM_OP6_SUBOP7 = 2119;
	public static final int OC_ITEM_OP6_SUBOP8 = 2127;
	public static final int OC_ITEM_OP6_SUBOP9 = 2135;
	public static final int OC_ITEM_OP6_SUBOP10 = 2143;
	public static final int OC_ITEM_OP6_SUBOP11 = 2151;
	public static final int OC_ITEM_OP6_SUBOP12 = 2159;
	public static final int OC_ITEM_OP6_SUBOP13 = 2167;
	public static final int OC_ITEM_OP6_SUBOP14 = 2175;
	public static final int OC_ITEM_OP6_SUBOP15 = 2183;
	public static final int OC_ITEM_OP6_SUBOP16 = 2191;
	public static final int OC_ITEM_OP6_SUBOP17 = 2199;
	public static final int OC_ITEM_OP6_SUBOP18 = 2207;
	public static final int OC_ITEM_OP6_SUBOP19 = 2215;
	public static final int OC_ITEM_OP6_SUBOP20 = 2223;
	public static final int OC_ITEM_OP7_SUBOP1 = 2072;
	public static final int OC_ITEM_OP7_SUBOP2 = 2080;
	public static final int OC_ITEM_OP7_SUBOP3 = 2088;
	public static final int OC_ITEM_OP7_SUBOP4 = 2096;
	public static final int OC_ITEM_OP7_SUBOP5 = 2104;
	public static final int OC_ITEM_OP7_SUBOP6 = 2112;
	public static final int OC_ITEM_OP7_SUBOP7 = 2120;
	public static final int OC_ITEM_OP7_SUBOP8 = 2128;
	public static final int OC_ITEM_OP7_SUBOP9 = 2136;
	public static final int OC_ITEM_OP7_SUBOP10 = 2144;
	public static final int OC_ITEM_OP7_SUBOP11 = 2152;
	public static final int OC_ITEM_OP7_SUBOP12 = 2160;
	public static final int OC_ITEM_OP7_SUBOP13 = 2168;
	public static final int OC_ITEM_OP7_SUBOP14 = 2176;
	public static final int OC_ITEM_OP7_SUBOP15 = 2184;
	public static final int OC_ITEM_OP7_SUBOP16 = 2192;
	public static final int OC_ITEM_OP7_SUBOP17 = 2200;
	public static final int OC_ITEM_OP7_SUBOP18 = 2208;
	public static final int OC_ITEM_OP7_SUBOP19 = 2216;
	public static final int OC_ITEM_OP7_SUBOP20 = 2224;
	public static final int OC_ITEM_OP8_SUBOP1 = 2073;
	public static final int OC_ITEM_OP8_SUBOP2 = 2081;
	public static final int OC_ITEM_OP8_SUBOP3 = 2089;
	public static final int OC_ITEM_OP8_SUBOP4 = 2097;
	public static final int OC_ITEM_OP8_SUBOP5 = 2105;
	public static final int OC_ITEM_OP8_SUBOP6 = 2113;
	public static final int OC_ITEM_OP8_SUBOP7 = 2121;
	public static final int OC_ITEM_OP8_SUBOP8 = 2129;
	public static final int OC_ITEM_OP8_SUBOP9 = 2137;
	public static final int OC_ITEM_OP8_SUBOP10 = 2145;
	public static final int OC_ITEM_OP8_SUBOP11 = 2153;
	public static final int OC_ITEM_OP8_SUBOP12 = 2161;
	public static final int OC_ITEM_OP8_SUBOP13 = 2169;
	public static final int OC_ITEM_OP8_SUBOP14 = 2177;
	public static final int OC_ITEM_OP8_SUBOP15 = 2185;
	public static final int OC_ITEM_OP8_SUBOP16 = 2193;
	public static final int OC_ITEM_OP8_SUBOP17 = 2201;
	public static final int OC_ITEM_OP8_SUBOP18 = 2209;
	public static final int OC_ITEM_OP8_SUBOP19 = 2217;
	public static final int OC_ITEM_OP8_SUBOP20 = 2225;

	/**
	 * Long name for NPCs used in the HP hud
	 */
	public static final int NPC_HP_NAME = 510;
	/**
	 * @see SettingID
	 */
	public static final int SETTING_ID = 1077;
	// defaults to 5
	// 1 is continuous
	public static final int SETTING_SLIDER_STEPS = 1101;
	public static final int SETTING_CUSTOM_TRANSMIT = 1085;
	// defaults to true
	// track is foreground
	public static final int SETTING_FOREGROUND_CLICKZONE = 1105;
	public static final int SETTING_SLIDER_CUSTOM_ONOP = 1106;
	public static final int SETTING_SLIDER_CUSTOM_SETPOS = 1107;
	public static final int SETTING_SLIDER_IS_DRAGGABLE = 1108;
	public static final int SETTING_SLIDER_DEADZONE = 1109;
	public static final int SETTING_SLIDER_DEADTIME = 1110;

	public static final int OC_PRAYER_COMPONENT = 1751;
	public static final int OC_PRAYER_LEVEL = 1753;

	public static final int NPC_DEATH_HIDER_EXCLUDE = 1799;

	public static final int SLAYER_TASK_NAME = 1801;

	public static final int ATTACK_STYLE_NAME = 1407;

	public static final int SPELL_BUTTON = 596;
	public static final int SPELL_NAME = 601;
	public static final int SPELL_LEVELREQ = 604;

	public static final int BANK_AUTOCHARGE = 2257;

	public static final int CLUE_SCROLL = 623;
}
