/*
 * Copyright (c) 2018 Abex
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF ME<PERSON><PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.api;

/**
 * IDs of fonts in the cache
 */
public final class FontID
{
	public static final int PLAIN_11 = 494;
	public static final int PLAIN_12 = 495;

	public static final int BOLD_12 = 496;

	public static final int QUILL_8 = 497;
	public static final int QUILL_MEDIUM = 645;
	public static final int QUILL_CAPS_LARGE = 646;

	public static final int FAIRY_SMALL = 647;
	public static final int FAIRY_LARGE = 648;

	public static final int BARBARIAN = 764;

	public static final int SUROK = 819;

	public static final int VERDANA_11 = 1442;
	public static final int VERDANA_11_BOLD = 1443;

	public static final int TAHOMA_11 = 1444;

	public static final int VERDANA_13 = 1445;
	public static final int VERDANA_13_BOLD = 1446;

	public static final int VERDANA_15 = 1447;
}
