/*
 * Copyright (c) 2017. l2-
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *     list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *     this list of conditions and the following disclaimer in the documentation
 *     and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

package net.runelite.api.events;

import lombok.Data;
import net.runelite.api.annotations.Varbit;
import net.runelite.api.annotations.Varp;

/**
 * An event when a varbit or varplayer has changed.
 *
 * If the client preemptively changes a varp and the server agrees
 * the next tick, VarbitChanged will only be posted when the client
 * changes the value, not the server. This can cause unintended effects
 * if the VarPlayer has special engine behavior assigned to it.
 */
@Data
public class VarbitChanged
{
	/**
	 * The id of the varp that was changed.
	 * For a varplayer, this is the varplayer id.
	 */
	@Varp
	private int varpId = -1;

	/**
	 * The id of the varbit that was changed.
	 * For a varplayer, this is -1.
	 */
	@Varbit
	private int varbitId = -1;

	/**
	 * The new value of the varp or varbit
	 */
	private int value;

	@Deprecated
	public int getIndex()
	{
		return varpId;
	}
}