/*
 * Copyright (c) 2017, <PERSON> <<PERSON>@sigterm.info>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.api.widgets;

import net.runelite.api.gameval.InterfaceID;

/**
 * Represents a group-child {@link Widget} relationship.
 * <p>
 * For getting a specific widget from the client, see {@link net.runelite.api.Client#getWidget(WidgetInfo)}.
 */
@Deprecated
public enum WidgetInfo
{
	FAIRY_RING_TELEPORT_BUTTON(InterfaceID.Fairyrings.CONFIRM),

	WORLD_SWITCHER_BUTTON(InterfaceID.Logout.WORLD_SWITCHER),
	LOGOUT_BUTTON(InterfaceID.Logout.WORLD_SWITCHER_GRAPHIC2),

	INVENTORY(WidgetID.INVENTORY_GROUP_ID, 0),
	FRIENDS_LIST(WidgetID.FRIENDS_LIST_GROUP_ID, 0),
	IGNORE_LIST(WidgetID.IGNORE_LIST_GROUP_ID, 0),
	FRIENDS_CHAT(WidgetID.FRIENDS_CHAT_GROUP_ID, 0),
	RAIDING_PARTY(WidgetID.RAIDING_PARTY_GROUP_ID, 0),

	WORLD_MAP_VIEW(InterfaceID.Worldmap.MAP_CONTAINER),
	WORLD_MAP_OVERVIEW_MAP(InterfaceID.Worldmap.OVERVIEW_CONTAINER),
	WORLD_MAP_BOTTOM_BAR(InterfaceID.Worldmap.BOTTOM_GRAPHIC0),
	WORLD_MAP_SEARCH(InterfaceID.Worldmap.MAPLIST_DISPLAY),
	WORLD_MAP_SURFACE_SELECTOR(InterfaceID.Worldmap.MAPLIST_BOX_GRAPHIC0),
	WORLD_MAP_TOOLTIP(InterfaceID.Worldmap.TOOLTIP),

	CLUE_SCROLL_TEXT(InterfaceID.TrailCluetext.TEXT),
	CLUE_SCROLL_REWARD_ITEM_CONTAINER(InterfaceID.TrailRewardscreen.ITEMS),

	EQUIPMENT(WidgetID.EQUIPMENT_GROUP_ID, 0),
	EQUIPMENT_INVENTORY_ITEMS_CONTAINER(InterfaceID.Wornitems.UNIVERSE),

	EMOTE_WINDOW(InterfaceID.Emote.UNIVERSE),
	EMOTE_SCROLL_CONTAINER(InterfaceID.Emote.SCROLLABLE),
	EMOTE_CONTAINER(InterfaceID.Emote.CONTENTS),
	EMOTE_SCROLLBAR(InterfaceID.Emote.SCROLLBAR),

	MUSIC_WINDOW(InterfaceID.Music.UNIVERSE),
	MUSIC_TRACK_LIST(InterfaceID.Music.JUKEBOX),
	MUSIC_TRACK_SCROLL_CONTAINER(InterfaceID.Music.SCROLLABLE),
	MUSIC_TRACK_SCROLLBAR(InterfaceID.Music.SCROLLBAR),

	DIARY_QUEST_WIDGET_TITLE(InterfaceID.Questjournal.TITLE),
	DIARY_QUEST_WIDGET_TEXT(InterfaceID.Questjournal.TEXTLAYER),

	ACHIEVEMENT_DIARY_SCROLL_TITLE(InterfaceID.Journalscroll.TITLE),
	ACHIEVEMENT_DIARY_SCROLL_TEXT(InterfaceID.Journalscroll.TEXTLAYER),

	PEST_CONTROL_BOAT_INFO(WidgetID.PEST_CONTROL_BOAT_GROUP_ID, 2),
	PEST_CONTROL_KNIGHT_INFO_CONTAINER(InterfaceID.PestStatusOverlay.CONTENT_LEFT),
	PEST_CONTROL_ACTIVITY_SHIELD_INFO_CONTAINER(InterfaceID.PestStatusOverlay.CONTENT_RIGHT),
	PEST_CONTROL_PURPLE_SHIELD(InterfaceID.PestStatusOverlay.PEST_STATUS_PORT1),
	PEST_CONTROL_BLUE_SHIELD(InterfaceID.PestStatusOverlay.PEST_STATUS_PORT2),
	PEST_CONTROL_YELLOW_SHIELD(InterfaceID.PestStatusOverlay.PEST_STATUS_PORT3),
	PEST_CONTROL_RED_SHIELD(InterfaceID.PestStatusOverlay.PEST_STATUS_PORT4),
	PEST_CONTROL_PURPLE_HEALTH(InterfaceID.PestStatusOverlay.PEST_STATUS_PORTTXT1),
	PEST_CONTROL_BLUE_HEALTH(InterfaceID.PestStatusOverlay.PEST_STATUS_PORTTXT2),
	PEST_CONTROL_YELLOW_HEALTH(InterfaceID.PestStatusOverlay.PEST_STATUS_PORTTXT3),
	PEST_CONTROL_RED_HEALTH(InterfaceID.PestStatusOverlay.PEST_STATUS_PORTTXT4),
	PEST_CONTROL_PURPLE_ICON(InterfaceID.PestStatusOverlay.PEST_PORTAL_1_DONE),
	PEST_CONTROL_BLUE_ICON(InterfaceID.PestStatusOverlay.PEST_PORTAL_2_DONE),
	PEST_CONTROL_YELLOW_ICON(InterfaceID.PestStatusOverlay.PEST_PORTAL_3_DONE),
	PEST_CONTROL_RED_ICON(InterfaceID.PestStatusOverlay.PEST_PORTAL_4_DONE),
	PEST_CONTROL_ACTIVITY_BAR(InterfaceID.PestStatusOverlay.ACTIVITY_CONTAINER),
	PEST_CONTROL_ACTIVITY_PROGRESS(InterfaceID.PestStatusOverlay.ACTIVITY_BAR),

	VOLCANIC_MINE_TIME_LEFT(InterfaceID.FossilVolcanicMine.TIME_TITLE),
	VOLCANIC_MINE_POINTS(InterfaceID.FossilVolcanicMine.POINTS_TITLE),
	VOLCANIC_MINE_STABILITY(InterfaceID.FossilVolcanicMine.STABILITY_TITLE),
	VOLCANIC_MINE_PLAYER_COUNT(InterfaceID.FossilVolcanicMine.PLAYERS_TITLE),
	VOLCANIC_MINE_VENTS_INFOBOX_GROUP(InterfaceID.FossilVolcanicMine.VENT_CHAMBERS_INFO),
	VOLCANIC_MINE_STABILITY_INFOBOX_GROUP(InterfaceID.FossilVolcanicMine.MAIN_INFO),
	VOLCANIC_MINE_VENT_A_PERCENTAGE(InterfaceID.FossilVolcanicMine.VENTS),
	VOLCANIC_MINE_VENT_B_PERCENTAGE(InterfaceID.FossilVolcanicMine.VENT_A),
	VOLCANIC_MINE_VENT_C_PERCENTAGE(InterfaceID.FossilVolcanicMine.VENT_B),
	VOLCANIC_MINE_VENT_A_STATUS(InterfaceID.FossilVolcanicMine.CHAMBERS),
	VOLCANIC_MINE_VENT_B_STATUS(InterfaceID.FossilVolcanicMine.CHAMBER_A),
	VOLCANIC_MINE_VENT_C_STATUS(InterfaceID.FossilVolcanicMine.CHAMBER_B),

	FRIEND_CHAT_TITLE(InterfaceID.Friends.HEADER),
	FRIEND_LIST_FULL_CONTAINER(InterfaceID.Friends.LIST_CONTAINER),
	FRIEND_LIST_SORT_BY_NAME_BUTTON(InterfaceID.Friends.SORT_NAME),
	FRIEND_LIST_SORT_BY_LAST_WORLD_CHANGE_BUTTON(InterfaceID.Friends.SORT_RECENT),
	FRIEND_LIST_SORT_BY_WORLD_BUTTON(InterfaceID.Friends.SORT_WORLD),
	FRIEND_LIST_LEGACY_SORT_BUTTON(InterfaceID.Friends.SORT_LEGACY),
	FRIEND_LIST_NAMES_CONTAINER(InterfaceID.Friends.LIST),
	FRIEND_LIST_SCROLL_BAR(InterfaceID.Friends.SCROLLBAR),
	FRIEND_LIST_LOADING_TEXT(InterfaceID.Friends.LOADING),
	FRIEND_LIST_PREVIOUS_NAME_HOLDER(InterfaceID.Friends.TOOLTIP),

	IGNORE_TITLE(InterfaceID.Ignore.HEADER),
	IGNORE_FULL_CONTAINER(InterfaceID.Ignore.LIST_CONTAINER),
	IGNORE_SORT_BY_NAME_BUTTON(InterfaceID.Ignore.SORT_NAME),
	IGNORE_LEGACY_SORT_BUTTON(InterfaceID.Ignore.SORT_LEGACY),
	IGNORE_NAMES_CONTAINER(InterfaceID.Ignore.LIST),
	IGNORE_SCROLL_BAR(InterfaceID.Ignore.SCROLLBAR),
	IGNORE_LOADING_TEXT(InterfaceID.Ignore.LOADING),
	IGNORE_PREVIOUS_NAME_HOLDER(InterfaceID.Ignore.TOOLTIP),

	EXPLORERS_RING_ALCH_INVENTORY(InterfaceID.LumbridgeAlchemy.ITEMS),

	FRIENDS_CHAT_ROOT(InterfaceID.ChatchannelCurrent.UNIVERSE),
	FRIENDS_CHAT_TITLE(InterfaceID.ChatchannelCurrent.CHANNELNAME),
	FRIENDS_CHAT_OWNER(InterfaceID.ChatchannelCurrent.CHANNELOWNER),
	FRIENDS_CHAT_LIST(InterfaceID.ChatchannelCurrent.LIST),

	BANK_CONTAINER(InterfaceID.Bankmain.UNIVERSE),
	BANK_SEARCH_BUTTON_BACKGROUND(InterfaceID.Bankmain.SEARCH),
	BANK_ITEM_CONTAINER(InterfaceID.Bankmain.ITEMS),
	BANK_INVENTORY_ITEMS_CONTAINER(InterfaceID.Bankside.ITEMS),
	BANK_EQUIPMENT_INVENTORY_ITEMS_CONTAINER(InterfaceID.Bankside.WORNOPS),
	BANK_TITLE_BAR(InterfaceID.Bankmain.TITLE),
	BANK_INCINERATOR(InterfaceID.Bankmain.INCINERATOR_TARGET),
	BANK_INCINERATOR_CONFIRM(InterfaceID.Bankmain.INCINERATOR_CONFIRM),
	BANK_CONTENT_CONTAINER(InterfaceID.Bankmain.ITEMS_CONTAINER),
	BANK_DEPOSIT_EQUIPMENT(InterfaceID.Bankmain.DEPOSITWORN),
	BANK_DEPOSIT_INVENTORY(InterfaceID.Bankmain.DEPOSITINV),
	BANK_TAB_CONTAINER(InterfaceID.Bankmain.TABS),
	BANK_EQUIPMENT_CONTAINER(InterfaceID.Bankmain.WORNITEMS_CONTAINER),
	BANK_EQUIPMENT_BUTTON(InterfaceID.Bankmain.WORNITEMS_BUTTON),
	BANK_POPUP(InterfaceID.Bankmain.POPUP),
	BANK_ITEM_COUNT_TOP(InterfaceID.Bankmain.OCCUPIEDSLOTS),
	BANK_ITEM_COUNT_BAR(InterfaceID.Bankmain.UNIVERSE_LINE4),
	BANK_ITEM_COUNT_BOTTOM(InterfaceID.Bankmain.CAPACITY_LAYER),
	BANK_GROUP_STORAGE_BUTTON(InterfaceID.Bankmain.GIM_STORAGE),
	BANK_SCROLLBAR(InterfaceID.Bankmain.SCROLLBAR),
	BANK_PIN_CONTAINER(InterfaceID.BankpinKeypad.UNIVERSE),
	BANK_SETTINGS_BUTTON(InterfaceID.Bankmain.MENU_BUTTON),
	BANK_TUTORIAL_BUTTON(InterfaceID.Bankmain.BANK_TUT),

	GROUP_STORAGE_UI(InterfaceID.SharedBank.FRAME),
	GROUP_STORAGE_ITEM_CONTAINER(InterfaceID.SharedBank.ITEMS),

	GRAND_EXCHANGE_WINDOW_CONTAINER(InterfaceID.GeOffers.UNIVERSE),
	GRAND_EXCHANGE_OFFER_CONTAINER(InterfaceID.GeOffers.SETUP),
	GRAND_EXCHANGE_OFFER_TEXT(InterfaceID.GeOffers.SETUP_DESC),

	GRAND_EXCHANGE_INVENTORY_ITEMS_CONTAINER(InterfaceID.GeOffersSide.ITEMS),

	DEPOSIT_BOX_INVENTORY_ITEMS_CONTAINER(InterfaceID.BankDepositbox.INVENTORY),

	SHOP_INVENTORY_ITEMS_CONTAINER(InterfaceID.Shopmain.UNIVERSE),

	SMITHING_INVENTORY_ITEMS_CONTAINER(InterfaceID.Smithing.UNIVERSE),

	GUIDE_PRICES_ITEMS_CONTAINER(InterfaceID.GePricechecker.ITEMS),
	GUIDE_PRICES_INVENTORY_ITEMS_CONTAINER(InterfaceID.GePricechecker.UNIVERSE),

	RUNE_POUCH_ITEM_CONTAINER(WidgetID.RUNE_POUCH_GROUP_ID, 0),

	MINIMAP_ORBS(WidgetID.MINIMAP_GROUP_ID, 0),
	MINIMAP_XP_ORB(InterfaceID.Orbs.XP_DROPS),
	MINIMAP_PRAYER_ORB(InterfaceID.Orbs.ORB_PRAYER),
	MINIMAP_QUICK_PRAYER_ORB(InterfaceID.Orbs.PRAYERBUTTON),
	MINIMAP_PRAYER_ORB_TEXT(InterfaceID.Orbs.PRAYER_TEXT),
	MINIMAP_RUN_ORB(InterfaceID.Orbs.ORB_RUNENERGY),
	MINIMAP_TOGGLE_RUN_ORB(InterfaceID.Orbs.RUNBUTTON),
	MINIMAP_RUN_ORB_TEXT(InterfaceID.Orbs.RUNENERGY_TEXT),
	MINIMAP_HEALTH_ORB(InterfaceID.Orbs.ORB_HEALTH),
	MINIMAP_SPEC_ORB(InterfaceID.Orbs.ORB_SPECENERGY),
	MINIMAP_WORLDMAP_ORB(InterfaceID.Orbs.ORB_WORLDMAP),
	MINIMAP_WIKI_BANNER_PARENT(InterfaceID.Orbs.WIKI),
	MINIMAP_WIKI_BANNER(InterfaceID.Orbs.WIKI_ICON),
	MINIMAP_WORLDMAP_OPTIONS(InterfaceID.Orbs.WORLDMAP),

	LMS_INFO(InterfaceID.BrLobbyoverlay.CONTENT),
	LMS_KDA(InterfaceID.BrOverlay.CONTENT),

	LOGIN_CLICK_TO_PLAY_SCREEN(WidgetID.LOGIN_CLICK_TO_PLAY_GROUP_ID, 0),
	LOGIN_CLICK_TO_PLAY_SCREEN_MESSAGE_OF_THE_DAY(InterfaceID.WelcomeScreen.MOTW),

	FIXED_VIEWPORT(InterfaceID.Toplevel.OVERLAY_HUD),
	FIXED_VIEWPORT_ROOT_INTERFACE_CONTAINER(InterfaceID.Toplevel.SIDE),
	FIXED_VIEWPORT_BANK_CONTAINER(InterfaceID.Toplevel.SIDEMODAL),
	FIXED_VIEWPORT_INTERFACE_CONTAINER(InterfaceID.Toplevel.SIDE_PANELS),
	FIXED_VIEWPORT_INVENTORY_CONTAINER(InterfaceID.Toplevel.SIDE3),
	FIXED_VIEWPORT_COMBAT_TAB(InterfaceID.Toplevel.STONE0),
	FIXED_VIEWPORT_STATS_TAB(InterfaceID.Toplevel.STONE1),
	FIXED_VIEWPORT_QUESTS_TAB(InterfaceID.Toplevel.STONE2),
	FIXED_VIEWPORT_INVENTORY_TAB(InterfaceID.Toplevel.STONE3),
	FIXED_VIEWPORT_EQUIPMENT_TAB(InterfaceID.Toplevel.STONE4),
	FIXED_VIEWPORT_PRAYER_TAB(InterfaceID.Toplevel.STONE5),
	FIXED_VIEWPORT_MAGIC_TAB(InterfaceID.Toplevel.STONE6),
	FIXED_VIEWPORT_FRIENDS_CHAT_TAB(InterfaceID.Toplevel.STONE7),
	FIXED_VIEWPORT_FRIENDS_TAB(InterfaceID.Toplevel.STONE9),
	FIXED_VIEWPORT_IGNORES_TAB(InterfaceID.Toplevel.STONE8),
	FIXED_VIEWPORT_LOGOUT_TAB(InterfaceID.Toplevel.STONE10),
	FIXED_VIEWPORT_OPTIONS_TAB(InterfaceID.Toplevel.STONE11),
	FIXED_VIEWPORT_EMOTES_TAB(InterfaceID.Toplevel.STONE12),
	FIXED_VIEWPORT_MUSIC_TAB(InterfaceID.Toplevel.STONE13),
	FIXED_VIEWPORT_COMBAT_ICON(InterfaceID.Toplevel.ICON0),
	FIXED_VIEWPORT_STATS_ICON(InterfaceID.Toplevel.ICON1),
	FIXED_VIEWPORT_QUESTS_ICON(InterfaceID.Toplevel.ICON2),
	FIXED_VIEWPORT_INVENTORY_ICON(InterfaceID.Toplevel.ICON3),
	FIXED_VIEWPORT_EQUIPMENT_ICON(InterfaceID.Toplevel.ICON4),
	FIXED_VIEWPORT_PRAYER_ICON(InterfaceID.Toplevel.ICON5),
	FIXED_VIEWPORT_MAGIC_ICON(InterfaceID.Toplevel.ICON6),
	FIXED_VIEWPORT_FRIENDS_CHAT_ICON(InterfaceID.Toplevel.ICON7),
	FIXED_VIEWPORT_FRIENDS_ICON(InterfaceID.Toplevel.ICON9),
	FIXED_VIEWPORT_IGNORES_ICON(InterfaceID.Toplevel.ICON8),
	FIXED_VIEWPORT_LOGOUT_ICON(InterfaceID.Toplevel.ICON10),
	FIXED_VIEWPORT_OPTIONS_ICON(InterfaceID.Toplevel.ICON11),
	FIXED_VIEWPORT_EMOTES_ICON(InterfaceID.Toplevel.ICON12),
	FIXED_VIEWPORT_MUSIC_ICON(InterfaceID.Toplevel.ICON13),
	FIXED_VIEWPORT_MINIMAP(InterfaceID.Toplevel.MAPCONTAINER),
	FIXED_VIEWPORT_MINIMAP_DRAW_AREA(InterfaceID.Toplevel.MINIMAP),

	RESIZABLE_MINIMAP_STONES_WIDGET(InterfaceID.ToplevelOsrsStretch.MAP_CONTAINER),
	RESIZABLE_MINIMAP_STONES_DRAW_AREA(InterfaceID.ToplevelOsrsStretch.MINIMAP),
	RESIZABLE_MINIMAP_STONES_ORB_HOLDER(InterfaceID.ToplevelOsrsStretch.ORBS),
	RESIZABLE_VIEWPORT_OLD_SCHOOL_BOX(InterfaceID.ToplevelOsrsStretch.HUD_CONTAINER_FRONT),
	RESIZABLE_VIEWPORT_COMBAT_TAB(InterfaceID.ToplevelOsrsStretch.STONE0),
	RESIZABLE_VIEWPORT_STATS_TAB(InterfaceID.ToplevelOsrsStretch.STONE1),
	RESIZABLE_VIEWPORT_QUESTS_TAB(InterfaceID.ToplevelOsrsStretch.STONE2),
	RESIZABLE_VIEWPORT_INVENTORY_TAB(InterfaceID.ToplevelOsrsStretch.STONE3),
	RESIZABLE_VIEWPORT_EQUIPMENT_TAB(InterfaceID.ToplevelOsrsStretch.STONE4),
	RESIZABLE_VIEWPORT_PRAYER_TAB(InterfaceID.ToplevelOsrsStretch.STONE5),
	RESIZABLE_VIEWPORT_MAGIC_TAB(InterfaceID.ToplevelOsrsStretch.STONE6),
	RESIZABLE_VIEWPORT_FRIENDS_CHAT_TAB(InterfaceID.ToplevelOsrsStretch.STONE7),
	RESIZABLE_VIEWPORT_FRIENDS_TAB(InterfaceID.ToplevelOsrsStretch.STONE9),
	RESIZABLE_VIEWPORT_IGNORES_TAB(InterfaceID.ToplevelOsrsStretch.STONE8),
	RESIZABLE_VIEWPORT_LOGOUT_TAB(InterfaceID.ToplevelOsrsStretch.STONE10),
	RESIZABLE_VIEWPORT_OPTIONS_TAB(InterfaceID.ToplevelOsrsStretch.STONE11),
	RESIZABLE_VIEWPORT_EMOTES_TAB(InterfaceID.ToplevelOsrsStretch.STONE12),
	RESIZABLE_VIEWPORT_MUSIC_TAB(InterfaceID.ToplevelOsrsStretch.STONE13),
	RESIZABLE_VIEWPORT_COMBAT_ICON(InterfaceID.ToplevelOsrsStretch.ICON0),
	RESIZABLE_VIEWPORT_STATS_ICON(InterfaceID.ToplevelOsrsStretch.ICON1),
	RESIZABLE_VIEWPORT_QUESTS_ICON(InterfaceID.ToplevelOsrsStretch.ICON2),
	RESIZABLE_VIEWPORT_INVENTORY_ICON(InterfaceID.ToplevelOsrsStretch.ICON3),
	RESIZABLE_VIEWPORT_EQUIPMENT_ICON(InterfaceID.ToplevelOsrsStretch.ICON4),
	RESIZABLE_VIEWPORT_PRAYER_ICON(InterfaceID.ToplevelOsrsStretch.ICON5),
	RESIZABLE_VIEWPORT_MAGIC_ICON(InterfaceID.ToplevelOsrsStretch.ICON6),
	RESIZABLE_VIEWPORT_FRIENDS_CHAT_ICON(InterfaceID.ToplevelOsrsStretch.ICON7),
	RESIZABLE_VIEWPORT_FRIENDS_ICON(InterfaceID.ToplevelOsrsStretch.ICON9),
	RESIZABLE_VIEWPORT_IGNORES_ICON(InterfaceID.ToplevelOsrsStretch.ICON8),
	RESIZABLE_VIEWPORT_LOGOUT_ICON(InterfaceID.ToplevelOsrsStretch.ICON10),
	RESIZABLE_VIEWPORT_OPTIONS_ICON(InterfaceID.ToplevelOsrsStretch.ICON11),
	RESIZABLE_VIEWPORT_EMOTES_ICON(InterfaceID.ToplevelOsrsStretch.ICON12),
	RESIZABLE_VIEWPORT_MUSIC_ICON(InterfaceID.ToplevelOsrsStretch.ICON13),
	RESIZABLE_VIEWPORT_INTERFACE_CONTAINER(InterfaceID.ToplevelOsrsStretch.SIDE_CONTAINER),
	RESIZABLE_VIEWPORT_INVENTORY_CONTAINER(InterfaceID.ToplevelOsrsStretch.SIDE3),
	RESIZABLE_VIEWPORT_CHATBOX_PARENT(InterfaceID.ToplevelOsrsStretch.CHAT_CONTAINER),
	RESIZABLE_VIEWPORT_INVENTORY_PARENT(InterfaceID.ToplevelOsrsStretch.SIDE_MENU),

	RESIZABLE_MINIMAP_WIDGET(InterfaceID.ToplevelPreEoc.MAP_CONTAINER),
	RESIZABLE_MINIMAP_DRAW_AREA(InterfaceID.ToplevelPreEoc.MINIMAP),
	RESIZABLE_MINIMAP_ORB_HOLDER(InterfaceID.ToplevelPreEoc.ORBS),
	RESIZABLE_MINIMAP_LOGOUT_BUTTON(InterfaceID.ToplevelPreEoc.ICON10),
	RESIZABLE_VIEWPORT_BOTTOM_LINE(InterfaceID.ToplevelPreEoc.HUD_CONTAINER_FRONT),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_LOGOUT_BUTTON(InterfaceID.ToplevelPreEoc.STONE10),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_QUESTS_ICON(InterfaceID.ToplevelPreEoc.ICON2),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_INVENTORY_TAB(InterfaceID.ToplevelPreEoc.STONE3),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_INVENTORY_ICON(InterfaceID.ToplevelPreEoc.ICON3),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_PRAYER_TAB(InterfaceID.ToplevelPreEoc.STONE5),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_PRAYER_ICON(InterfaceID.ToplevelPreEoc.ICON5),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_EQUIPMENT_ICON(InterfaceID.ToplevelPreEoc.ICON4),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_COMBAT_ICON(InterfaceID.ToplevelPreEoc.ICON0),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_STATS_ICON(InterfaceID.ToplevelPreEoc.ICON1),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_MAGIC_ICON(InterfaceID.ToplevelPreEoc.ICON6),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_FRIEND_ICON(InterfaceID.ToplevelPreEoc.ICON9),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_FRIEND_CHAT_ICON(InterfaceID.ToplevelPreEoc.ICON7),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_OPTIONS_ICON(InterfaceID.ToplevelPreEoc.ICON11),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_EMOTES_ICON(InterfaceID.ToplevelPreEoc.ICON12),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_MUSIC_ICON(InterfaceID.ToplevelPreEoc.ICON13),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_INVENTORY_CONTAINER(InterfaceID.ToplevelPreEoc.SIDE3),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_INTERFACE_CONTAINER(InterfaceID.ToplevelPreEoc.SIDE_BACKGROUND),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_CHATBOX_PARENT(InterfaceID.ToplevelPreEoc.CHAT_CONTAINER),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_TABS1(InterfaceID.ToplevelPreEoc.SIDE_STATIC_LAYER),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_TABS2(InterfaceID.ToplevelPreEoc.SIDE_MOVABLE_LAYER),
	RESIZABLE_VIEWPORT_BOTTOM_LINE_INVENTORY_PARENT(InterfaceID.ToplevelPreEoc.SIDE_CONTAINER),

	COMBAT_LEVEL(InterfaceID.CombatInterface.LEVEL),
	COMBAT_STYLE_ONE(InterfaceID.CombatInterface._0),
	COMBAT_STYLE_TWO(InterfaceID.CombatInterface._1),
	COMBAT_STYLE_THREE(InterfaceID.CombatInterface._2),
	COMBAT_STYLE_FOUR(InterfaceID.CombatInterface._3),
	COMBAT_SPELLS(InterfaceID.CombatInterface.AUTOCAST_BUTTONS),
	COMBAT_DEFENSIVE_SPELL_BOX(InterfaceID.CombatInterface.AUTOCAST_DEFENSIVE),
	COMBAT_DEFENSIVE_SPELL_ICON(InterfaceID.CombatInterface.DEFENSIVE_CONTAINER_GRAPHIC0),
	COMBAT_DEFENSIVE_SPELL_SHIELD(InterfaceID.CombatInterface.DEFENSIVE_CONTAINER_GRAPHIC1),
	COMBAT_DEFENSIVE_SPELL_TEXT(InterfaceID.CombatInterface.DEFENSIVE_CONTAINER_TEXT2),
	COMBAT_SPELL_BOX(InterfaceID.CombatInterface.AUTOCAST_NORMAL),
	COMBAT_SPELL_ICON(InterfaceID.CombatInterface.NORMAL_CONTAINER_GRAPHIC0),
	COMBAT_SPELL_TEXT(InterfaceID.CombatInterface.NORMAL_CONTAINER_TEXT1),
	COMBAT_AUTO_RETALIATE(InterfaceID.CombatInterface.RETALIATE),

	DIALOG_OPTION(WidgetID.DIALOG_OPTION_GROUP_ID, 0),
	DIALOG_OPTION_OPTIONS(InterfaceID.Chatmenu.OPTIONS),

	DIALOG_SPRITE(WidgetID.DIALOG_SPRITE_GROUP_ID, 0),
	DIALOG_SPRITE_SPRITE(InterfaceID.Objectbox.ITEM),
	DIALOG_SPRITE_TEXT(InterfaceID.Objectbox.TEXT),
	DIALOG_DOUBLE_SPRITE_TEXT(InterfaceID.ObjectboxDouble.TEXT),
	DIALOG_DOUBLE_SPRITE_SPRITE1(InterfaceID.ObjectboxDouble.MODEL1),
	DIALOG_DOUBLE_SPRITE_SPRITE2(InterfaceID.ObjectboxDouble.MODEL2),

	DIALOG_NPC_NAME(InterfaceID.ChatLeft.NAME),
	DIALOG_NPC_TEXT(InterfaceID.ChatLeft.TEXT),
	DIALOG_NPC_HEAD_MODEL(InterfaceID.ChatLeft.HEAD),

	DIALOG_PLAYER(WidgetID.DIALOG_PLAYER_GROUP_ID, 0),
	DIALOG_PLAYER_TEXT(InterfaceID.ChatRight.TEXT),

	PRIVATE_CHAT_MESSAGE(WidgetID.PRIVATE_CHAT, 0),

	SLAYER_REWARDS_TOPBAR(InterfaceID.SlayerRewards.TABS),

	CHATBOX_PARENT(InterfaceID.Chatbox.UNIVERSE),
	CHATBOX(InterfaceID.Chatbox.CHATAREA),
	CHATBOX_MESSAGES(InterfaceID.Chatbox.MES_LAYER_HIDE),
	CHATBOX_BUTTONS(InterfaceID.Chatbox.CONTROLS),
	CHATBOX_TITLE(InterfaceID.Chatbox.MES_TEXT),
	CHATBOX_FULL_INPUT(InterfaceID.Chatbox.MES_TEXT2),
	CHATBOX_GE_SEARCH_RESULTS(InterfaceID.Chatbox.MES_LAYER_SCROLLCONTENTS),
	CHATBOX_CONTAINER(InterfaceID.Chatbox.MES_LAYER),
	CHATBOX_REPORT_TEXT(InterfaceID.Chatbox.REPORTABUSE_TEXT1),
	CHATBOX_INPUT(InterfaceID.Chatbox.INPUT),
	CHATBOX_TRANSPARENT_BACKGROUND(InterfaceID.Chatbox.CHAT_BACKGROUND),
	CHATBOX_TRANSPARENT_LINES(InterfaceID.Chatbox.CHATDISPLAY),
	CHATBOX_MESSAGE_LINES(InterfaceID.Chatbox.SCROLLAREA),
	CHATBOX_FIRST_MESSAGE(InterfaceID.Chatbox.LINE0),
	CHATBOX_TAB_ALL(InterfaceID.Chatbox.CHAT_ALL),
	CHATBOX_TAB_GAME(InterfaceID.Chatbox.CHAT_GAME),
	CHATBOX_TAB_PUBLIC(InterfaceID.Chatbox.CHAT_PUBLIC),
	CHATBOX_TAB_PRIVATE(InterfaceID.Chatbox.CHAT_PRIVATE),
	CHATBOX_TAB_CHANNEL(InterfaceID.Chatbox.CHAT_FRIENDSCHAT),
	CHATBOX_TAB_CLAN(InterfaceID.Chatbox.CHAT_CLAN),
	CHATBOX_TAB_TRADE(InterfaceID.Chatbox.CHAT_TRADE),

	BA_TEAM(InterfaceID.BarbassaultOverRecruitPlayerNames.CONTENT),

	BA_HEAL_ROLE_TEXT(InterfaceID.BarbassaultOverHeal.BARBASSAULT_HEALER_HORN_TEXT),
	BA_HEAL_ROLE_SPRITE(InterfaceID.BarbassaultOverHeal.BARBASSAULT_HEALER_HORN),

	BA_HEAL_TEAMMATES(InterfaceID.BarbassaultOverHeal.CONTENT_BOTTOM),
	BA_HEAL_TEAMMATE1(InterfaceID.BarbassaultOverHeal.BARBASSAULT_HEALER_PLAYER1_HP),
	BA_HEAL_TEAMMATE2(InterfaceID.BarbassaultOverHeal.BARBASSAULT_HEALER_PLAYER2_HP),
	BA_HEAL_TEAMMATE3(InterfaceID.BarbassaultOverHeal.BARBASSAULT_HEALER_PLAYER3_HP),
	BA_HEAL_TEAMMATE4(InterfaceID.BarbassaultOverHeal.BARBASSAULT_HEALER_PLAYER4_HP),

	BA_COLL_ROLE_TEXT(InterfaceID.BarbassaultOverCol.BARBASSAULT_COLLECTOR_HORN_TEXT),
	BA_COLL_ROLE_SPRITE(InterfaceID.BarbassaultOverCol.BARBASSAULT_COLLECTOR_HORN),

	BA_ATK_ROLE_TEXT(InterfaceID.BarbassaultOverAtt.BARBASSAULT_ATTACKER_HORN_TEXT),
	BA_ATK_ROLE_SPRITE(InterfaceID.BarbassaultOverAtt.BARBASSAULT_ATTACKER_HORN),

	BA_DEF_ROLE_TEXT(InterfaceID.BarbassaultOverDef.BARBASSAULT_DEFENDER_HORN_TEXT),
	BA_DEF_ROLE_SPRITE(InterfaceID.BarbassaultOverDef.BARBASSAULT_DEFENDER_HORN),

	BA_REWARD_TEXT(InterfaceID.BarbassaultWavecomplete.BARBASSAULT_COMPL_QUEENREWARDS),

	LEVEL_UP(WidgetID.LEVEL_UP_GROUP_ID, 0),
	LEVEL_UP_SKILL(InterfaceID.LevelupDisplay.TEXT1),
	LEVEL_UP_LEVEL(InterfaceID.LevelupDisplay.TEXT2),

	QUEST_COMPLETED(WidgetID.QUEST_COMPLETED_GROUP_ID, 0),
	QUEST_COMPLETED_NAME_TEXT(InterfaceID.Questscroll.QUEST_TITLE),

	MOTHERLODE_MINE(WidgetID.MOTHERLODE_MINE_GROUP_ID, 0),

	GWD_KC(WidgetID.GWD_KC_GROUP_ID, 5),

	PUZZLE_BOX(InterfaceID.TrailSlidepuzzle.PIECES),

	LIGHT_BOX(InterfaceID.LightPuzzle.STEEL_BORDER),
	LIGHT_BOX_CONTENTS(InterfaceID.LightPuzzle.LIGHTS),
	LIGHT_BOX_BUTTON_A(InterfaceID.LightPuzzle.BUTTON1),
	LIGHT_BOX_BUTTON_B(InterfaceID.LightPuzzle.BUTTON2),
	LIGHT_BOX_BUTTON_C(InterfaceID.LightPuzzle.BUTTON3),
	LIGHT_BOX_BUTTON_D(InterfaceID.LightPuzzle.BUTTON4),
	LIGHT_BOX_BUTTON_E(InterfaceID.LightPuzzle.BUTTON5),
	LIGHT_BOX_BUTTON_F(InterfaceID.LightPuzzle.BUTTON6),
	LIGHT_BOX_BUTTON_G(InterfaceID.LightPuzzle.BUTTON7),
	LIGHT_BOX_BUTTON_H(InterfaceID.LightPuzzle.BUTTON8),

	LIGHT_BOX_WINDOW(InterfaceID.LightPuzzle.CONTENT_LAYER),

	NIGHTMARE_ZONE(WidgetID.NIGHTMARE_ZONE_GROUP_ID, 0),

	NIGHTMARE_PILLAR_HEALTH(WidgetID.NIGHTMARE_PILLAR_HEALTH_GROUP_ID, 1),

	RAIDS_POINTS_INFOBOX(InterfaceID.RaidsOverlay.DATA),

	RAIDS_PRIVATE_STORAGE_ITEM_CONTAINER(InterfaceID.RaidsStoragePrivate.ITEMS),

	TOB_PARTY_INTERFACE(InterfaceID.TobHud.NAMES_CONTAINER),
	TOB_PARTY_STATS(InterfaceID.TobHud.STATUS_CONTAINER),
	TOB_HEALTH_BAR(InterfaceID.TobHud.PROGRESS_CONTAINER),

	BLAST_FURNACE_COFFER(WidgetID.BLAST_FURNACE_GROUP_ID, 2),

	PYRAMID_PLUNDER_DATA(WidgetID.PYRAMID_PLUNDER_GROUP_ID, 2),

	EXPERIENCE_TRACKER(WidgetID.EXPERIENCE_TRACKER_GROUP_ID, 0),
	EXPERIENCE_TRACKER_WIDGET(InterfaceID.XpDrops.CONTAINER),
	EXPERIENCE_TRACKER_BOTTOM_BAR(InterfaceID.XpDrops.PROGRESS_BAR),

	FISHING_TRAWLER_CONTRIBUTION(WidgetID.FISHING_TRAWLER_GROUP_ID, 13),
	FISHING_TRAWLER_TIMER(WidgetID.FISHING_TRAWLER_GROUP_ID, 14),

	TITHE_FARM(WidgetID.TITHE_FARM_GROUP_ID, 2),

	BARROWS_BROTHERS(InterfaceID.BarrowsOverlay.BROTHERS),
	BARROWS_POTENTIAL(InterfaceID.BarrowsOverlay.KILLCOUNT),
	BARROWS_PUZZLE_PARENT(InterfaceID.BarrowsPuzzle.UNIVERSE),
	BARROWS_PUZZLE_ANSWER1(InterfaceID.BarrowsPuzzle.PIC_A),
	BARROWS_PUZZLE_ANSWER1_CONTAINER(InterfaceID.BarrowsPuzzle.A),
	BARROWS_PUZZLE_ANSWER2(InterfaceID.BarrowsPuzzle.PIC_B),
	BARROWS_PUZZLE_ANSWER2_CONTAINER(InterfaceID.BarrowsPuzzle.B),
	BARROWS_PUZZLE_ANSWER3(InterfaceID.BarrowsPuzzle.PIC_C),
	BARROWS_PUZZLE_ANSWER3_CONTAINER(InterfaceID.BarrowsPuzzle.C),
	BARROWS_FIRST_PUZZLE(InterfaceID.BarrowsPuzzle._1),

	BLAST_MINE(WidgetID.BLAST_MINE_GROUP_ID, 2),

	FAIRY_RING(WidgetID.FAIRY_RING_GROUP_ID, 0),

	FAIRY_RING_HEADER(InterfaceID.FairyringsLog.TITLEBOX),
	FAIRY_RING_LIST(InterfaceID.FairyringsLog.CONTENTS),
	FAIRY_RING_FAVORITES(InterfaceID.FairyringsLog.FAVES),
	FAIRY_RING_LIST_SEPARATOR(InterfaceID.FairyringsLog.DIVIDER),
	FAIRY_RING_LIST_SCROLLBAR(InterfaceID.FairyringsLog.SCROLLBAR),

	DESTROY_ITEM(WidgetID.DESTROY_ITEM_GROUP_ID, 0),
	DESTROY_ITEM_NAME(InterfaceID.Confirmdestroy.NAME),
	DESTROY_ITEM_YES(InterfaceID.Confirmdestroy.YES),
	DESTROY_ITEM_NO(InterfaceID.Confirmdestroy.NO),

	VARROCK_MUSEUM_QUESTION(InterfaceID.VmNaturalHistory.VM_NATHIS_QUESTION),
	VARROCK_MUSEUM_FIRST_ANSWER(InterfaceID.VmNaturalHistory.VM_NATHIS_ANSWER_01),
	VARROCK_MUSEUM_SECOND_ANSWER(InterfaceID.VmNaturalHistory.VM_NATHIS_ANSWER_02),
	VARROCK_MUSEUM_THIRD_ANSWER(InterfaceID.VmNaturalHistory.VM_NATHIS_ANSWER_03),

	KILL_LOG_TITLE(InterfaceID.KillLog.INTERFACE_TITLE),
	KILL_LOG_MONSTER(InterfaceID.KillLog.NAME),
	KILL_LOG_KILLS(InterfaceID.KillLog.KILL),
	KILL_LOG_STREAK(InterfaceID.KillLog.STREAK),

	ADVENTURE_LOG(InterfaceID.Menu.LJ_LAYER2),

	COLLECTION_LOG(InterfaceID.Collection.INFINITY),

	COLLECTION_LOG_TABS(InterfaceID.Collection.TABS),
	COLLECTION_LOG_ENTRY(InterfaceID.Collection.MAIN),
	COLLECTION_LOG_ENTRY_HEADER(InterfaceID.Collection.HEADER_TEXT),
	COLLECTION_LOG_ENTRY_ITEMS(InterfaceID.Collection.ITEMS_CONTENTS),

	GENERIC_SCROLL_TEXT(InterfaceID.Longscroll.SCROLL_TEXT),

	WORLD_SWITCHER_LIST(InterfaceID.Worldswitcher.BUTTONS),

	FOSSIL_ISLAND_OXYGENBAR(WidgetID.FOSSIL_ISLAND_OXYGENBAR_ID, 1),

	SPELL_LUMBRIDGE_HOME_TELEPORT(InterfaceID.MagicSpellbook.TELEPORT_HOME_STANDARD),
	SPELL_EDGEVILLE_HOME_TELEPORT(InterfaceID.MagicSpellbook.TELEPORT_HOME_ZAROS),
	SPELL_LUNAR_HOME_TELEPORT(InterfaceID.MagicSpellbook.TELEPORT_HOME_LUNAR),
	SPELL_ARCEUUS_HOME_TELEPORT(InterfaceID.MagicSpellbook.TELEPORT_HOME_ARCEUUS),
	SPELL_KOUREND_HOME_TELEPORT(InterfaceID.MagicSpellbook.BACK_BUTTON),
	SPELL_CATHERBY_HOME_TELEPORT(InterfaceID.MagicSpellbook.LEAGUE_TWISTED_HOME_TELEPORT),
	SPELL_LUNAR_FERTILE_SOIL(InterfaceID.MagicSpellbook.FERTILE_SOIL),

	PVP_WILDERNESS_SKULL_CONTAINER(InterfaceID.PvpIcons.ICONS),
	PVP_SKULL_CONTAINER(InterfaceID.PvpIcons.ICON_CONTAINER),
	PVP_WORLD_SAFE_ZONE(InterfaceID.PvpIcons.PVPW_SAFE),

	PVP_WILDERNESS_LEVEL(InterfaceID.PvpIcons.WILDERNESSLEVEL),
	PVP_KILLDEATH_COUNTER(InterfaceID.PvpIcons.KD_CONTAINER),

	ZEAH_MESS_HALL_COOKING_DISPLAY(InterfaceID.HosidiusServeryHud.CONTENT),

	LOOTING_BAG_CONTAINER(InterfaceID.WildernessLootingbag.ITEMS),

	SKOTIZO_CONTAINER(InterfaceID.CataBoss.CATA_ALTARS),

	CHARACTER_SUMMARY_CONTAINER(InterfaceID.AccountSummarySidepanel.SUMMARY_CONTENTS),

	QUESTLIST_BOX(InterfaceID.Questlist.UNIVERSE),
	QUESTLIST_CONTAINER(InterfaceID.Questlist.CONTAINER),

	SEED_VAULT_TITLE_CONTAINER(InterfaceID.SeedVault.FRAME),
	SEED_VAULT_ITEM_CONTAINER(InterfaceID.SeedVault.OBJ_LIST),
	SEED_VAULT_ITEM_TEXT(InterfaceID.SeedVault.TEXT_LIST),
	SEED_VAULT_SEARCH_BUTTON(InterfaceID.SeedVault.SEARCH),
	SEED_VAULT_INVENTORY_ITEMS_CONTAINER(InterfaceID.SeedVaultDeposit.INV),

	SETTINGS_SIDE_CAMERA_ZOOM_SLIDER_TRACK(InterfaceID.SettingsSide.ZOOM_SLIDER),
	SETTINGS_SIDE_MUSIC_SLIDER(InterfaceID.SettingsSide.MUSIC_HOLDER),
	SETTINGS_SIDE_MUSIC_SLIDER_STEP_HOLDER(InterfaceID.SettingsSide.MUSIC_BOBBLE_CONTAINER),
	SETTINGS_SIDE_SOUND_EFFECT_SLIDER(InterfaceID.SettingsSide.SOUND_HOLDER),
	SETTINGS_SIDE_AREA_SOUND_SLIDER(InterfaceID.SettingsSide.AREASOUNDS_HOLDER),

	SETTINGS_INIT(InterfaceID.Settings.UNIVERSE),

	ACHIEVEMENT_DIARY_CONTAINER(InterfaceID.AreaTask.TASKBOX),

	SKILLS_CONTAINER(InterfaceID.Stats.UNIVERSE),

	GAUNTLET_TIMER_CONTAINER(InterfaceID.GauntletOverlay.CONTENT),
	HALLOWED_SEPULCHRE_TIMER_CONTAINER(InterfaceID.HallowedOverlay.CONTENT),

	HEALTH_OVERLAY_BAR(InterfaceID.HpbarHud.HP),
	HEALTH_OVERLAY_BAR_TEXT(InterfaceID.HpbarHud.HP_BAR_TEXT),

	TRAILBLAZER_AREA_TELEPORT(InterfaceID.TrailblazerAreas.ECHO_BOSS_MAP_BUTTON),

	MULTICOMBAT_FIXED(InterfaceID.Toplevel.MULTIWAY_ICON),
	MULTICOMBAT_RESIZABLE_MODERN(InterfaceID.ToplevelPreEoc.MULTIWAY_ICON),
	MULTICOMBAT_RESIZABLE_CLASSIC(InterfaceID.ToplevelOsrsStretch.MULTIWAY_ICON),

	TEMPOROSS_STATUS_INDICATOR(InterfaceID.TemporossHud.STATUS),
	TEMPOROSS_LOBBY(InterfaceID.TemporossLobbyHud.CONTENT),

	CLAN_LAYER(InterfaceID.ClansSidepanel.UNIVERSE),
	CLAN_HEADER(InterfaceID.ClansSidepanel.HEADER),
	CLAN_MEMBER_LIST(InterfaceID.ClansSidepanel.PLAYERLIST),

	CLAN_GUEST_LAYER(InterfaceID.ClansGuestSidepanel.UNIVERSE),
	CLAN_GUEST_HEADER(InterfaceID.ClansGuestSidepanel.HEADER),
	CLAN_GUEST_MEMBER_LIST(InterfaceID.ClansGuestSidepanel.PLAYERLIST),

	POH_TREASURE_CHEST_INVENTORY_CONTAINER(WidgetID.POH_TREASURE_CHEST_INVENTORY_GROUP_ID, 0),

	TRADE_WINDOW_HEADER(InterfaceID.Trademain.TITLE),

	TOA_PARTY_LAYER(WidgetID.TOA_PARTY_GROUP_ID, 2),
	TOA_RAID_LAYER(WidgetID.TOA_RAID_GROUP_ID, 3),

	QUICK_PRAYER_PRAYERS(WidgetID.QUICK_PRAYERS_GROUP_ID, 4),

	GOTR_MAIN_DISPLAY(WidgetID.GOTR_GROUP_ID, 2),
	TROUBLE_BREWING_SCORE(InterfaceID.BrewOverlay.CONTENT_RIGHT),
	TROUBLE_BREWING_LOBBY(WidgetID.TROUBLE_BREWING_LOBBY_GROUP_ID, 2),
	MORTTON_TEMPLE_STATUS(WidgetID.MORTTON_TEMPLE_GROUP_ID, 2),
	BGR_RANK_DISPLAY_DRAUGHTS(WidgetID.BGR_RANK_DRAUGHTS_GROUP_ID, 2),
	BGR_RANK_DISPLAY_RUNELINK(WidgetID.BGR_RANK_RUNELINK_GROUP_ID, 2),
	BGR_RANK_DISPLAY_RUNESQUARES(WidgetID.BGR_RANK_RUNESQUARES_GROUP_ID, 2),
	BGR_RANK_DISPLAY_RUNEVERSI(WidgetID.BGR_RANK_RUNEVERSI_GROUP_ID, 2),
	AGILITY_ARENA_LIGHT_INDICATOR(WidgetID.AGILITY_ARENA_HUD_GROUP_ID, 2),
	GNOMEBALL_SCORE(WidgetID.GNOMEBALL_SCORE_GROUP_ID, 2),
	MTA_ALCHEMY_POINTS(WidgetID.MTA_ALCHEMY_GROUP_ID, 2),
	MTA_ENCHANT_POINTS(WidgetID.MTA_ENCHANT_GROUP_ID, 2),
	MTA_ENCHANT_BONUS(WidgetID.MTA_ENCHANT_GROUP_ID, 8),
	MTA_GRAVEYARD_POINTS(WidgetID.MTA_GRAVEYARD_GROUP_ID, 2),
	MTA_GRAVEYARD_VALUES(WidgetID.MTA_GRAVEYARD_GROUP_ID, 8),
	MTA_TELEKINETIC_POINTS(WidgetID.MTA_TELEKINETIC_GROUP_ID, 2),
	MTA_TELEKINETIC_SOLVED(WidgetID.MTA_TELEKINETIC_GROUP_ID, 8),

	STRANGLER_INFECTION_OVERLAY(WidgetID.THE_STRANGLER_INFECTION_GROUP_ID, 4),
	SANITY_OVERLAY(WidgetID.SANITY_GROUP_ID, 3),
	;

	private final int id;

	WidgetInfo(int id)
	{
		this.id = id;
	}

	WidgetInfo(int groupId, int childId)
	{
		this.id = (groupId << 16) | childId;
	}

	/**
	 * Gets the ID of the group-child pairing.
	 *
	 * @return the ID
	 */
	public int getId()
	{
		return id;
	}

	/**
	 * Gets the group ID of the pair.
	 *
	 * @return the group ID
	 */
	public int getGroupId()
	{
		return id >> 16;
	}

	/**
	 * Gets the ID of the child in the group.
	 *
	 * @return the child ID
	 */
	public int getChildId()
	{
		return id & 0xffff;
	}

	/**
	 * Gets the packed widget ID.
	 *
	 * @return the packed ID
	 */
	public int getPackedId()
	{
		return id;
	}

	/**
	 * Utility method that converts an ID returned by {@link #getId()} back
	 * to its group ID.
	 *
	 * @param id passed group-child ID
	 * @return the group ID
	 */
	public static int TO_GROUP(int id)
	{
		return id >>> 16;
	}

	/**
	 * Utility method that converts an ID returned by {@link #getId()} back
	 * to its child ID.
	 *
	 * @param id passed group-child ID
	 * @return the child ID
	 */
	public static int TO_CHILD(int id)
	{
		return id & 0xFFFF;
	}

	/**
	 * Packs the group and child IDs into a single integer.
	 *
	 * @param groupId the group ID
	 * @param childId the child ID
	 * @return the packed ID
	 */
	public static int PACK(int groupId, int childId)
	{
		return groupId << 16 | childId;
	}

}
