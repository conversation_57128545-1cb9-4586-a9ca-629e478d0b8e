/* This file is automatically generated. Do not edit. */
package net.runelite.api.gameval;

@SuppressWarnings("unused")
public final class InventoryID
{
	public static final int TRAWLER_REWARDINV = 0;
	public static final int AXESHOP = 1;
	public static final int ARMOURSHOP = 2;
	public static final int GENERALSHOP1 = 3;
	public static final int GENERALSHOP2 = 4;
	public static final int RUNESHOP = 5;
	public static final int SWORDSHOP = 6;
	public static final int ARCHERYSHOP = 7;
	public static final int CLOTHESHOP = 8;
	public static final int STAFFSHOP = 9;
	public static final int HELMETSHOP = 10;
	public static final int SCIMITARSHOP = 11;
	public static final int LEGSSHOP = 12;
	public static final int GENERALSHOP3 = 13;
	public static final int MININGSTORE = 14;
	public static final int SHIELDSHOP = 15;
	public static final int SKIRTSHOP = 16;
	public static final int GENERALSHOP4 = 17;
	public static final int CHAMPIONSHOP = 18;
	public static final int MACESHOP = 19;
	public static final int FOODSHOP = 20;
	public static final int BATTLEAXESHOP = 21;
	public static final int CHAINMAILSHOP = 22;
	public static final int GENERALDWARF = 23;
	public static final int GENERALSHOP5 = 24;
	public static final int MAGICSHOP = 25;
	public static final int GEMSHOP = 26;
	public static final int CRAFTINGSHOP = 27;
	public static final int GOLDSHOP = 28;
	public static final int BOOZESHOP = 29;
	public static final int FISHINGSHOP = 30;
	public static final int GENERALSHOP6 = 31;
	public static final int CRAFTINGSHOP2 = 32;
	public static final int RUNITESHOP = 33;
	public static final int GENERALSHOP7 = 34;
	public static final int RUNEPLATESHOP = 35;
	public static final int DRAGONSWORDSHOP = 36;
	public static final int CHEAPRINGSHOP = 37;
	public static final int GENERALSHOP8 = 38;
	public static final int _2HANDEDSHOP = 39;
	public static final int HERBLORESHOP = 40;
	public static final int TAXFREE = 41;
	public static final int PIZZABASESHOP = 42;
	public static final int FISHINGSHOP2 = 43;
	public static final int FISHRESTAURANT = 44;
	public static final int DRAGONAXESHOP = 45;
	public static final int AMULETSHOP = 46;
	public static final int ARHEINSTORE = 47;
	public static final int CANDLESHOP = 48;
	public static final int ARCHERYSHOP2 = 49;
	public static final int HERBLORESHOP2 = 50;
	public static final int MEMBERSTAFFSHOP = 51;
	public static final int GEMSHOP2 = 52;
	public static final int TOPSHOP = 53;
	public static final int BAKERY = 54;
	public static final int SILVERSHOP = 55;
	public static final int GEMSHOP3 = 56;
	public static final int ADVENTURERSHOP = 57;
	public static final int SPICESHOP = 58;
	public static final int FURSHOP = 59;
	public static final int FISHINGGUILD = 60;
	public static final int KHAZARDSHOP = 61;
	public static final int GNOMESHOP = 62;
	public static final int FANCYCLOTHESSTORE = 63;
	public static final int MAGICGUILDSHOP = 64;
	public static final int JUNGLESTORE = 65;
	public static final int LATHASTRAININGSTORE = 66;
	public static final int COOKERYSHOP = 67;
	public static final int PICKAXESHOP = 68;
	public static final int TEASHOP = 69;
	public static final int GENERALSHOPOGRE = 70;
	public static final int HERBLORESHOPOGRE = 71;
	public static final int SILVERSTALL = 72;
	public static final int ARDOUGNEGEMSTALL = 73;
	public static final int SPICESTALL = 74;
	public static final int FURSTALL = 75;
	public static final int WYDINSTORE = 76;
	public static final int BEDABINCAMPSHOP = 77;
	public static final int SHANTAYSHOP = 78;
	public static final int MCANNONSHOP = 79;
	public static final int RANGING_GUILD_BOWSHOP = 80;
	public static final int RANGING_GUILD_ARMOURSHOP = 81;
	public static final int RANGING_GUILD_TRIBALSHOP = 82;
	public static final int GENERALLEGENDS = 83;
	public static final int LEGENDSGUILDSHOP = 84;
	public static final int SHILOFISHINGSHOP = 85;
	public static final int GNOMESHOP_ROMETTI = 86;
	public static final int GNOMESHOP_GULLUCK = 87;
	public static final int GNOMESHOP_HECK = 88;
	public static final int GNOMESHOP_HUDO = 89;
	public static final int TRADEOFFER = 90;
	public static final int PARTYROOM_DROPINV = 91;
	public static final int PARTYROOM_TEMPINV = 92;
	public static final int INV = 93;
	public static final int WORN = 94;
	public static final int BANK = 95;
	public static final int CRAFTING_MAKE_RINGS = 96;
	public static final int CRAFTING_MAKE_NECKLACES = 97;
	public static final int CRAFTING_MAKE_AMULETS = 98;
	public static final int BLURBERRYBAR = 99;
	public static final int GIANNERESTAURANT = 100;
	public static final int SMITHING_BRONZE1 = 101;
	public static final int SMITHING_BRONZE2 = 102;
	public static final int SMITHING_BRONZE3 = 103;
	public static final int SMITHING_BRONZE4 = 104;
	public static final int SMITHING_BRONZE5 = 105;
	public static final int SMITHING_IRON1 = 106;
	public static final int SMITHING_IRON2 = 107;
	public static final int SMITHING_IRON3 = 108;
	public static final int SMITHING_IRON4 = 109;
	public static final int SMITHING_IRON5 = 110;
	public static final int SMITHING_STEEL1 = 111;
	public static final int SMITHING_STEEL2 = 112;
	public static final int SMITHING_STEEL3 = 113;
	public static final int SMITHING_STEEL4 = 114;
	public static final int SMITHING_STEEL5 = 115;
	public static final int SMITHING_MITHRIL1 = 116;
	public static final int SMITHING_MITHRIL2 = 117;
	public static final int SMITHING_MITHRIL3 = 118;
	public static final int SMITHING_MITHRIL4 = 119;
	public static final int SMITHING_MITHRIL5 = 120;
	public static final int SMITHING_ADAMANT1 = 121;
	public static final int SMITHING_ADAMANT2 = 122;
	public static final int SMITHING_ADAMANT3 = 123;
	public static final int SMITHING_ADAMANT4 = 124;
	public static final int SMITHING_ADAMANT5 = 125;
	public static final int SMITHING_RUNE1 = 126;
	public static final int SMITHING_RUNE2 = 127;
	public static final int SMITHING_RUNE3 = 128;
	public static final int SMITHING_RUNE4 = 129;
	public static final int SMITHING_RUNE5 = 130;
	public static final int MAGEARENA_RUNESHOP = 131;
	public static final int MAGEARENA_STAFFSHOP = 132;
	public static final int DEATHKEEP = 133;
	public static final int DUELOFFER = 134;
	public static final int DUEL_ROTTENFRUITSHOP = 135;
	public static final int DUELWINNINGS = 136;
	public static final int APRILFOOLSHORSESHOP = 137;
	public static final int SHILOJUNGLESTORE = 138;
	public static final int UPASSGENERALSHOP = 139;
	public static final int TRAIL_PUZZLEINV = 140;
	public static final int TRAIL_REWARDINV = 141;
	public static final int DUELARROWS = 142;
	public static final int WEREWOLFSTORE1 = 143;
	public static final int WEREWOLFSTORE2 = 144;
	public static final int WEREWOLFGENERALSTORE = 145;
	public static final int CRAFTINGSHOP_FREE = 146;
	public static final int CRAFTINGSHOP2_FREE = 147;
	public static final int DEATH_GENERALSHOP = 148;
	public static final int DEATH_PUB = 149;
	public static final int SMITHING_BRONZE_CLAWS = 150;
	public static final int SMITHING_IRON_CLAWS = 151;
	public static final int SMITHING_STEEL_CLAWS = 152;
	public static final int SMITHING_MITHRIL_CLAWS = 153;
	public static final int SMITHING_ADAMANT_CLAWS = 154;
	public static final int SMITHING_RUNE_CLAWS = 155;
	public static final int TBWT_TIADECHE_FINAL_INVENTORY = 156;
	public static final int KARAMJA_FISHRESTAURANT = 157;
	public static final int TBWT_TAMAYU_FINAL_INVENTORY = 158;
	public static final int REGICIDE_GENERAL_SHOP = 159;
	public static final int REGICIDE_GENERAL_SHOP_2 = 160;
	public static final int SKILL_GUIDE_FIREMAKING = 161;
	public static final int SKILL_GUIDE_AGILITY = 162;
	public static final int SKILL_GUIDE_COMBAT_WEAPONS = 163;
	public static final int SKILL_GUIDE_COMBAT_ARMOURS = 164;
	public static final int SKILL_GUIDE_COOKING_OVERALL = 165;
	public static final int SKILL_GUIDE_COOKING_OVERALL2 = 166;
	public static final int SKILL_GUIDE_COOKING_MEAT = 167;
	public static final int SKILL_GUIDE_COOKING_BREAD = 168;
	public static final int SKILL_GUIDE_COOKING_CAKES = 169;
	public static final int SKILL_GUIDE_COOKING_PIZZAS = 170;
	public static final int SKILL_GUIDE_COOKING_PIES = 171;
	public static final int SKILL_GUIDE_COOKING_STEWS = 172;
	public static final int SKILL_GUIDE_COOKING_WINE = 173;
	public static final int SKILL_GUIDE_CRAFTING_LEATHER = 174;
	public static final int SKILL_GUIDE_CRAFTING_SPINNING = 175;
	public static final int SKILL_GUIDE_CRAFTING_POTTERY = 176;
	public static final int SKILL_GUIDE_CRAFTING_GLASS = 177;
	public static final int SKILL_GUIDE_CRAFTING_JEWELLERY = 178;
	public static final int SKILL_GUIDE_CRAFTING_STAFFS = 179;
	public static final int SKILL_GUIDE_FISHING = 180;
	public static final int SKILL_GUIDE_FLETCHING_BOWS = 181;
	public static final int SKILL_GUIDE_FLETCHING_ARROWS = 182;
	public static final int SKILL_GUIDE_FLETCHING_DARTS = 183;
	public static final int SKILL_GUIDE_FLETCHING_BOLTS = 184;
	public static final int SKILL_GUIDE_HERBLORE_HERBS = 185;
	public static final int SKILL_GUIDE_HERBLORE_POTIONS = 186;
	public static final int SKILL_GUIDE_MINING_ORES = 187;
	public static final int SKILL_GUIDE_MINING_PICKAXES = 188;
	public static final int SKILL_GUIDE_RANGED_BOWS = 189;
	public static final int SKILL_GUIDE_RANGED_THROWN = 190;
	public static final int SKILL_GUIDE_RANGED_ARMOUR = 191;
	public static final int SKILL_GUIDE_RUNECRAFTING = 192;
	public static final int SKILL_GUIDE_SMITHING_SMELTING = 193;
	public static final int SKILL_GUIDE_SMITHING_BRONZE = 194;
	public static final int SKILL_GUIDE_SMITHING_IRON = 195;
	public static final int SKILL_GUIDE_SMITHING_STEEL = 196;
	public static final int SKILL_GUIDE_SMITHING_MITHRIL = 197;
	public static final int SKILL_GUIDE_SMITHING_ADAMANT = 198;
	public static final int SKILL_GUIDE_SMITHING_RUNE = 199;
	public static final int SKILL_GUIDE_THIEVING_STALLS = 200;
	public static final int SKILL_GUIDE_THIEVING_PICKPOCKET = 201;
	public static final int SKILL_GUIDE_CRAFTING_ARMOUR = 202;
	public static final int SKILL_GUIDE_WOODCUTTING = 203;
	public static final int RAZMIREGENERALSTORE = 204;
	public static final int RAZMIREBUILDINGSTORE = 205;
	public static final int SKILL_GUIDE_MAGIC_ARMOUR = 206;
	public static final int TRAIL_PUZZLEHINTINV = 207;
	public static final int VIKING_CLOTHES_SHOP = 208;
	public static final int VIKING_BAR = 209;
	public static final int VIKING_WEAPONS_SHOP = 210;
	public static final int VIKING_GENERAL_STORE = 211;
	public static final int VIKING_FISHMONGER = 212;
	public static final int VIKING_FURSHOP = 213;
	public static final int LIGHTHOUSESHOP = 214;
	public static final int BOARDGAMES_BOARDINV = 215;
	public static final int BOARDGAMES_SIDEINV = 216;
	public static final int MISC_FISHMONGER = 217;
	public static final int MISC_GREENGROCER = 218;
	public static final int ETC_FISHMONGER = 219;
	public static final int ETC_GREENGROCER = 220;
	public static final int REINITIALISATION_INV = 221;
	public static final int REINITIALISATION_INV_INACTIVE = 222;
	public static final int MM_MAGIC_SHOP = 223;
	public static final int MM_SCIMITAR_SHOP = 224;
	public static final int MM_FOOD_SHOP = 225;
	public static final int MM_CRAFTING_SHOP = 226;
	public static final int MM_GENERAL_SHOP = 227;
	public static final int RANGINGGUILD_TRADE_TICKETS = 228;
	public static final int SKILL_GUIDE_SLAYER_MONSTERS = 229;
	public static final int SKILL_GUIDE_SLAYER_EQUIPMENT = 230;
	public static final int SLAYERSHOP = 231;
	public static final int MAGICGUILDSHOP2 = 232;
	public static final int GENERALSHOP_PHASMATYS = 233;
	public static final int SKILL_GUIDE_COOKING_HOTDRINKS = 234;
	public static final int AHOY_GENERALSHOP = 235;
	public static final int WILDERNESSCAPESHOP1 = 236;
	public static final int WILDERNESSCAPESHOP2 = 237;
	public static final int WILDERNESSCAPESHOP3 = 238;
	public static final int WILDERNESSCAPESHOP4 = 239;
	public static final int WILDERNESSCAPESHOP5 = 240;
	public static final int WILDERNESSCAPESHOP6 = 241;
	public static final int WILDERNESSCAPESHOP7 = 242;
	public static final int WILDERNESSCAPESHOP8 = 243;
	public static final int WILDERNESSCAPESHOP9 = 244;
	public static final int WILDERNESSCAPESHOP10 = 245;
	public static final int CASTLEWARS_TRADE_TICKETS = 246;
	public static final int CASTLEWARS_TRADE_COINS = 247;
	public static final int SMITHING_LAMP_IRON = 248;
	public static final int SMITHING_LAMP_STEEL = 249;
	public static final int SALESMAN_RANGING = 250;
	public static final int MM_SCIMITAR_SHOP2 = 251;
	public static final int FEUD_MORRISANES = 252;
	public static final int FEUD_ALISPUB = 253;
	public static final int POLLNIVNEACH_GENERALSTORE = 254;
	public static final int DT_BANDIT_SHOP = 255;
	public static final int RASOOLSHOP1 = 256;
	public static final int FD_BANDIT_BAR = 257;
	public static final int SOPHANEM_CLOTH_STORE = 258;
	public static final int AHOY_AKHARANU_SHOP = 259;
	public static final int UGLUG_NAR_SHOP = 260;
	public static final int KELDAGRIM_WARHAMMER_SHOP = 261;
	public static final int KELDAGRIM_QUALITY_WEAPONS_SHOP = 262;
	public static final int KELDAGRIM_QUALITY_ARMOUR_SHOP = 263;
	public static final int KELDAGRIM_GENERAL_SHOP = 264;
	public static final int KELDAGRIM_PICKAXE_SHOP = 265;
	public static final int KELDAGRIM_CLOTHES_SHOP = 266;
	public static final int KELDAGRIM_BREAD_STALL = 267;
	public static final int KELDAGRIM_CRAFTING_STALL = 268;
	public static final int KELDAGRIM_GEM_STALL = 269;
	public static final int KELDAGRIM_SILVER_STALL = 270;
	public static final int KELDAGRIM_CLOTHES_STALL = 271;
	public static final int MINECART_TEMP_INV = 272;
	public static final int SEED_STALL = 273;
	public static final int SKILL_GUIDE_THIEVING_CHESTS = 274;
	public static final int SKILL_GUIDE_PRAYER = 275;
	public static final int DARKRUNESHOP_CRAP = 276;
	public static final int DARKRUNESHOP_UBER = 277;
	public static final int ROGUESDEN_PUZZLE_ROTATION = 278;
	public static final int ROGUESDEN_SHOP = 279;
	public static final int FARMING_SHOP_1 = 280;
	public static final int FARMING_SHOP_2 = 281;
	public static final int FARMING_SHOP_3 = 282;
	public static final int FARMING_SHOP_4 = 283;
	public static final int SKILL_GUIDE_COOKING_BREWING = 284;
	public static final int SKILL_GUIDE_COOKING_VEGETABLES = 285;
	public static final int SKILL_GUIDE_CRAFTING_WEAVING = 286;
	public static final int SKILL_GUIDE_FARMING_VEG = 287;
	public static final int SKILL_GUIDE_FARMING_HOPS = 288;
	public static final int SKILL_GUIDE_FARMING_TREES = 289;
	public static final int SKILL_GUIDE_FARMING_FRUIT_TREES = 290;
	public static final int SKILL_GUIDE_FARMING_HERBS = 291;
	public static final int SKILL_GUIDE_FARMING_FLOWERS = 292;
	public static final int SKILL_GUIDE_FARMING_BUSHES = 293;
	public static final int SKILL_GUIDE_FARMING_SPECIAL = 294;
	public static final int SKILL_GUIDE_FARMING_MUSHROOM = 295;
	public static final int SKILL_GUIDE_FARMING_CACTUS = 296;
	public static final int SKILL_GUIDE_FARMING_CALQUAT = 297;
	public static final int SKILL_GUIDE_FARMING_SPIRIT_TREE = 298;
	public static final int SKILL_GUIDE_FARMING_SCARECROW = 299;
	public static final int SKILL_GUIDE_FARMING_BELLADONNA = 300;
	public static final int LLETYAGENERALSHOP1 = 301;
	public static final int LLETYASEAMSTRESSSHOP1 = 302;
	public static final int LLETYAARCHERYSHOP1 = 303;
	public static final int LLETYAFOODSHOP1 = 304;
	public static final int TAI_BWO_WANNAI_COOPERATIVE = 305;
	public static final int TAI_BWO_WANNAI_DRINKSIES = 306;
	public static final int MACRO_CERTER = 307;
	public static final int ROGUETRADER_TOUGHSUDUKUINV = 308;
	public static final int ROGUETRADER_ALIM_DEFENDBJ_INV = 309;
	public static final int ROGUETRADER_ALIM_ASSAULTBJ_INV = 310;
	public static final int ROGUETRADER_ALIM_MEANPCLOTHES_INV = 311;
	public static final int ROGUETRADER_ALIM_CARPETCLOTHES_INV = 312;
	public static final int ROGUETRADER_ALIM_RUNERETAIL_INV = 313;
	public static final int ROGUETRADER_ALIM_RUNEWHOLESALE_INV = 314;
	public static final int ROGUETRADER_ALIM_RUNEDUMP = 315;
	public static final int BLAST_FURNACE_INV = 316;
	public static final int BLAST_FURNACE_BARS_INV = 317;
	public static final int SMITHING_GUILD_ORE_SELLER = 318;
	public static final int SMITHING_GUILD_BUYER = 319;
	public static final int PICKCATINV = 320;
	public static final int SKILL_GUIDE_AGILITY_COURSES = 321;
	public static final int SKILL_GUIDE_AGILITY_AREAS = 322;
	public static final int SKILL_GUIDE_AGILITY_SHORTCUTS = 323;
	public static final int TZHAAR_SHOP_GENERAL = 324;
	public static final int TZHAAR_SHOP_EQUIPMENT = 325;
	public static final int TZHAAR_SHOP_OREANDGEM = 326;
	public static final int TZHAAR_SHOP_RUNE = 327;
	public static final int WHITEKNIGHT_ARMOURY1 = 328;
	public static final int WHITEKNIGHT_ARMOURY2 = 329;
	public static final int WHITEKNIGHT_ARMOURY3 = 330;
	public static final int WHITEKNIGHT_ARMOURY4 = 331;
	public static final int WHITEKNIGHT_ARMOURY5 = 332;
	public static final int WHITEKNIGHT_ARMOURY6 = 333;
	public static final int PILLORY_ROTTENFRUITSHOP = 334;
	public static final int SKILL_GUIDE_COOKING_DAIRY = 335;
	public static final int SKILL_GUIDE_WOODCUTTING_AXES = 336;
	public static final int SILVERCAST_HOLYSYMBOL = 337;
	public static final int SILVERCAST_UNHOLYSYMBOL = 338;
	public static final int SILVERCAST_SICKLE = 339;
	public static final int SILVERCAST_LIGHTNING = 340;
	public static final int SILVERCAST_TIARA = 341;
	public static final int SILVERCAST_AGRITH = 342;
	public static final int GENERALSHOPNARDAH = 343;
	public static final int ARMOURSHOPNARDAH = 344;
	public static final int CHOCICESHOPNARDAH = 345;
	public static final int BAKERY2 = 346;
	public static final int MAGICTRAINING_INVENTORY = 347;
	public static final int SMITHSSHOP = 348;
	public static final int JOESHOP = 349;
	public static final int CHARLEYSHOP = 350;
	public static final int MIKESHOP = 351;
	public static final int SHOP_PIES = 352;
	public static final int SKILL_GUIDE_WOODCUTTING_CANOES = 353;
	public static final int FARMING_TOOLS_FAIRYVERSION = 354;
	public static final int WIELDED_WEAPON_INV = 355;
	public static final int HUNDRED_FOODCHEST1 = 356;
	public static final int HUNDRED_FOODCHEST2 = 357;
	public static final int HUNDRED_FOODCHEST3 = 358;
	public static final int HUNDRED_FOODCHEST4 = 359;
	public static final int HUNDRED_FOODCHEST5 = 360;
	public static final int HUNDRED_FOODCHEST6 = 361;
	public static final int HUNDRED_FOODCHEST7 = 362;
	public static final int HUNDRED_FOODCHEST8 = 363;
	public static final int HUNDRED_FOODCHEST9 = 364;
	public static final int HUNDRED_FOODCHEST10 = 365;
	public static final int HUNDRED_REWARDCHEST1 = 366;
	public static final int HUNDRED_REWARDCHEST2 = 367;
	public static final int HUNDRED_REWARDCHEST3 = 368;
	public static final int HUNDRED_REWARDCHEST4 = 369;
	public static final int HUNDRED_REWARDCHEST5 = 370;
	public static final int HUNDRED_REWARDCHEST6 = 371;
	public static final int HUNDRED_REWARDCHEST7 = 372;
	public static final int HUNDRED_REWARDCHEST8 = 373;
	public static final int HUNDRED_REWARDCHEST9 = 374;
	public static final int HUNDRED_REWARDCHEST10 = 375;
	public static final int HUNDRED_QUEST_JOURNAL = 376;
	public static final int _100GUIDE_FLOURINV1 = 377;
	public static final int _100GUIDE_FLOURINV2 = 378;
	public static final int _100GUIDE_FLOURINV3 = 379;
	public static final int _100GUIDE_FLOURINV4 = 380;
	public static final int _100GUIDE_FLOURINV5 = 381;
	public static final int SILVERCAST_COMMANDROD = 382;
	public static final int BURGH_GENERAL_STORE = 383;
	public static final int WINE_MERCHANT = 384;
	public static final int WINE_VINEGAR_MERCHANT = 385;
	public static final int WINE_MERCHANT_FREE = 386;
	public static final int PEST_ARCHERY_STORE = 387;
	public static final int PEST_RUNE_STORE = 388;
	public static final int PEST_GENERAL_STORE = 389;
	public static final int MISC_RESOURCES_COLLECTED = 390;
	public static final int ROYAL_GENERALSTORE = 391;
	public static final int ROYAL_CLOTHESHOP = 392;
	public static final int ROYAL_BAR = 393;
	public static final int ROYAL_FOODSHOP = 394;
	public static final int POH_SAWMILL_SHOP = 395;
	public static final int POH_STONEMASON_SHOP = 396;
	public static final int POH_GARDEN_CENTRE = 397;
	public static final int POH_FURNITURE_MENU_INV = 398;
	public static final int SKILL_GUIDE_CARPENTRY_ROOMS = 399;
	public static final int SKILL_GUIDE_CARPENTRY_SEATING = 400;
	public static final int SKILL_GUIDE_CARPENTRY_STORAGE = 401;
	public static final int SKILL_GUIDE_CARPENTRY_SKILLS = 402;
	public static final int SKILL_GUIDE_CARPENTRY_DECORATIVE = 403;
	public static final int SKILL_GUIDE_CARPENTRY_GAMES = 404;
	public static final int SKILL_GUIDE_CARPENTRY_GARDEN = 405;
	public static final int SKILL_GUIDE_CARPENTRY_MISC = 406;
	public static final int SKILL_GUIDE_CARPENTRY_CHAPEL = 407;
	public static final int SKILL_GUIDE_CARPENTRY_DUNGEON = 408;
	public static final int SKILL_GUIDE_CARPENTRY_TROPHIES = 409;
	public static final int WARGUILD_FOOD_SHOP = 410;
	public static final int WARGUILD_ARMOUR_SHOP = 411;
	public static final int WARGUILD_POTION_SHOP = 412;
	public static final int DORGESHUUN_WEAPON_SHOP = 413;
	public static final int BREW_REWARD_SHOP = 414;
	public static final int SKILL_GUIDE_THIEVING_OTHER = 415;
	public static final int LUNAR_GENERAL = 416;
	public static final int LUNAR_CLOTHESHOP = 417;
	public static final int LUNAR_RUNESHOP = 418;
	public static final int LUNAR_RUNESHOP_COMPL = 419;
	public static final int SKILL_GUIDE_RANGED_SHORTCUTS = 420;
	public static final int SKILL_GUIDE_STRENGTH_WEAPONS_AND_ARMOUR = 421;
	public static final int SKILL_GUIDE_STRENGTH_SHORTCUTS = 422;
	public static final int SKILL_GUIDE_FLETCHING_CBOWS = 423;
	public static final int SKILL_GUIDE_MAGIC_BOLTS = 424;
	public static final int SKILL_GUIDE_RANGED_CROSSBOWS = 425;
	public static final int SKILL_GUIDE_SMITHING_BLURITE = 426;
	public static final int XBOWS_SHOP = 427;
	public static final int XBOWS_SHOP_ADDY = 428;
	public static final int SILVERCAST_XBOWS = 429;
	public static final int SMITHING_BRONZE6 = 430;
	public static final int SMITHING_IRON6 = 431;
	public static final int SMITHING_STEEL6 = 432;
	public static final int SMITHING_MITHRIL6 = 433;
	public static final int XBOWS_GRAPPLE_INV = 434;
	public static final int SMITHING_ADAMANT6 = 435;
	public static final int SMITHING_RUNE6 = 436;
	public static final int SKILL_GUIDE_COOKING_GNOME = 437;
	public static final int EYEGLO_INV_IN = 438;
	public static final int EYEGLO_INV_OUT = 439;
	public static final int EYEGLO_INV_SIDE = 440;
	public static final int TRADER_STAN_SHOP = 441;
	public static final int MEIYERDITCH_BLACK_MARKET = 442;
	public static final int TEMPLEKNIGHT_ARMOURY1 = 443;
	public static final int TEMPLEKNIGHT_ARMOURY2 = 444;
	public static final int POH_COSTUME_MENU_INV = 445;
	public static final int POH_COSTUME_ROOM_MAGIC_WARDROBE_INV = 446;
	public static final int POH_COSTUME_ROOM_ARMOUR_INV = 447;
	public static final int POH_COSTUME_ROOM_AME_INV = 448;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_1_INV = 449;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_2_INV = 450;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_3_INV = 451;
	public static final int POH_COSTUME_ROOM_CAPES_INV = 452;
	public static final int POH_COSTUME_ROOM_HOLIDAY_ITEMS_INV = 453;
	public static final int SKILL_GUIDE_CARPENTRY_COSTUME_ROOM = 454;
	public static final int ROGUESDEN_SHOP_SKILLCAPE = 455;
	public static final int ROGUESDEN_SHOP_SKILLCAPE_TRIMMED = 456;
	public static final int RANGING_GUILD_ARMOURSHOP_SKILLCAPE = 457;
	public static final int RANGING_GUILD_ARMOURSHOP_SKILLCAPE_TRIMMED = 458;
	public static final int RUNESHOP_SKILLCAPE = 459;
	public static final int RUNESHOP_SKILLCAPE_TRIMMED = 460;
	public static final int MAGICGUILDSHOP2_SKILLCAPE = 461;
	public static final int MAGICGUILDSHOP2_SKILLCAPE_TRIMMED = 462;
	public static final int RANGING_GUILD_BOWSHOP_SKILLCAPE = 463;
	public static final int RANGING_GUILD_BOWSHOP_SKILLCAPE_TRIMMED = 464;
	public static final int MYARM_SHOP = 465;
	public static final int ARCHERYSHOP2_SKILLCAPE = 466;
	public static final int ARCHERYSHOP2_SKILLCAPE_TRIMMED = 467;
	public static final int DIANGO_HOLS_SACK = 468;
	public static final int SKILL_GUIDE_HUNTING_TRACKING = 469;
	public static final int SKILL_GUIDE_HUNTING_BIRDS = 470;
	public static final int SKILL_GUIDE_HUNTING_BUTTERFLIES = 471;
	public static final int SKILL_GUIDE_HUNTING_DEADFALLS = 472;
	public static final int SKILL_GUIDE_HUNTING_BOXTRAPS = 473;
	public static final int SKILL_GUIDE_HUNTING_NETTRAPS = 474;
	public static final int SKILL_GUIDE_HUNTING_PITFALLS = 475;
	public static final int SKILL_GUIDE_HUNTING_FALCONRY = 476;
	public static final int SKILL_GUIDE_HUNTING_IMPBOXES = 477;
	public static final int SKILL_GUIDE_HUNTING_RABBITS = 478;
	public static final int SKILL_GUIDE_HUNTING_EAGLES = 479;
	public static final int SKILL_GUIDE_HUNTING_TRAPS = 480;
	public static final int SKILL_GUIDE_HUNTING_CLOTHING = 481;
	public static final int HUNTING_CUSTOMFURSHOP = 482;
	public static final int HUNTING_SHOP_YANILLE = 483;
	public static final int HUNTING_SHOP_NARDAH = 484;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_1A_INV = 485;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_2A_INV = 486;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_3A_INV = 487;
	public static final int POH_COSTUME_ROOM_CAPES_INV_PAGE2 = 488;
	public static final int ANMA_SHOP = 489;
	public static final int BARBASSAULT_EGGINV = 490;
	public static final int CONTACT_CRAFTINGSHOP = 491;
	public static final int CONTACT_BAKERY = 492;
	public static final int CONTACT_DAGGERSHOP = 493;
	public static final int CONTACT_EMBALMER = 494;
	public static final int SOPHANEM_CLOTH_STORE_UPDATED = 495;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_1_INV_CHECK = 496;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_1A_INV_CHECK = 497;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_2_INV_CHECK = 498;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_2A_INV_CHECK = 499;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_3_INV_CHECK = 500;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_3A_INV_CHECK = 501;
	public static final int FRISB_N_SHOP = 502;
	public static final int FRISD_FISHMONGER = 503;
	public static final int FRISD_COOK = 504;
	public static final int FRISD_ARMOURSHOP = 505;
	public static final int FRISD_WEAPONSHOP = 506;
	public static final int FRISD_ORESHOP = 507;
	public static final int FRISD_YAK_CONTRABAND = 508;
	public static final int TTREK_FOLLOWER_INV = 509;
	public static final int DORGESH_FOOD_SOLD = 510;
	public static final int DORGESH_LAMP_SHOP = 511;
	public static final int DORGESH_LEATHER_SHOP = 512;
	public static final int DORGESH_GENERAL_STORE = 513;
	public static final int DREAM_BANK_INVENTORY = 514;
	public static final int DREAM_CRATE_INVENTORY = 515;
	public static final int LOOTING_BAG = 516;
	public static final int POH_MENAGERIE_PETS = 517;
	public static final int TRADINGPOST_SELL_0 = 518;
	public static final int TRADINGPOST_SELL_1 = 519;
	public static final int TRADINGPOST_SELL_2 = 520;
	public static final int TRADINGPOST_SELL_3 = 521;
	public static final int TRADINGPOST_SELL_4 = 522;
	public static final int TRADINGPOST_SELL_5 = 523;
	public static final int TRADINGPOST_DISPLAY = 524;
	public static final int GRAVESTONE = 525;
	public static final int GE_OFFER_0 = 526;
	public static final int GE_OFFER_1 = 527;
	public static final int GE_OFFER_2 = 528;
	public static final int GE_OFFER_3 = 529;
	public static final int GE_OFFER_4 = 530;
	public static final int GE_OFFER_5 = 531;
	public static final int MICROT_DROPBOX = 532;
	public static final int MICROT_DELBOX = 533;
	public static final int BONDS_ESCROW = 534;
	public static final int BONDS_REDEEM_DUMMY = 535;
	public static final int BONDS_POUCH = 536;
	public static final int GE_OFFER_6 = 537;
	public static final int GE_OFFER_7 = 538;
	public static final int GE_COLLECT_6 = 539;
	public static final int GE_COLLECT_7 = 540;
	public static final int DUELDISPLAY_DUMMY = 541;
	public static final int DEADMAN_AXESHOP = 542;
	public static final int DEADMAN_SCIMITARSHOP = 543;
	public static final int DEADMAN_CRAFTINGSHOP2 = 544;
	public static final int DEADMAN_LEGSSHOP = 545;
	public static final int DEADMAN_SWORDSHOP = 546;
	public static final int DEADMAN_ARMOURSHOP = 547;
	public static final int DEADMAN_ARCHERYSHOP = 548;
	public static final int DEADMAN_SKIRTSHOP = 549;
	public static final int DEADMAN_FISHINGSHOP = 550;
	public static final int DEADMAN_MEMBERSTAFFSHOP = 551;
	public static final int DEADMAN_HELMETSHOP = 552;
	public static final int DEADMANLOSE = 553;
	public static final int DEADMANLOSE_DUMMY = 554;
	public static final int DEADMANPROTECT = 555;
	public static final int DEADMANCERT = 556;
	public static final int DEADMANKEY = 557;
	public static final int DEADMAN_LOOT_INV0 = 558;
	public static final int DEADMAN_LOOT_INV1 = 559;
	public static final int DEADMAN_LOOT_INV2 = 560;
	public static final int DEADMAN_LOOT_INV3 = 561;
	public static final int DEADMAN_LOOT_INV4 = 562;
	public static final int DEADMAN_SAFEBOX = 563;
	public static final int DEADMAN_SAFEBOX_SPLIT = 564;
	public static final int PISCARILIUS_FISH_TRADER_0 = 565;
	public static final int PISCARILIUS_FISH_TRADER_250 = 566;
	public static final int PISCARILIUS_FISH_TRADER_500 = 567;
	public static final int PISCARILIUS_FISH_TRADER_750 = 568;
	public static final int PISCARILIUS_FISH_TRADER_1000 = 569;
	public static final int PISCARILIUS_FISHING_SUPPLIES = 570;
	public static final int PISCARILIUS_GENERALSTORE = 571;
	public static final int BANK_HOLDINGINV = 572;
	public static final int SEED_BOX = 573;
	public static final int MM2_JAVELIN_STORE = 574;
	public static final int LOVAKENGJ_DYNAMITE_SHOP = 575;
	public static final int HH_INV = 576;
	public static final int BR_REWARDINV = 577;
	public static final int KOURENDGEMSTALL = 578;
	public static final int KOURENDGEMSTORE = 579;
	public static final int BAKERY3 = 580;
	public static final int RAIDS_REWARDS = 581;
	public static final int RAIDS_SHAREDSTORAGE = 582;
	public static final int RAIDS_PRIVATESTORAGE = 583;
	public static final int DEATHKEEP_ITEMS = 584;
	public static final int TZHAAR_SHOP_CITYEQUIPMENT = 585;
	public static final int TZHAAR_SHOP_CITYOREANDGEM = 586;
	public static final int MGUILD_ORESHOP = 587;
	public static final int MGUILD_PICKAXESHOP = 588;
	public static final int MGUILD_REWARDSHOP = 589;
	public static final int FOSSIL_GENERAL_STORE = 590;
	public static final int DEADMAN_ARCHERYSHOP2 = 591;
	public static final int DEADMAN_ADVENTURESHOP = 592;
	public static final int DEADMAN_PUB = 593;
	public static final int DEADMAN_SHANTAYSHOP = 594;
	public static final int DEADMAN_RANGING_GUILD_BOWSHOP = 595;
	public static final int DEADMAN_GEMSHOP = 596;
	public static final int DEADMAN_GEMSHOP2 = 597;
	public static final int DEADMAN_GEMSTALL = 598;
	public static final int DEADMAN_KOURENDGEMSTALL = 599;
	public static final int WARRENS_FISHMONGER = 600;
	public static final int MYTHS_GUILD_WEAPONRY = 601;
	public static final int MYTHS_GUILD_ARMOURY = 602;
	public static final int MYTHS_GUILD_CAPES = 603;
	public static final int MYTHS_GUILD_HERBALIST = 604;
	public static final int DEADMAN_SALESMAN_RANGING = 605;
	public static final int DEADMAN_PEST_ARCHERY_STORE = 606;
	public static final int DEADMAN_PEST_RUNE_STORE = 607;
	public static final int DEADMAN_LUNAR_RUNESHOP = 608;
	public static final int DEADMAN_LUNAR_RUNESHOP_COMPL = 609;
	public static final int DEADMAN_LLETYAARCHERYSHOP1 = 610;
	public static final int SPECTATOR_INVENTORY = 611;
	public static final int TOB_CHESTS = 612;
	public static final int APRILFOOLSHORSESHOP_DEADMAN = 613;
	public static final int DUELTAX = 614;
	public static final int DUELHOLDING_DUMMY = 615;
	public static final int DEADMAN_ARCHERYSHOP2_SKILLCAPE = 616;
	public static final int DEADMAN_ARCHERYSHOP2_SKILLCAPE_TRIMMED = 617;
	public static final int BOT_BUSTING_LOOT = 618;
	public static final int TELENEXUS_COST = 619;
	public static final int COLLECTION_TRANSMIT = 620;
	public static final int KEBOS_POH_GARDEN_CENTRE = 621;
	public static final int KEBOS_FARMING_EQUIPMENT_SHOP = 622;
	public static final int KEBOS_WEAPON_SHOP = 623;
	public static final int KEBOS_FARMING_SEED_SHOP = 624;
	public static final int KEBOS_FARMING_SEED_IRON_SHOP = 625;
	public static final int SEED_VAULT = 626;
	public static final int POH_COSTUME_ROOM_TREASURE_TRAIL_0_INV = 627;
	public static final int GAUNTLET_HOLDING = 628;
	public static final int PRIF_FOOD_STORE = 629;
	public static final int PRIF_MACE_STORE = 630;
	public static final int PRIF_WEAPON_STORE = 631;
	public static final int PRIF_LEIGH_STORE = 632;
	public static final int LEAGUES_HOLDING = 633;
	public static final int GENERALSHOP9_EASTER = 634;
	public static final int HALLOWED_REWARDSHOP = 635;
	public static final int DEATH_PERMANENT = 636;
	public static final int POH_COSTUMES = 637;
	public static final int BANKPIN_INV = 638;
	public static final int SLAYERSHOP_PRE_PORCINE = 639;
	public static final int LEAGUE_TRAILBLAZER_GENERALSHOP1 = 640;
	public static final int LEAGUE_TRAILBLAZER_SLAYERSHOP = 641;
	public static final int LEAGUE_TRAILBLAZER_SLAYERSHOP_PREPORCINE = 642;
	public static final int LEAGUE_TRAILBLAZER_CATHERBY_CHARTER = 643;
	public static final int LEAGUE_TRAILBLAZER_PORT_KHAZARD_CHARTER = 644;
	public static final int LEAGUE_TRAILBLAZER_VARROCK_ARMOUR_SHOP = 645;
	public static final int LEAGUE_SECONDINV = 646;
	public static final int II_SHOP = 647;
	public static final int ROGUESDEN_SHOP_IRON = 648;
	public static final int ROGUESDEN_SHOP_SKILLCAPE_IRON = 649;
	public static final int ROGUESDEN_SHOP_SKILLCAPE_TRIMMED_IRON = 650;
	public static final int TACKLE_BOX = 651;
	public static final int CUTSCENE_EMPTY = 652;
	public static final int DEADMAN_SHAYZIEN_RANGESHOP = 653;
	public static final int SHAYZIEN_RANGESHOP = 654;
	public static final int SHAYZIEN_PUB = 655;
	public static final int SHAYZIEN_CLOTHESSHOP = 656;
	public static final int WARGUILD_FOOD_SHOP_UIM = 657;
	public static final int WARGUILD_FOOD_SHOP_GIM = 658;
	public static final int INV_GROUP_TEMP = 659;
	public static final int INV_PLAYER_TEMP = 660;
	public static final int INV_PLAYER_SNAPSHOT = 661;
	public static final int TRADER_STAN_BRIMHAVEN_GIM = 662;
	public static final int TRADER_STAN_CATHERBY_GIM = 663;
	public static final int TRADER_STAN_HARMLESS_GIM = 664;
	public static final int TRADER_STAN_MUSA_GIM = 665;
	public static final int TRADER_STAN_KHAZARD_GIM = 666;
	public static final int TRADER_STAN_PHASMATYS_GIM = 667;
	public static final int TRADER_STAN_SHOP_UIM = 668;
	public static final int TRADER_STAN_SHOP_GIM = 669;
	public static final int SKILL_GUIDE_FIREMAKING_UIM = 670;
	public static final int SMITHING_GUILD_ORE_SELLER_UIM = 671;
	public static final int SMITHING_GUILD_ORE_SELLER_GIM = 672;
	public static final int UPASSGENERALSHOP_UIM = 673;
	public static final int UPASSGENERALSHOP_GIM = 674;
	public static final int TBWT_TIADECHE_FINAL_UIM = 675;
	public static final int TBWT_TIADECHE_FINAL_GIM = 676;
	public static final int LLETYAARCHERYSHOP_UIM = 677;
	public static final int LLETYAARCHERYSHOP_GIM = 678;
	public static final int KARAMJA_FISHRESTAURANT_UIM = 679;
	public static final int KARAMJA_FISHRESTAURANT_GIM = 680;
	public static final int ROGUETRADER_ALIM_RUNERETAIL_UIM = 681;
	public static final int ROGUETRADER_ALIM_RUNERETAIL_GIM = 682;
	public static final int ROGUETRADER_ALIM_RUNEWHOLESALE_UIM = 683;
	public static final int ROGUETRADER_ALIM_RUNEWHOLESALE_GIM = 684;
	public static final int HUNDRED_FOODCHEST1_UIM = 685;
	public static final int HUNDRED_FOODCHEST1_GIM = 686;
	public static final int HUNDRED_FOODCHEST2_UIM = 687;
	public static final int HUNDRED_FOODCHEST2_GIM = 688;
	public static final int HUNDRED_FOODCHEST3_UIM = 689;
	public static final int HUNDRED_FOODCHEST3_GIM = 690;
	public static final int HUNDRED_FOODCHEST4_UIM = 691;
	public static final int HUNDRED_FOODCHEST4_GIM = 692;
	public static final int HUNDRED_FOODCHEST5_UIM = 693;
	public static final int HUNDRED_FOODCHEST5_GIM = 694;
	public static final int HUNDRED_FOODCHEST6_UIM = 695;
	public static final int HUNDRED_FOODCHEST6_GIM = 696;
	public static final int HUNDRED_FOODCHEST7_UIM = 697;
	public static final int HUNDRED_FOODCHEST7_GIM = 698;
	public static final int HUNDRED_FOODCHEST8_UIM = 699;
	public static final int HUNDRED_FOODCHEST8_GIM = 700;
	public static final int HUNDRED_FOODCHEST9_UIM = 701;
	public static final int HUNDRED_FOODCHEST9_GIM = 702;
	public static final int HUNDRED_FOODCHEST10_UIM = 703;
	public static final int HUNDRED_FOODCHEST10_GIM = 704;
	public static final int ANMA_SHOP_UIM = 705;
	public static final int ANMA_SHOP_GIM = 706;
	public static final int LUNAR_RUNESHOP_UIM = 707;
	public static final int LUNAR_RUNESHOP_GIM = 708;
	public static final int LUNAR_RUNESHOP_COMPL_UIM = 709;
	public static final int LUNAR_RUNESHOP_COMPL_GIM = 710;
	public static final int MAGEARENA_RUNESHOP_UIM = 711;
	public static final int MAGEARENA_RUNESHOP_GIM = 712;
	public static final int DARKRUNESHOP_CRAP_UIM = 713;
	public static final int DARKRUNESHOP_CRAP_GIM = 714;
	public static final int DARKRUNESHOP_UBER_UIM = 715;
	public static final int DARKRUNESHOP_UBER_GIM = 716;
	public static final int KELDAGRIM_QUALITY_WEAPONS_SHOP_UIM = 717;
	public static final int KELDAGRIM_QUALITY_WEAPONS_SHOP_GIM = 718;
	public static final int KELDAGRIM_GEM_STALL_UIM = 719;
	public static final int KELDAGRIM_GEM_STALL_GIM = 720;
	public static final int PISCARILIUS_FISH_TRADER_0_UIM = 721;
	public static final int PISCARILIUS_FISH_TRADER_0_GIM = 722;
	public static final int PISCARILIUS_FISH_TRADER_250_UIM = 723;
	public static final int PISCARILIUS_FISH_TRADER_250_GIM = 724;
	public static final int PISCARILIUS_FISH_TRADER_500_UIM = 725;
	public static final int PISCARILIUS_FISH_TRADER_500_GIM = 726;
	public static final int PISCARILIUS_FISH_TRADER_750_UIM = 727;
	public static final int PISCARILIUS_FISH_TRADER_750_GIM = 728;
	public static final int PISCARILIUS_FISH_TRADER_1000_UIM = 729;
	public static final int PISCARILIUS_FISH_TRADER_1000_GIM = 730;
	public static final int TZHAAR_SHOP_OREANDGEM_UIM = 731;
	public static final int TZHAAR_SHOP_OREANDGEM_GIM = 732;
	public static final int TZHAAR_SHOP_CITYOREANDGEM_UIM = 733;
	public static final int TZHAAR_SHOP_CITYOREANDGEM_GIM = 734;
	public static final int GEMSHOP_UIM = 735;
	public static final int GEMSHOP_GIM = 736;
	public static final int GEMSHOP2_UIM = 737;
	public static final int GEMSHOP2_GIM = 738;
	public static final int SILVERSHOP_UIM = 739;
	public static final int SILVERSHOP_GIM = 740;
	public static final int ARDOUGNEGEMSTALL_UIM = 741;
	public static final int ARDOUGNEGEMSTALL_GIM = 742;
	public static final int KOURENDGEMSTALL_UIM = 743;
	public static final int KOURENDGEMSTALL_GIM = 744;
	public static final int GNOMESHOP_GULLUCK_UIM = 745;
	public static final int GNOMESHOP_GULLUCK_GIM = 746;
	public static final int SEED_STALL_UIM = 747;
	public static final int SEED_STALL_GIM = 748;
	public static final int KEBOS_FARMING_SEED_SHOP_UIM = 749;
	public static final int KEBOS_FARMING_SEED_SHOP_GIM = 750;
	public static final int RUNESHOP_UIM = 751;
	public static final int RUNESHOP_GIM = 752;
	public static final int RUNESHOP_SKILLCAPE_UIM = 753;
	public static final int RUNESHOP_SKILLCAPE_GIM = 754;
	public static final int RUNESHOP_SKILLCAPE_TRIMMED_UIM = 755;
	public static final int RUNESHOP_SKILLCAPE_TRIMMED_GIM = 756;
	public static final int MAGICSHOP_UIM = 757;
	public static final int MAGICSHOP_GIM = 758;
	public static final int MAGICGUILDSHOP_UIM = 759;
	public static final int MAGICGUILDSHOP_GIM = 760;
	public static final int ARCHERYSHOP_UIM = 761;
	public static final int ARCHERYSHOP_GIM = 762;
	public static final int SALESMAN_RANGING_UIM = 763;
	public static final int SALESMAN_RANGING_GIM = 764;
	public static final int ARCHERYSHOP2_UIM = 765;
	public static final int ARCHERYSHOP2_GIM = 766;
	public static final int ARCHERYSHOP2_SKILLCAPE_UIM = 767;
	public static final int ARCHERYSHOP2_SKILLCAPE_GIM = 768;
	public static final int ARCHERYSHOP2_SKILLCAPE_TRIMMED_UIM = 769;
	public static final int ARCHERYSHOP2_SKILLCAPE_TRIMMED_GIM = 770;
	public static final int STAFFSHOP_UIM = 771;
	public static final int STAFFSHOP_GIM = 772;
	public static final int MEMBERSTAFFSHOP_UIM = 773;
	public static final int MEMBERSTAFFSHOP_GIM = 774;
	public static final int RANGING_GUILD_BOWSHOP_UIM = 775;
	public static final int RANGING_GUILD_BOWSHOP_GIM = 776;
	public static final int ADVENTURERSHOP_UIM = 777;
	public static final int ADVENTURERSHOP_GIM = 778;
	public static final int GNOMESHOP_UIM = 779;
	public static final int GNOMESHOP_GIM = 780;
	public static final int GENERALLEGENDS_UIM = 781;
	public static final int GENERALLEGENDS_GIM = 782;
	public static final int INV_PLAYER_FALLBACK = 783;
	public static final int WINE_MERCHANT_DEFAULT_IRON = 784;
	public static final int WINE_MERCHANT_DEFAULT_UIM = 785;
	public static final int WINE_MERCHANT_DEFAULT_GIM = 786;
	public static final int WINE_VINEGAR_MERCHANT_IRON = 787;
	public static final int WINE_VINEGAR_MERCHANT_UIM = 788;
	public static final int WINE_VINEGAR_MERCHANT_GIM = 789;
	public static final int WINE_MERCHANT_FREE_IRON = 790;
	public static final int WINE_MERCHANT_FREE_UIM = 791;
	public static final int WINE_MERCHANT_FREE_GIM = 792;
	public static final int FEUD_ALISPUB_IM = 793;
	public static final int FEUD_ALISPUB_UIM = 794;
	public static final int FEUD_ALISPUB_GIM = 795;
	public static final int LEAGUE_3_GENERALSHOP1 = 796;
	public static final int LOOT_INV_ACCESS = 797;
	public static final int PVPA_SHOP_INV = 798;
	public static final int GOTR_REWARD_SHOP_ALL = 799;
	public static final int GOTR_REWARD_SHOP_NONE = 800;
	public static final int GOTR_REWARD_SHOP_LAW = 801;
	public static final int GOTR_REWARD_SHOP_DEATH = 802;
	public static final int GOTR_REWARD_SHOP_BLOOD = 803;
	public static final int GOTR_REWARD_SHOP_LAW_DEATH = 804;
	public static final int GOTR_REWARD_SHOP_LAW_BLOOD = 805;
	public static final int GOTR_REWARD_SHOP_DEATH_BLOOD = 806;
	public static final int TOA_MIDRAIDLOOT_BUNDLE1 = 807;
	public static final int TOA_MIDRAIDLOOT_BUNDLE2 = 808;
	public static final int TOA_MIDRAIDLOOT_BUNDLE3 = 809;
	public static final int TOA_MIDRAIDLOOT_BAG = 810;
	public static final int TOA_CHESTS = 811;
	public static final int BH_RISK_CACHE = 812;
	public static final int OMNISHOP_INV_TEMP = 813;
	public static final int FORESTRY_KIT = 814;
	public static final int FORESTRY_SHOP_LOG_STORAGE = 815;
	public static final int DT2_VAULT_SHOP = 816;
	public static final int TRADER_SHOP_PISCARILIUS = 817;
	public static final int TRADER_SHOP_PISCARILIUS_IRON = 818;
	public static final int TRADER_SHOP_PISCARILIUS_UIM = 819;
	public static final int TRADER_SHOP_PISCARILIUS_GIM = 820;
	public static final int TRADER_SHOP_LANDSEND = 821;
	public static final int TRADER_SHOP_LANDSEND_IRON = 822;
	public static final int TRADER_SHOP_LANDSEND_UIM = 823;
	public static final int TRADER_SHOP_LANDSEND_GIM = 824;
	public static final int HUNTING_SHOP_GUILD = 825;
	public static final int FUR_SHOP_GUILD = 826;
	public static final int TRADER_SHOP_FORTIS = 827;
	public static final int TRADER_SHOP_FORTIS_IRON = 828;
	public static final int TRADER_SHOP_FORTIS_UIM = 829;
	public static final int TRADER_SHOP_FORTIS_GIM = 830;
	public static final int TRADER_SHOP_ALDARIN = 831;
	public static final int TRADER_SHOP_ALDARIN_IRON = 832;
	public static final int TRADER_SHOP_ALDARIN_UIM = 833;
	public static final int TRADER_SHOP_ALDARIN_GIM = 834;
	public static final int TRADER_SHOP_SUNSETCOAST = 835;
	public static final int TRADER_SHOP_SUNSETCOAST_IRON = 836;
	public static final int TRADER_SHOP_SUNSETCOAST_UIM = 837;
	public static final int TRADER_SHOP_SUNSETCOAST_GIM = 838;
	public static final int TRADER_SHOP_TEMPESTUS = 839;
	public static final int TRADER_SHOP_TEMPESTUS_IRON = 840;
	public static final int TRADER_SHOP_TEMPESTUS_UIM = 841;
	public static final int TRADER_SHOP_TEMPESTUS_GIM = 842;
	public static final int COLOSSEUM_REWARDS = 843;
	public static final int COLOSSEUM_REWARDS_FUTURE = 844;
	public static final int COLOSSEUM_REWARDS_PREVIOUS = 845;
	public static final int DISARM_SLOT = 846;
	public static final int PMOON_REWARDINV = 847;
	public static final int CAM_TORUM_SHOP_GENERAL = 848;
	public static final int CAM_TORUM_SHOP_MAGIC = 849;
	public static final int CAM_TORUM_SHOP_MINING = 850;
	public static final int CAM_TORUM_SHOP_BLACKSMITH = 851;
	public static final int CAM_TORUM_SHOP_BAKER = 852;
	public static final int CAM_TORUM_SHOP_JEWELLERY = 853;
	public static final int CAM_TORUM_SHOP_HERBALIST = 854;
	public static final int HUNTSMANS_KIT = 855;
	public static final int SUNSET_COAST_FISHING_SHOP = 856;
	public static final int SUNSET_COAST_GENERAL_STORE = 857;
	public static final int SUNSET_COAST_HELMET_SHOP = 858;
	public static final int STONECUTTER_STONEMASON = 859;
	public static final int FORTIS_SHOP_SILK = 860;
	public static final int FORTIS_SHOP_FUR = 861;
	public static final int FORTIS_SHOP_GEMS = 862;
	public static final int FORTIS_SHOP_SPICES = 863;
	public static final int FORTIS_SHOP_BAKER = 864;
	public static final int FORTIS_SHOP_GENERAL_1 = 865;
	public static final int FORTIS_SHOP_GENERAL_2 = 866;
	public static final int FORTIS_SHOP_BLACKSMITH = 867;
	public static final int FORTIS_SHOP_MACES = 868;
	public static final int FORTIS_SHOP_FARMING = 869;
	public static final int FORTIS_SHOP_SEAMSTRESS = 870;
	public static final int FORTIS_SHOP_FOOD = 871;
	public static final int FORTIS_SHOP_CRAFTING = 872;
	public static final int PUB_DEFAULT_1 = 873;
	public static final int PUB_DEFAULT_2 = 874;
	public static final int PUB_DEFAULT_3 = 875;
	public static final int PUB_FORTIS_1 = 876;
	public static final int PUB_FORTIS_2 = 877;
	public static final int PUB_CAM_TORUM = 878;
	public static final int DIZANAS_QUIVER_AMMO = 879;
	public static final int POH_SAWMILL_SHOP_IRON = 880;
	public static final int POH_SAWMILL_SHOP_UIM = 881;
	public static final int POH_SAWMILL_SHOP_GIM = 882;
	public static final int PISCARILIUS_GENERALSTORE_IRON = 883;
	public static final int PISCARILIUS_GENERALSTORE_UIM = 884;
	public static final int PISCARILIUS_GENERALSTORE_GIM = 885;
	public static final int WEREWOLFSTORE1_IRON = 886;
	public static final int WEREWOLFSTORE1_UIM = 887;
	public static final int WEREWOLFSTORE1_GIM = 888;
	public static final int DEADMAN_MM_SCIMITAR_SHOP2 = 889;
	public static final int ROGUETRADER_ALIM_RUNEWHOLESALE_INV_DEADMAN_TOURNAMENT = 890;
	public static final int PUB_QUETZACALLI = 891;
	public static final int PUB_ALDARIN = 892;
	public static final int PUB_MISTROCK = 893;
	public static final int FARMER_SUPPLIES = 894;
	public static final int POTION_STORE_TEMP_INV = 895;
	public static final int MAG_EMELIO_SHOP = 896;
	public static final int MM_CARRY = 897;
	public static final int MM_LAB_ALEMBIC = 898;
	public static final int MM_LAB_AGITATOR = 899;
	public static final int MM_LAB_RETORT = 900;
	public static final int ALDARIN_GENERAL_STORE = 901;
	public static final int ALDARIN_FOOD_STORE = 902;
	public static final int ALDARIN_GEM_STORE = 903;
	public static final int ALDARIN_WINE_STORE = 904;
	public static final int ALDARIN_DONATION_NEST = 905;
	public static final int MISTROCK_SHIELD_STORE = 906;
	public static final int MISTROCK_MINING_STORE = 907;
	public static final int SALVAGER_OVERLOOK_GENERAL_STORE = 908;
	public static final int SALVAGER_OVERLOOK_PLATEBODY_STORE = 909;
	public static final int QUETZACALLI_GENERAL_STORE = 910;
	public static final int PREPOT_DEVICE_INV = 911;
	public static final int ALDARIN_GEM_STORE_IM = 912;
	public static final int ALDARIN_GEM_STORE_UIM = 913;
	public static final int ALDARIN_GEM_STORE_GIM = 914;
	public static final int ITEMGRANT_TRACKING = 915;
	public static final int CLAN_HALL_PARTYCHEST = 916;
	public static final int TRADER_SHOP_SUNSETCOAST_LEAGUES = 917;
	public static final int WEREWOLFGENERALSTORE_TBLR = 918;
	public static final int LEAGUES_SAVE_TRANSFER_ITEMS = 919;
	public static final int LUNAMI_AXE_STORE = 920;
	public static final int SEBAMO_STAFF_STORE = 921;
	public static final int AUBURN_GENERAL_STORE = 922;
	public static final int DOM_LOOTPILE = 923;
	public static final int KASTORI_GENERAL_STORE = 924;
	public static final int KASTORIFARMINGSTORE = 925;
	public static final int KASTORIFISHINGSTORE = 926;
	public static final int TAL_TEKLAN_DYESHOP = 927;
	public static final int TAL_TEKLAN_RUNESHOP = 928;
	public static final int TAL_TEKLAN_ARCHERSHOP = 929;
	public static final int TAL_TEKLAN_ARCHERSHOP_DEADMAN = 930;
	public static final int TAL_TEKLAN_GENERALSTORE = 931;
	public static final int PUB_TAL_TEKLAN = 932;
	public static final int PUB_AUBURNVALE = 933;
	public static final int PUB_NEMUS = 934;
/* This file is automatically generated. Do not edit. */
}
