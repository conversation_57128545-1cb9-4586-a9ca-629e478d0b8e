@echo off
echo Fast Sideloaded Plugin Builder
echo ==============================

set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set MAVEN_OPTS=-Xmx1024m

echo [1/3] Compiling only sideloaded plugin classes...
javac -cp "runelite-client\target\classes;%USERPROFILE%\.m2\repository\*" ^
      -d runelite-client\target\classes ^
      runelite-client\src\main\java\net\runelite\client\plugins\microbot\sideloaded\**\*.java ^
      runelite-client\src\main\java\net\runelite\client\plugins\microbot\util\SideloadedPluginBuilder.java

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    exit /b 1
)

echo [2/3] Building sideloaded plugin JARs...
java -cp "runelite-client\target\classes;%USERPROFILE%\.m2\repository\*" ^
     net.runelite.client.plugins.microbot.util.SideloadedPluginBuilder ^
     "runelite-client\target\classes" ^
     "runelite-client\target" ^
     "C:\Users\<USER>\Documents\git\containerized_runescape\x11-setup"

if %ERRORLEVEL% neq 0 (
    echo Plugin building failed!
    exit /b 1
)

echo [3/3] Done! Sideloaded plugins built and copied.
echo Build completed in seconds instead of minutes!
