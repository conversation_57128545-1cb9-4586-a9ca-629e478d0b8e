<?xml version="1.0" encoding="UTF-8"?>
<!--
 Copyright (c) 2018, <PERSON> <<EMAIL>>
 All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions are met:

 1. Redistributions of source code must retain the above copyright notice, this
    list of conditions and the following disclaimer.
 2. Redistributions in binary form must reproduce the above copyright notice,
    this list of conditions and the following disclaimer in the documentation
    and/or other materials provided with the distribution.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON>UBSTITUTE GOODS OR SERVICES;
 LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>net.runelite</groupId>
		<artifactId>runelite-parent</artifactId>
		<version>1.11.13-SNAPSHOT</version>
	</parent>

	<artifactId>runelite-maven-plugin</artifactId>
	<name>RuneLite Maven Plugin</name>
	<packaging>maven-plugin</packaging>

	<properties>
		<pmd.skip>true</pmd.skip>
	</properties>

	<dependencies>
		<dependency>
			<groupId>net.runelite</groupId>
			<artifactId>cache</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.maven</groupId>
			<artifactId>maven-plugin-api</artifactId>
			<version>3.0.5</version>
		</dependency>
		<dependency>
			<groupId>org.apache.maven.plugin-tools</groupId>
			<artifactId>maven-plugin-annotations</artifactId>
			<version>3.4</version>
		</dependency>
		<dependency>
			<groupId>org.apache.maven</groupId>
			<artifactId>maven-core</artifactId>
			<version>3.0.5</version>
		</dependency>

		<dependency>
			<groupId>org.tomlj</groupId>
			<artifactId>tomlj</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>com.squareup</groupId>
			<artifactId>javapoet</artifactId>
			<version>1.13.0</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-pmd-plugin</artifactId>
				<configuration>
					<failOnViolation>true</failOnViolation>
					<printFailingErrors>true</printFailingErrors>
					<rulesets>
						<ruleset>${project.basedir}/pmd-ruleset.xml</ruleset>
					</rulesets>
					<linkXRef>false</linkXRef>
					<analysisCache>true</analysisCache>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-plugin-plugin</artifactId>
				<executions>
					<execution>
						<id>default-descriptor</id>
						<phase>process-classes</phase>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
