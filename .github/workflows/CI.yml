name: CI

on: [ push, pull_request ]

jobs:

  build:
    name: Build
    runs-on: ubuntu-24.04

    permissions:
      contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: <PERSON><PERSON>
        uses: actions/cache@v4
        with:
          path: |
            ~/.m2/repository
            ~/.cache/runelite
          key: ${{ runner.os }}-cache-${{ hashFiles('**/pom.xml', '**/build.sh', '**/pmd-ruleset.xml') }}
          restore-keys: |
            ${{ runner.os }}-cache-

      - name: Set up JDK 11
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 11

      - name: Build
        run: ./ci/build.sh
