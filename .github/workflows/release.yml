name: Release
on:
  push:
    branches:
      - main

env:
  version: "1.9.7"

jobs:
  build:
    runs-on: ubuntu-latest  # You can choose a different runner if needed
    permissions:
      contents: write

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up JDK 11
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: 11

      - name: Build Shaded JAR
        run: |
          COMMIT_SHA=$(git rev-parse --short HEAD)
          mvn clean package -Dmicrobot.commit.sha=$COMMIT_SHA

      - name: Create Release
        uses: "marvinpinto/action-automatic-releases@latest"
        with:
          repo_token: "${{ secrets.GITHUB_TOKEN }}"
          automatic_release_tag: "$version"
          prerelease: false
          title: "Release $version"
          files: |
            /home/<USER>/work/Microbot/Microbot/runelite-client/target/microbot-*.jar

      - name: Upload Jar to <PERSON><PERSON><PERSON>
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.PROD_HOST }}
          username: root
          key: ${{ secrets.PROD_SSH_KEY }}
          source: runelite-client/target/microbot-*.jar
          target: /var/www/files/releases/microbot/stable/
          strip_components: 2

      - name: Set up Maven settings
        run: |
          mkdir -p ~/.m2
          cat > ~/.m2/settings.xml <<EOL
          <settings>
          <servers>
           <server>
             <id>microbot-release</id>
             <username>${{ secrets.NEXUS_USER }}</username>
             <password>${{ secrets.NEXUS_PASSWORD }}</password>
           </server>
          </servers>
          </settings>
          EOL

      - name: Deploy to Nexus
        run: mvn deploy:deploy-file -DgroupId=com.microbot -DartifactId=client -Dversion=$version -Dpackaging=jar -Dfile=runelite-client/target/microbot-$version.jar -DrepositoryId=microbot-release -Durl=https://nexus.microbot.cloud/repository/microbot-release/

