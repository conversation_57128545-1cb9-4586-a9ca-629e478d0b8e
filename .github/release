name: Release

on: [ push, pull_request ]

jobs:
  build:
    runs-on: ubuntu-latest  # You can choose a different runner if needed
    permissions: write-all

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up JDK 11
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: 11

      - name: Build Shaded JAR
        run: mvn clean package


      - name: Create Release
        uses: "marvinpinto/action-automatic-releases@latest"
        with:
          repo_token: "${{ secrets.GITHUB_TOKEN }}"
          automatic_release_tag: "release"
          prerelease: false
          title: "Release Build"
          files: |
            /home/<USER>/work/microbot/microbot/runelite-client/target/*.jar
