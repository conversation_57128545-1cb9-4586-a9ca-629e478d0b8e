/*
 * Copyright (c) 2017, <PERSON> <<PERSON>@sigterm.info>
 * Copyright (c) 2018-2019, <PERSON> WB <hunterwb.com>
 * Copyright (c) 2019, Abex
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.cache.script;

public interface Opcodes
{
	int ICONST = 0;
	int GET_VARP = 1;
	int SET_VARP = 2;
	int SCONST = 3;
	int JUMP = 6;
	int IF_ICMPNE = 7;
	int IF_ICMPEQ = 8;
	int IF_ICMPLT = 9;
	int IF_ICMPGT = 10;
	int RETURN = 21;
	int GET_VARBIT = 25;
	int SET_VARBIT = 27;
	int IF_ICMPLE = 31;
	int IF_ICMPGE = 32;
	int ILOAD = 33;
	int ISTORE = 34;
	int OLOAD = 35;
	int OSTORE = 36;
	int JOIN_STRING = 37;
	int POP_INT = 38;
	int POP_OBJECT = 39;
	int INVOKE = 40;
	int GET_VARC_INT = 42;
	int SET_VARC_INT = 43;
	int DEFINE_ARRAY = 44;
	int GET_ARRAY_INT = 45;
	int SET_ARRAY_INT = 46;
	int GET_VARC_STRING_OLD = 47;
	int SET_VARC_STRING_OLD = 48;
	int GET_VARC_STRING = 49;
	int SET_VARC_STRING = 50;
	int SWITCH = 60;
	int PUSH_NULL = 63;
	int GET_VARCLANSETTING = 74;
	int GET_VARCLAN = 76;
	int CC_CREATE = 100;
	int CC_DELETE = 101;
	int CC_DELETEALL = 102;
	int CC_FIND = 200;
	int IF_FIND = 201;
	int CC_SETPOSITION = 1000;
	int CC_SETSIZE = 1001;
	int CC_SETHIDE = 1003;
	int CC_SETNOCLICKTHROUGH = 1005;
	int CC_SETNOSCROLLTHROUGH = 1006;
	int CC_SETSCROLLPOS = 1100;
	int CC_SETCOLOUR = 1101;
	int CC_SETFILL = 1102;
	int CC_SETTRANS = 1103;
	int CC_SETLINEWID = 1104;
	int CC_SETGRAPHIC = 1105;
	int CC_SET2DANGLE = 1106;
	int CC_SETTILING = 1107;
	int CC_SETMODEL = 1108;
	int CC_SETMODELANGLE = 1109;
	int CC_SETMODELANIM = 1110;
	int CC_SETMODELORTHOG = 1111;
	int CC_SETTEXT = 1112;
	int CC_SETTEXTFONT = 1113;
	int CC_SETTEXTALIGN = 1114;
	int CC_SETTEXTSHADOW = 1115;
	int CC_SETOUTLINE = 1116;
	int CC_SETGRAPHICSHADOW = 1117;
	int CC_SETVFLIP = 1118;
	int CC_SETHFLIP = 1119;
	int CC_SETSCROLLSIZE = 1120;
	int CC_RESUME_PAUSEBUTTON = 1121;
	int CC_SETFILLCOLOUR = 1123;
	int CC_SETLINEDIRECTION = 1126;
	int CC_SETMODELTRANSPARENT = 1127;
	int CC_SETOBJECT = 1200;
	int CC_SETNPCHEAD = 1201;
	int CC_SETPLAYERHEAD_SELF = 1202;
	int CC_SETOBJECT_NONUM = 1205;
	int CC_SETOBJECT_ALWAYS_NUM = 1212;
	int CC_SETOP = 1300;
	int CC_SETDRAGGABLE = 1301;
	int CC_SETDRAGGABLEBEHAVIOR = 1302;
	int CC_SETDRAGDEADZONE = 1303;
	int CC_SETDRAGDEADTIME = 1304;
	int CC_SETOPBASE = 1305;
	int CC_SETTARGETVERB = 1306;
	int CC_CLEAROPS = 1307;
	int CC_SETOPKEY = 1350;
	int CC_SETOPTKEY = 1351;
	int CC_SETOPKEYRATE = 1352;
	int CC_SETOPTKEYRATE = 1353;
	int CC_SETOPKEYIGNOREHELD = 1354;
	int CC_SETOPTKEYIGNOREHELD = 1355;
	int CC_SETONCLICK = 1400;
	int CC_SETONHOLD = 1401;
	int CC_SETONRELEASE = 1402;
	int CC_SETONMOUSEOVER = 1403;
	int CC_SETONMOUSELEAVE = 1404;
	int CC_SETONDRAG = 1405;
	int CC_SETONTARGETLEAVE = 1406;
	int CC_SETONVARTRANSMIT = 1407;
	int CC_SETONTIMER = 1408;
	int CC_SETONOP = 1409;
	int CC_SETONDRAGCOMPLETE = 1410;
	int CC_SETONCLICKREPEAT = 1411;
	int CC_SETONMOUSEREPEAT = 1412;
	int CC_SETONINVTRANSMIT = 1414;
	int CC_SETONSTATTRANSMIT = 1415;
	int CC_SETONTARGETENTER = 1416;
	int CC_SETONSCROLLWHEEL = 1417;
	int CC_SETONCHATTRANSMIT = 1418;
	int CC_SETONKEY = 1419;
	int CC_SETONFRIENDTRANSMIT = 1420;
	int CC_SETONCLANTRANSMIT = 1421;
	int CC_SETONMISCTRANSMIT = 1422;
	int CC_SETONDIALOGABORT = 1423;
	int CC_SETONSUBCHANGE = 1424;
	int CC_SETONSTOCKTRANSMIT = 1425;
	int CC_SETONRESIZE = 1427;
	int CC_SETONCLANSETTINGSTRANSMIT = 1428;
	int CC_SETONCLANCHANNELTRANSMIT = 1429;
	int CC_GETX = 1500;
	int CC_GETY = 1501;
	int CC_GETWIDTH = 1502;
	int CC_GETHEIGHT = 1503;
	int CC_GETHIDE = 1504;
	int CC_GETLAYER = 1505;
	int CC_GETSCROLLX = 1600;
	int CC_GETSCROLLY = 1601;
	int CC_GETTEXT = 1602;
	int CC_GETSCROLLWIDTH = 1603;
	int CC_GETSCROLLHEIGHT = 1604;
	int CC_GETMODELZOOM = 1605;
	int CC_GETMODELANGLE_X = 1606;
	int CC_GETMODELANGLE_Z = 1607;
	int CC_GETMODELANGLE_Y = 1608;
	int CC_GETTRANS = 1609;
	int CC_GETCOLOUR = 1611;
	int CC_GETFILLCOLOUR = 1612;
	int CC_GETMODELTRANSPARENT = 1614;
	int CC_GETINVOBJECT = 1700;
	int CC_GETINVCOUNT = 1701;
	int CC_GETID = 1702;
	int CC_GETTARGETMASK = 1800;
	int CC_GETOP = 1801;
	int CC_GETOPBASE = 1802;
	int CC_CALLONRESIZE = 1927;
	int CC_TRIGGEROP = 1928;
	int IF_SETPOSITION = 2000;
	int IF_SETSIZE = 2001;
	int IF_SETHIDE = 2003;
	int IF_SETNOCLICKTHROUGH = 2005;
	int IF_SETNOSCROLLTHROUGH = 2006;
	int IF_SETSCROLLPOS = 2100;
	int IF_SETCOLOUR = 2101;
	int IF_SETFILL = 2102;
	int IF_SETTRANS = 2103;
	int IF_SETLINEWID = 2104;
	int IF_SETGRAPHIC = 2105;
	int IF_SET2DANGLE = 2106;
	int IF_SETTILING = 2107;
	int IF_SETMODEL = 2108;
	int IF_SETMODELANGLE = 2109;
	int IF_SETMODELANIM = 2110;
	int IF_SETMODELORTHOG = 2111;
	int IF_SETTEXT = 2112;
	int IF_SETTEXTFONT = 2113;
	int IF_SETTEXTALIGN = 2114;
	int IF_SETTEXTSHADOW = 2115;
	int IF_SETOUTLINE = 2116;
	int IF_SETGRAPHICSHADOW = 2117;
	int IF_SETVFLIP = 2118;
	int IF_SETHFLIP = 2119;
	int IF_SETSCROLLSIZE = 2120;
	int IF_RESUME_PAUSEBUTTON = 2121;
	int IF_SETFILLCOLOUR = 2123;
	int IF_SETLINEDIRECTION = 2126;
	int IF_SETMODELTRANSPARENT = 2127;
	int IF_SETOBJECT = 2200;
	int IF_SETNPCHEAD = 2201;
	int IF_SETPLAYERHEAD_SELF = 2202;
	int IF_SETOBJECT_NONUM = 2205;
	int IF_SETOBJECT_ALWAYS_NUM = 2212;
	int IF_SETOP = 2300;
	int IF_SETDRAGGABLE = 2301;
	int IF_SETDRAGGABLEBEHAVIOR = 2302;
	int IF_SETDRAGDEADZONE = 2303;
	int IF_SETDRAGDEADTIME = 2304;
	int IF_SETOPBASE = 2305;
	int IF_SETTARGETVERB = 2306;
	int IF_CLEAROPS = 2307;
	int IF_SETOPKEY = 2350;
	int IF_SETOPTKEY = 2351;
	int IF_SETOPKEYRATE = 2352;
	int IF_SETOPTKEYRATE = 2353;
	int IF_SETOPKEYIGNOREHELD = 2354;
	int IF_SETOPTKEYIGNOREHELD = 2355;
	int IF_SETONCLICK = 2400;
	int IF_SETONHOLD = 2401;
	int IF_SETONRELEASE = 2402;
	int IF_SETONMOUSEOVER = 2403;
	int IF_SETONMOUSELEAVE = 2404;
	int IF_SETONDRAG = 2405;
	int IF_SETONTARGETLEAVE = 2406;
	int IF_SETONVARTRANSMIT = 2407;
	int IF_SETONTIMER = 2408;
	int IF_SETONOP = 2409;
	int IF_SETONDRAGCOMPLETE = 2410;
	int IF_SETONCLICKREPEAT = 2411;
	int IF_SETONMOUSEREPEAT = 2412;
	int IF_SETONINVTRANSMIT = 2414;
	int IF_SETONSTATTRANSMIT = 2415;
	int IF_SETONTARGETENTER = 2416;
	int IF_SETONSCROLLWHEEL = 2417;
	int IF_SETONCHATTRANSMIT = 2418;
	int IF_SETONKEY = 2419;
	int IF_SETONFRIENDTRANSMIT = 2420;
	int IF_SETONCLANTRANSMIT = 2421;
	int IF_SETONMISCTRANSMIT = 2422;
	int IF_SETONDIALOGABORT = 2423;
	int IF_SETONSUBCHANGE = 2424;
	int IF_SETONSTOCKTRANSMIT = 2425;
	int IF_SETONRESIZE = 2427;
	int IF_SETONCLANSETTINGSTRANSMIT = 2428;
	int IF_SETONCLANCHANNELTRANSMIT = 2429;
	int IF_GETX = 2500;
	int IF_GETY = 2501;
	int IF_GETWIDTH = 2502;
	int IF_GETHEIGHT = 2503;
	int IF_GETHIDE = 2504;
	int IF_GETLAYER = 2505;
	int IF_GETSCROLLX = 2600;
	int IF_GETSCROLLY = 2601;
	int IF_GETTEXT = 2602;
	int IF_GETSCROLLWIDTH = 2603;
	int IF_GETSCROLLHEIGHT = 2604;
	int IF_GETMODELZOOM = 2605;
	int IF_GETMODELANGLE_X = 2606;
	int IF_GETMODELANGLE_Z = 2607;
	int IF_GETMODELANGLE_Y = 2608;
	int IF_GETTRANS = 2609;
	int IF_GETCOLOUR = 2611;
	int IF_GETFILLCOLOUR = 2612;
	int IF_GETMODELTRANSPARENT = 2614;
	int IF_GETINVOBJECT = 2700;
	int IF_GETINVCOUNT = 2701;
	int IF_HASSUB = 2702;
	int IF_GETTOP = 2706;
	int IF_GETTARGETMASK = 2800;
	int IF_GETOP = 2801;
	int IF_GETOPBASE = 2802;
	int IF_CALLONRESIZE = 2927;
	int IF_TRIGGEROP = 2928;
	int MES = 3100;
	int ANIM = 3101;
	int IF_CLOSE = 3103;
	int RESUME_COUNTDIALOG = 3104;
	int RESUME_NAMEDIALOG = 3105;
	int RESUME_STRINGDIALOG = 3106;
	int OPPLAYER = 3107;
	int IF_DRAGPICKUP = 3108;
	int CC_DRAGPICKUP = 3109;
	int MOUSECAM = 3110;
	int GETREMOVEROOFS = 3111;
	int SETREMOVEROOFS = 3112;
	int OPENURL = 3113;
	int RESUME_OBJDIALOG = 3115;
	int BUG_REPORT = 3116;
	int SETSHIFTCLICKDROP = 3117;
	int SETSHOWMOUSEOVERTEXT = 3118;
	int RENDERSELF = 3119;
	int SETSHOWMOUSECROSS = 3125;
	int SETSHOWLOADINGMESSAGES = 3126;
	int SETTAPTODROP = 3127;
	int GETTAPTODROP = 3128;
	int GETCANVASSIZE = 3132;
	int MOBILE_SETFPS = 3133;
	int MOBILE_OPENSTORE = 3134;
	int MOBILE_OPENSTORECATEGORY = 3135;
	int SETHIDEUSERNAME = 3141;
	int GETHIDEUSERNAME = 3142;
	int SETREMEMBERUSERNAME = 3143;
	int GETREMEMBERUSERNAME = 3144;
	int SHOW_IOS_REVIEW = 3145;
	int SOUND_SYNTH = 3200;
	int SOUND_SONG = 3201;
	int SOUND_JINGLE = 3202;
	int CLIENTCLOCK = 3300;
	int INV_GETOBJ = 3301;
	int INV_GETNUM = 3302;
	int INV_TOTAL = 3303;
	int INV_SIZE = 3304;
	int STAT = 3305;
	int STAT_BASE = 3306;
	int STAT_XP = 3307;
	int COORD = 3308;
	int COORDX = 3309;
	int COORDZ = 3310;
	int COORDY = 3311;
	int MAP_MEMBERS = 3312;
	int INVOTHER_GETOBJ = 3313;
	int INVOTHER_GETNUM = 3314;
	int INVOTHER_TOTAL = 3315;
	int STAFFMODLEVEL = 3316;
	int REBOOTTIMER = 3317;
	int MAP_WORLD = 3318;
	int RUNENERGY_VISIBLE = 3321;
	int RUNWEIGHT_VISIBLE = 3322;
	int PLAYERMOD = 3323;
	int WORLDFLAGS = 3324;
	int MOVECOORD = 3325;
	int ENUM_STRING = 3400;
	int ENUM = 3408;
	int ENUM_GETOUTPUTCOUNT = 3411;
	int FRIEND_COUNT = 3600;
	int FRIEND_GETNAME = 3601;
	int FRIEND_GETWORLD = 3602;
	int FRIEND_GETRANK = 3603;
	int FRIEND_SETRANK = 3604;
	int FRIEND_ADD = 3605;
	int FRIEND_DEL = 3606;
	int IGNORE_ADD = 3607;
	int IGNORE_DEL = 3608;
	int FRIEND_TEST = 3609;
	int CLAN_GETCHATDISPLAYNAME = 3611;
	int CLAN_GETCHATCOUNT = 3612;
	int CLAN_GETCHATUSERNAME = 3613;
	int CLAN_GETCHATUSERWORLD = 3614;
	int CLAN_GETCHATUSERRANK = 3615;
	int CLAN_GETCHATMINKICK = 3616;
	int CLAN_KICKUSER = 3617;
	int CLAN_GETCHATRANK = 3618;
	int CLAN_JOINCHAT = 3619;
	int CLAN_LEAVECHAT = 3620;
	int IGNORE_COUNT = 3621;
	int IGNORE_GETNAME = 3622;
	int IGNORE_TEST = 3623;
	int CLAN_ISSELF = 3624;
	int CLAN_GETCHATOWNERNAME = 3625;
	int CLAN_ISFRIEND = 3626;
	int CLAN_ISIGNORE = 3627;
	int ACTIVECLANSETTINGS_FIND_LISTENED = 3800;
	int ACTIVECLANSETTINGS_FIND_AFFINED = 3801;
	int ACTIVECLANSETTINGS_GETCLANNAME = 3802;
	int ACTIVECLANSETTINGS_GETALLOWUNAFFINED = 3803;
	int ACTIVECLANSETTINGS_GETRANKTALK = 3804;
	int ACTIVECLANSETTINGS_GETRANKKICK = 3805;
	int ACTIVECLANSETTINGS_GETRANKLOOTSHARE = 3806;
	int ACTIVECLANSETTINGS_GETCOINSHARE = 3807;
	int ACTIVECLANSETTINGS_GETAFFINEDCOUNT = 3809;
	int ACTIVECLANSETTINGS_GETAFFINEDDISPLAYNAME = 3810;
	int ACTIVECLANSETTINGS_GETAFFINEDRANK = 3811;
	int ACTIVECLANSETTINGS_GETBANNEDCOUNT = 3812;
	int ACTIVECLANSETTINGS_GETBANNEDDISPLAYNAME = 3813;
	int ACTIVECLANSETTINGS_GETAFFINEDEXTRAINFO = 3814;
	int ACTIVECLANSETTINGS_GETCURRENTOWNER_SLOT = 3815;
	int ACTIVECLANSETTINGS_GETREPLACEMENTOWNER_SLOT = 3816;
	int ACTIVECLANSETTINGS_GETAFFINEDSLOT = 3817;
	int ACTIVECLANSETTINGS_GETSORTEDAFFINEDSLOT = 3818;
	int AFFINEDCLANSETTINGS_ADDBANNED_FROMCHANNEL = 3819;
	int ACTIVECLANSETTINGS_GETAFFINEDJOINRUNEDAY = 3820;
	int AFFINEDCLANSETTINGS_SETMUTED_FROMCHANNEL = 3821;
	int ACTIVECLANSETTINGS_GETAFFINEDMUTED = 3822;
	int ACTIVECLANCHANNEL_FIND_LISTENED = 3850;
	int ACTIVECLANCHANNEL_FIND_AFFINED = 3851;
	int ACTIVECLANCHANNEL_GETCLANNAME = 3852;
	int ACTIVECLANCHANNEL_GETRANKKICK = 3853;
	int ACTIVECLANCHANNEL_GETRANKTALK = 3854;
	int ACTIVECLANCHANNEL_GETUSERCOUNT = 3855;
	int ACTIVECLANCHANNEL_GETUSERDISPLAYNAME = 3856;
	int ACTIVECLANCHANNEL_GETUSERRANK = 3857;
	int ACTIVECLANCHANNEL_GETUSERWORLD = 3858;
	int ACTIVECLANCHANNEL_KICKUSER = 3859;
	int ACTIVECLANCHANNEL_GETUSERSLOT = 3860;
	int ACTIVECLANCHANNEL_GETSORTEDUSERSLOT = 3861;
	int CLANPROFILE_FIND = 3890;
	int STOCKMARKET_GETOFFERTYPE = 3903;
	int STOCKMARKET_GETOFFERITEM = 3904;
	int STOCKMARKET_GETOFFERPRICE = 3905;
	int STOCKMARKET_GETOFFERCOUNT = 3906;
	int STOCKMARKET_GETOFFERCOMPLETEDCOUNT = 3907;
	int STOCKMARKET_GETOFFERCOMPLETEDGOLD = 3908;
	int STOCKMARKET_ISOFFEREMPTY = 3910;
	int STOCKMARKET_ISOFFERSTABLE = 3911;
	int STOCKMARKET_ISOFFERFINISHED = 3912;
	int STOCKMARKET_ISOFFERADDING = 3913;
	int TRADINGPOST_SORTBY_NAME = 3914;
	int TRADINGPOST_SORTBY_PRICE = 3915;
	int TRADINGPOST_SORTFILTERBY_WORLD = 3916;
	int TRADINGPOST_SORTBY_AGE = 3917;
	int TRADINGPOST_SORTBY_COUNT = 3918;
	int TRADINGPOST_GETTOTALOFFERS = 3919;
	int TRADINGPOST_GETOFFERWORLD = 3920;
	int TRADINGPOST_GETOFFERNAME = 3921;
	int TRADINGPOST_GETOFFERPREVIOUSNAME = 3922;
	int TRADINGPOST_GETOFFERAGE = 3923;
	int TRADINGPOST_GETOFFERCOUNT = 3924;
	int TRADINGPOST_GETOFFERPRICE = 3925;
	int TRADINGPOST_GETOFFERITEM = 3926;
	int ADD = 4000;
	int SUB = 4001;
	int MULTIPLY = 4002;
	int DIV = 4003;
	int RANDOM = 4004;
	int RANDOMINC = 4005;
	int INTERPOLATE = 4006;
	int ADDPERCENT = 4007;
	int SETBIT = 4008;
	int CLEARBIT = 4009;
	int TESTBIT = 4010;
	int MOD = 4011;
	int POW = 4012;
	int INVPOW = 4013;
	int AND = 4014;
	int OR = 4015;
	int SCALE = 4018;
	int BITCOUNT = 4025;
	int TOGGLEBIT = 4026;
	int SETBIT_RANGE = 4027;
	int CLEARBIT_RANGE = 4028;
	int GETBIT_RANGE = 4029;
	int APPEND_NUM = 4100;
	int APPEND = 4101;
	int APPEND_SIGNNUM = 4102;
	int LOWERCASE = 4103;
	int FROMDATE = 4104;
	int TEXT_GENDER = 4105;
	int TOSTRING = 4106;
	int COMPARE = 4107;
	int PARAHEIGHT = 4108;
	int PARAWIDTH = 4109;
	int TEXT_SWITCH = 4110;
	int ESCAPE = 4111;
	int APPEND_CHAR = 4112;
	int CHAR_ISPRINTABLE = 4113;
	int CHAR_ISALPHANUMERIC = 4114;
	int CHAR_ISALPHA = 4115;
	int CHAR_ISNUMERIC = 4116;
	int STRING_LENGTH = 4117;
	int SUBSTRING = 4118;
	int REMOVETAGS = 4119;
	int STRING_INDEXOF_CHAR = 4120;
	int STRING_INDEXOF_STRING = 4121;
	int OC_NAME = 4200;
	int OC_OP = 4201;
	int OC_IOP = 4202;
	int OC_COST = 4203;
	int OC_STACKABLE = 4204;
	int OC_CERT = 4205;
	int OC_UNCERT = 4206;
	int OC_MEMBERS = 4207;
	int OC_PLACEHOLDER = 4208;
	int OC_UNPLACEHOLDER = 4209;
	int OC_FIND = 4210;
	int OC_FINDNEXT = 4211;
	int OC_FINDRESET = 4212;
	int CHAT_GETFILTER_PUBLIC = 5000;
	int CHAT_SETFILTER = 5001;
	int CHAT_SENDABUSEREPORT = 5002;
	int CHAT_GETHISTORY_BYTYPEANDLINE = 5003;
	int CHAT_GETHISTORY_BYUID = 5004;
	int CHAT_GETFILTER_PRIVATE = 5005;
	int CHAT_SENDPUBLIC = 5008;
	int CHAT_SENDPRIVATE = 5009;
	int CHAT_SENDCLAN = 5010;
	int CHAT_PLAYERNAME = 5015;
	int CHAT_GETFILTER_TRADE = 5016;
	int CHAT_GETHISTORYLENGTH = 5017;
	int CHAT_GETNEXTUID = 5018;
	int CHAT_GETPREVUID = 5019;
	int DOCHEAT = 5020;
	int CHAT_SETMESSAGEFILTER = 5021;
	int CHAT_GETMESSAGEFILTER = 5022;
	int WRITECONSOLE = 5023;
	int CHAT_GETHISTORYEX_BYTYPEANDLINE = 5030;
	int CHAT_GETHISTORYEX_BYUID = 5031;
	int GETWINDOWMODE = 5306;
	int SETWINDOWMODE = 5307;
	int GETDEFAULTWINDOWMODE = 5308;
	int SETDEFAULTWINDOWMODE = 5309;
	int CAM_FORCEANGLE = 5504;
	int CAM_GETANGLE_XA = 5505;
	int CAM_GETANGLE_YA = 5506;
	int CAM_SETFOLLOWHEIGHT = 5530;
	int CAM_GETFOLLOWHEIGHT = 5531;
	int LOGOUT = 5630;
	int VIEWPORT_SETFOV = 6200;
	int VIEWPORT_SETZOOM = 6201;
	int VIEWPORT_CLAMPFOV = 6202;
	int VIEWPORT_GETEFFECTIVESIZE = 6203;
	int VIEWPORT_GETZOOM = 6204;
	int VIEWPORT_GETFOV = 6205;
	int WORLDLIST_FETCH = 6500;
	int WORLDLIST_START = 6501;
	int WORLDLIST_NEXT = 6502;
	int WORLDLIST_SPECIFIC = 6506;
	int WORLDLIST_SORT = 6507;
	int SETFOLLOWEROPSLOWPRIORITY = 6512;
	int NC_PARAM = 6513;
	int LC_PARAM = 6514;
	int OC_PARAM = 6515;
	int STRUCT_PARAM = 6516;
	int ON_MOBILE = 6518;
	int CLIENTTYPE = 6519;
	int MOBILE_KEYBOARDHIDE = 6521;
	int MOBILE_BATTERYLEVEL = 6524;
	int MOBILE_BATTERYCHARGING = 6525;
	int MOBILE_WIFIAVAILABLE = 6526;
	int WORLDMAP_GETMAPNAME = 6601;
	int WORLDMAP_SETMAP = 6602;
	int WORLDMAP_GETZOOM = 6603;
	int WORLDMAP_SETZOOM = 6604;
	int WORLDMAP_ISLOADED = 6605;
	int WORLDMAP_JUMPTODISPLAYCOORD = 6606;
	int WORLDMAP_JUMPTODISPLAYCOORD_INSTANT = 6607;
	int WORLDMAP_JUMPTOSOURCECOORD = 6608;
	int WORLDMAP_JUMPTOSOURCECOORD_INSTANT = 6609;
	int WORLDMAP_GETDISPLAYPOSITION = 6610;
	int WORLDMAP_GETCONFIGORIGIN = 6611;
	int WORLDMAP_GETCONFIGSIZE = 6612;
	int WORLDMAP_GETCONFIGBOUNDS = 6613;
	int WORLDMAP_GETCONFIGZOOM = 6614;
	int WORLDMAP_GETCURRENTMAP = 6616;
	int WORLDMAP_GETDISPLAYCOORD = 6617;
	int WORLDMAP_COORDINMAP = 6621;
	int WORLDMAP_GETSIZE = 6622;
	int WORLDMAP_PERPETUALFLASH = 6628;
	int WORLDMAP_FLASHELEMENT = 6629;
	int WORLDMAP_FLASHELEMENTCATEGORY = 6630;
	int WORLDMAP_STOPCURRENTFLASHES = 6631;
	int WORLDMAP_DISABLEELEMENTS = 6632;
	int WORLDMAP_DISABLEELEMENT = 6633;
	int WORLDMAP_DISABLEELEMENTCATEGORY = 6634;
	int WORLDMAP_GETDISABLEELEMENTS = 6635;
	int WORLDMAP_GETDISABLEELEMENT = 6636;
	int WORLDMAP_GETDISABLEELEMENTCATEGORY = 6637;
	int WORLDMAP_LISTELEMENT_START = 6639;
	int WORLDMAP_LISTELEMENT_NEXT = 6640;
	int MEC_TEXT = 6693;
	int MEC_TEXTSIZE = 6694;
	int MEC_CATEGORY = 6695;
	int MEC_SPRITE = 6696;
	int WORLDMAP_ELEMENT = 6697;
	int WORLDMAP_ELEMENTCOORD = 6699;
	int DB_FIND_WITH_COUNT = 7500;
	int DB_FINDNEXT = 7501;
	int DB_GETFIELD = 7502;
	int DB_GETFIELDCOUNT = 7503;
	int DB_FINDALL_WITH_COUNT = 7504;
	int DB_GETROWTABLE = 7505;
	int DB_GETROW = 7506;
	int DB_FIND_FILTER_WITH_COUNT = 7507;
	int DB_FIND = 7508;
	int DB_FINDALL = 7509;
	int DB_FIND_FILTER = 7510;
}
