/*
 * Copyright (c) 2017, <PERSON> <<PERSON>@sigterm.info>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.cache.definitions.sound;

public class SoundEffect1Definition
{
	public SoundEffect2Definition field1173;
	public SoundEffect2Definition field1174;
	public SoundEffect2Definition field1175;
	public int field1176 = 500;
	public int[] field1177 = new int[]
	{
		0, 0, 0, 0, 0
	};
	public SoundEffect2Definition field1178;
	public int[] field1179 = new int[]
	{
		0, 0, 0, 0, 0
	};
	public int[] field1180 = new int[]
	{
		0, 0, 0, 0, 0
	};
	public SoundEffect2Definition field1181;
	public SoundEffect3Definition field1182;
	public SoundEffect2Definition field1183;
	public int field1184 = 100;
	public SoundEffect2Definition field1186;
	public int field1187 = 0;
	public int field1188 = 0;
	public SoundEffect2Definition field1192;
	public SoundEffect2Definition field1193;
}
