/*
 * Copyright (c) 2023, <PERSON> <<PERSON>@sigterm.info>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.cache.util;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Random;
import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assume.assumeNoException;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;

@Ignore
public class BZip2Test
{
	@BeforeClass
	public static void beforeClass()
	{
		try
		{
			var l = LibBZip2.INSTANCE;
		}
		catch (UnsatisfiedLinkError ex)
		{
			assumeNoException(ex);
		}
	}

	@Test
	public void testLibBZip2Small() throws IOException
	{
		byte[] data = "runelite".getBytes(StandardCharsets.UTF_8);
		byte[] ddata = BZip2.compressLibBZip2(data);
		byte[] idata = BZip2.decompress(ddata, ddata.length);
		assertEquals("runelite", new String(idata));
	}

	@Test
	public void testLibBZipLarge() throws IOException
	{
		byte[] data = new byte[1024 * 1024];
		Random r = new Random(42);
		r.nextBytes(data);

		byte[] ddata = BZip2.compressLibBZip2(data);
		byte[] idata = BZip2.decompress(ddata, ddata.length);
		assertArrayEquals(data, idata);
	}
}