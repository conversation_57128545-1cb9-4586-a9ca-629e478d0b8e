import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("mj")
public class class329 {
	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "(I)V",
		garbageValue = "1117025431"
	)
	public static void method6845() {
		ByteArrayPool.field5120.clear();
		ByteArrayPool.field5120.add(100);
		ByteArrayPool.field5120.add(5000);
		ByteArrayPool.field5120.add(10000);
		ByteArrayPool.field5120.add(30000);
	}
}
