import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("py")
public class class399 {
	@ObfuscatedName("ly")
	static String field4678;

	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "(Lps;I)V",
		garbageValue = "509732699"
	)
	public static void method8008(AbstractArchive var0) {
		ParamComposition.ParamDefinition_archive = var0;
	}
}
