import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;

@ObfuscatedName("rj")
public class class457 {
	@ObfuscatedName("ap")
	public char field5045;
	@ObfuscatedName("aj")
	@ObfuscatedGetter(
		intValue = -1333754181
	)
	public int field5046;
	@ObfuscatedName("an")
	@ObfuscatedGetter(
		intValue = -861789389
	)
	public int field5047;

	class457() {
		this.field5046 = 0;
		this.field5047 = 0;
	}
}
