import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("im")
public class class214 {
	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	static final class214 field2387;
	@ObfuscatedName("aj")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	static final class214 field2374;
	@ObfuscatedName("an")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	static final class214 field2380;
	@ObfuscatedName("ai")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	public static final class214 field2375;
	@ObfuscatedName("al")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	public static final class214 field2376;
	@ObfuscatedName("ac")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	public static final class214 field2377;
	@ObfuscatedName("aa")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	public static final class214 field2378;
	@ObfuscatedName("am")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	public static final class214 field2379;
	@ObfuscatedName("ah")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	public static final class214 field2373;
	@ObfuscatedName("ag")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	public static final class214 field2381;
	@ObfuscatedName("au")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	public static final class214 field2382;
	@ObfuscatedName("ar")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	public static final class214 field2383;
	@ObfuscatedName("ad")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	static final class214 field2384;
	@ObfuscatedName("af")
	@ObfuscatedSignature(
		descriptor = "Lim;"
	)
	static final class214 field2385;
	@ObfuscatedName("az")
	@ObfuscatedGetter(
		intValue = 1587448925
	)
	public final int field2372;

	static {
		field2387 = new class214(0);
		field2374 = new class214(1);
		field2380 = new class214(2);
		field2375 = new class214(3);
		field2376 = new class214(4);
		field2377 = new class214(5);
		field2378 = new class214(6);
		field2379 = new class214(7);
		field2373 = new class214(8);
		field2381 = new class214(9);
		field2382 = new class214(10);
		field2383 = new class214(11);
		field2384 = new class214(12);
		field2385 = new class214(13);
	}

	class214(int var1) {
		this.field2372 = var1;
	}
}
