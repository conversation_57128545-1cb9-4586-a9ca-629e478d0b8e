import java.io.File;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("wl")
public class class573 {
	@ObfuscatedName("aw")
	static File field5665;

	@ObfuscatedName("aq")
	@ObfuscatedSignature(
		descriptor = "(Ltp;B)V",
		garbageValue = "-16"
	)
	static void method10775(class509 var0) {
		DelayFadeTask.method9245(var0, (class563)null, true);
	}
}
