import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("qe")
public class class422 {
	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "Lqe;"
	)
	static final class422 field4878;
	@ObfuscatedName("aj")
	@ObfuscatedSignature(
		descriptor = "Lqe;"
	)
	static final class422 field4872;
	@ObfuscatedName("an")
	@ObfuscatedSignature(
		descriptor = "Lqe;"
	)
	static final class422 field4873;
	@ObfuscatedName("ai")
	@ObfuscatedSignature(
		descriptor = "Lqe;"
	)
	static final class422 field4874;

	static {
		field4878 = new class422();
		field4872 = new class422();
		field4873 = new class422();
		field4874 = new class422();
	}

	class422() {
	}
}
