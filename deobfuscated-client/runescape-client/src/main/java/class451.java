import net.runelite.mapping.Export;
import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;

@ObfuscatedName("ri")
public final class class451 {
	@ObfuscatedName("aw")
	@Export("Tiles_hue")
	static int[] Tiles_hue;
	@ObfuscatedName("ap")
	final Object field5012;
	@ObfuscatedName("aj")
	@ObfuscatedGetter(
		intValue = 1516019759
	)
	int field5010;

	class451(Object var1, int var2) {
		this.field5012 = var1;
		this.field5010 = var2;
	}
}
