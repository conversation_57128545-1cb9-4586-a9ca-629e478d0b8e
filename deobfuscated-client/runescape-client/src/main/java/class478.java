import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("sc")
public class class478 {
	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "Lsc;"
	)
	public static final class478 field5146;
	@ObfuscatedName("aj")
	@ObfuscatedSignature(
		descriptor = "Lsc;"
	)
	static final class478 field5144;
	@ObfuscatedName("an")
	@ObfuscatedGetter(
		intValue = -283230723
	)
	final int field5143;

	static {
		field5146 = new class478(0);
		field5144 = new class478(1);
	}

	class478(int var1) {
		this.field5143 = var1;
	}
}
