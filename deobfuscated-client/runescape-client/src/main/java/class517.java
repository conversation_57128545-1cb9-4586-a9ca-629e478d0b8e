import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("tm")
public class class517 extends class518 {
	public class517(int var1) {
		super(var1);
	}

	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "(Lwt;II)V",
		garbageValue = "-2054197163"
	)
	void vmethod10097(Buffer var1, int var2) {
	}

	@ObfuscatedName("ai")
	@ObfuscatedSignature(
		descriptor = "(II)D",
		garbageValue = "1823407610"
	)
	public static double method10079(int var0) {
		return (double)class177.method3964(var0) / 65536.0D;
	}
}
