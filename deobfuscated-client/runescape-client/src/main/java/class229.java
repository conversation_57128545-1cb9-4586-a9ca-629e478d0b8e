import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("iq")
public class class229 {
	@ObfuscatedName("ai")
	public String field2507;
	@ObfuscatedName("al")
	public float[] field2505;
	@ObfuscatedName("ac")
	@ObfuscatedGetter(
		intValue = 1379093053
	)
	public int field2506;
	@ObfuscatedName("aa")
	@ObfuscatedGetter(
		intValue = -1003254749
	)
	public int field2502;
	@ObfuscatedName("am")
	@ObfuscatedGetter(
		intValue = 2129230383
	)
	public int field2504;
	// $FF: synthetic field
	@ObfuscatedSignature(
		descriptor = "Lio;"
	)
	final class228 this$0;

	@ObfuscatedSignature(
		descriptor = "(Lio;)V"
	)
	class229(class228 var1) {
		this.this$0 = var1;
		this.field2505 = new float[4];
		this.field2506 = 1;
		this.field2502 = 1;
		this.field2504 = 0;
	}
}
