import net.runelite.mapping.Export;
import net.runelite.mapping.Implements;
import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("mg")
@Implements("ClientPacket")
public class ClientPacket implements class323 {
	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPLOCT;
	@ObfuscatedName("aj")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPPLAYER6;
	@ObfuscatedName("an")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket PING_STATISTICS;
	@ObfuscatedName("ai")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket FRIEND_DELUSER;
	@ObfuscatedName("al")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket CLAN_SETTINGS_SET_MUTED_FROM_CHANNEL;
	@ObfuscatedName("ac")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPNPC5;
	@ObfuscatedName("aa")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPOBJ3;
	@ObfuscatedName("am")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPHELDD;
	@ObfuscatedName("ah")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket FRIEND_CHAT_SETRANK;
	@ObfuscatedName("ag")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_RUNSCRIPT;
	@ObfuscatedName("au")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPPLAYER1;
	@ObfuscatedName("ar")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPNPCU;
	@ObfuscatedName("ad")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPPLAYER7;
	@ObfuscatedName("af")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket EVENT_MOUSE_CLICK;
	@ObfuscatedName("ak")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPNPC3;
	@ObfuscatedName("az")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket MESSAGE_PUBLIC;
	@ObfuscatedName("aw")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket UPDATE_PLAYER_MODEL;
	@ObfuscatedName("at")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTON2;
	@ObfuscatedName("ae")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTON3;
	@ObfuscatedName("av")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPOBJ5;
	@ObfuscatedName("ao")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	static final ClientPacket field3395;
	@ObfuscatedName("as")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_CRMVIEW;
	@ObfuscatedName("ax")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket CLAN_CHANNEL_KICK_USER;
	@ObfuscatedName("ab")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTON8;
	@ObfuscatedName("aq")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTON10;
	@ObfuscatedName("ay")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket CLAN_CHANNEL_FULL_REQUEST;
	@ObfuscatedName("ba")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_SUBOP;
	@ObfuscatedName("bx")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPNPCT;
	@ObfuscatedName("bl")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPLOC5;
	@ObfuscatedName("bi")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPLOC3;
	@ObfuscatedName("bv")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket EVENT_CAMERA_POSITION;
	@ObfuscatedName("bq")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket MUSIC_PLAYING;
	@ObfuscatedName("bm")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket SET_HEADING;
	@ObfuscatedName("be")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	static final ClientPacket field3446;
	@ObfuscatedName("bo")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPLOC2;
	@ObfuscatedName("bw")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket BUG_REPORT;
	@ObfuscatedName("bf")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket CHAT_SENDABUSEREPORT;
	@ObfuscatedName("bj")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPNPC1;
	@ObfuscatedName("bt")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket RESUME_NAMEDIALOG;
	@ObfuscatedName("bd")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPNPC4;
	@ObfuscatedName("bs")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPLOC4;
	@ObfuscatedName("bn")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPPLAYER3;
	@ObfuscatedName("bc")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	static final ClientPacket field3417;
	@ObfuscatedName("bg")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket CLICK_WORLD_MAP;
	@ObfuscatedName("bb")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket EVENT_WINDOW_SETTING;
	@ObfuscatedName("bu")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPLOCE;
	@ObfuscatedName("bz")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket CLAN_SETTINGS_FULL_REQUEST;
	@ObfuscatedName("by")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket CLAN_SETTINGS_ADD_BANNED_FROM_CHANNEL;
	@ObfuscatedName("br")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPPLAYER5;
	@ObfuscatedName("bk")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket MAP_BUILD_COMPLETE;
	@ObfuscatedName("bh")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket CHAT_SENDPRIVATE;
	@ObfuscatedName("bp")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTONT;
	@ObfuscatedName("ck")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPPLAYERU;
	@ObfuscatedName("cy")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket WIDGET_TYPE;
	@ObfuscatedName("ca")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket CLOSE_MODAL;
	@ObfuscatedName("cr")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	static final ClientPacket field3430;
	@ObfuscatedName("cu")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	static final ClientPacket field3463;
	@ObfuscatedName("cd")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket RESUME_COUNTDIALOG;
	@ObfuscatedName("cl")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket EVENT_MOUSE_IDLE;
	@ObfuscatedName("cq")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPOBJU;
	@ObfuscatedName("ci")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPPLAYER2;
	@ObfuscatedName("cg")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket RESUME_OBJDIALOG;
	@ObfuscatedName("cx")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket REFLECTION_CHECK_REPLY;
	@ObfuscatedName("cw")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTON4;
	@ObfuscatedName("cz")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPPLAYERT;
	@ObfuscatedName("cs")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IGNORE_DELUSER;
	@ObfuscatedName("ct")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket RESUME_PAUSEBUTTON;
	@ObfuscatedName("cf")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTON7;
	@ObfuscatedName("ch")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket FRIEND_ADDUSER;
	@ObfuscatedName("cp")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket MOVE_GAMECLICK;
	@ObfuscatedName("ce")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket FRIEND_CHAT_JOIN_LEAVE;
	@ObfuscatedName("co")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket MINIMAP_CLICK;
	@ObfuscatedName("cj")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPNPC2;
	@ObfuscatedName("cc")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPOBJ2;
	@ObfuscatedName("cn")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket CHAT_SETFILTER;
	@ObfuscatedName("cv")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IGNORE_LIST_ADD;
	@ObfuscatedName("cb")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket NO_TIMEOUT;
	@ObfuscatedName("cm")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPOBJE;
	@ObfuscatedName("dl")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket TELEPORT;
	@ObfuscatedName("db")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket CLAN_KICKUSER;
	@ObfuscatedName("dk")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTON1;
	@ObfuscatedName("de")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket FREECAM_EXIT;
	@ObfuscatedName("du")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket EVENT_APPLET_FOCUS;
	@ObfuscatedName("dv")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket DOCHEAT;
	@ObfuscatedName("dr")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket MOUSE_MOVE;
	@ObfuscatedName("dd")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTON6;
	@ObfuscatedName("dy")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTON9;
	@ObfuscatedName("dx")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPNPCE;
	@ObfuscatedName("dm")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPOBJ4;
	@ObfuscatedName("di")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPLOC1;
	@ObfuscatedName("dp")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTON5;
	@ObfuscatedName("dn")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket RESUME_STRINGDIALOG;
	@ObfuscatedName("dg")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPPLAYER8;
	@ObfuscatedName("dt")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket MOUSE_WHEEL;
	@ObfuscatedName("ds")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPOBJT;
	@ObfuscatedName("dq")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket EVENT_KEYBOARD;
	@ObfuscatedName("dc")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket DETECT_MODIFIED_CLIENT;
	@ObfuscatedName("dj")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPLOCU;
	@ObfuscatedName("da")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket LOGIN_TIMINGS;
	@ObfuscatedName("dz")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPOBJ1;
	@ObfuscatedName("do")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket OPPLAYER4;
	@ObfuscatedName("dh")
	@ObfuscatedSignature(
		descriptor = "Lmg;"
	)
	public static final ClientPacket IF_BUTTONX;
	@ObfuscatedName("nb")
	@ObfuscatedSignature(
		descriptor = "Lcm;"
	)
	@Export("clientPreferences")
	static ClientPreferences clientPreferences;
	@ObfuscatedName("dw")
	@ObfuscatedGetter(
		intValue = -1436688485
	)
	@Export("id")
	final int id;
	@ObfuscatedName("df")
	@ObfuscatedGetter(
		intValue = -1934804097
	)
	@Export("length")
	final int length;

	static {
		OPLOCT = new ClientPacket(0, 15);
		OPPLAYER6 = new ClientPacket(1, 3);
		PING_STATISTICS = new ClientPacket(2, 10);
		FRIEND_DELUSER = new ClientPacket(3, -1);
		CLAN_SETTINGS_SET_MUTED_FROM_CHANNEL = new ClientPacket(4, -1);
		OPNPC5 = new ClientPacket(5, 3);
		OPOBJ3 = new ClientPacket(6, 7);
		OPHELDD = new ClientPacket(7, 16);
		FRIEND_CHAT_SETRANK = new ClientPacket(8, -1);
		IF_RUNSCRIPT = new ClientPacket(9, -2);
		OPPLAYER1 = new ClientPacket(10, 3);
		OPNPCU = new ClientPacket(11, 11);
		OPPLAYER7 = new ClientPacket(12, 3);
		EVENT_MOUSE_CLICK = new ClientPacket(13, 6);
		OPNPC3 = new ClientPacket(14, 3);
		MESSAGE_PUBLIC = new ClientPacket(15, -1);
		UPDATE_PLAYER_MODEL = new ClientPacket(16, 26);
		IF_BUTTON2 = new ClientPacket(17, 8);
		IF_BUTTON3 = new ClientPacket(18, 8);
		OPOBJ5 = new ClientPacket(19, 7);
		field3395 = new ClientPacket(20, 1);
		IF_CRMVIEW = new ClientPacket(21, 22);
		CLAN_CHANNEL_KICK_USER = new ClientPacket(22, -1);
		IF_BUTTON8 = new ClientPacket(23, 8);
		IF_BUTTON10 = new ClientPacket(24, 8);
		CLAN_CHANNEL_FULL_REQUEST = new ClientPacket(25, 1);
		IF_SUBOP = new ClientPacket(26, 10);
		OPNPCT = new ClientPacket(27, 11);
		OPLOC5 = new ClientPacket(28, 7);
		OPLOC3 = new ClientPacket(29, 7);
		EVENT_CAMERA_POSITION = new ClientPacket(30, 4);
		MUSIC_PLAYING = new ClientPacket(31, 4);
		SET_HEADING = new ClientPacket(32, 1);
		field3446 = new ClientPacket(33, 2);
		OPLOC2 = new ClientPacket(34, 7);
		BUG_REPORT = new ClientPacket(35, -2);
		CHAT_SENDABUSEREPORT = new ClientPacket(36, -1);
		OPNPC1 = new ClientPacket(37, 3);
		RESUME_NAMEDIALOG = new ClientPacket(38, -1);
		OPNPC4 = new ClientPacket(39, 3);
		OPLOC4 = new ClientPacket(40, 7);
		OPPLAYER3 = new ClientPacket(41, 3);
		field3417 = new ClientPacket(42, 7);
		CLICK_WORLD_MAP = new ClientPacket(43, 4);
		EVENT_WINDOW_SETTING = new ClientPacket(44, 5);
		OPLOCE = new ClientPacket(45, 2);
		CLAN_SETTINGS_FULL_REQUEST = new ClientPacket(46, 1);
		CLAN_SETTINGS_ADD_BANNED_FROM_CHANNEL = new ClientPacket(47, -1);
		OPPLAYER5 = new ClientPacket(48, 3);
		MAP_BUILD_COMPLETE = new ClientPacket(49, 0);
		CHAT_SENDPRIVATE = new ClientPacket(50, -2);
		IF_BUTTONT = new ClientPacket(51, 16);
		OPPLAYERU = new ClientPacket(52, 11);
		WIDGET_TYPE = new ClientPacket(53, 4);
		CLOSE_MODAL = new ClientPacket(54, 0);
		field3430 = new ClientPacket(55, -1);
		field3463 = new ClientPacket(56, -1);
		RESUME_COUNTDIALOG = new ClientPacket(57, 4);
		EVENT_MOUSE_IDLE = new ClientPacket(58, 0);
		OPOBJU = new ClientPacket(59, 15);
		OPPLAYER2 = new ClientPacket(60, 3);
		RESUME_OBJDIALOG = new ClientPacket(61, 2);
		REFLECTION_CHECK_REPLY = new ClientPacket(62, -1);
		IF_BUTTON4 = new ClientPacket(63, 8);
		OPPLAYERT = new ClientPacket(64, 11);
		IGNORE_DELUSER = new ClientPacket(65, -1);
		RESUME_PAUSEBUTTON = new ClientPacket(66, 6);
		IF_BUTTON7 = new ClientPacket(67, 8);
		FRIEND_ADDUSER = new ClientPacket(68, -1);
		MOVE_GAMECLICK = new ClientPacket(69, -1);
		FRIEND_CHAT_JOIN_LEAVE = new ClientPacket(70, -1);
		MINIMAP_CLICK = new ClientPacket(71, -1);
		OPNPC2 = new ClientPacket(72, 3);
		OPOBJ2 = new ClientPacket(73, 7);
		CHAT_SETFILTER = new ClientPacket(74, 3);
		IGNORE_LIST_ADD = new ClientPacket(75, -1);
		NO_TIMEOUT = new ClientPacket(76, 0);
		OPOBJE = new ClientPacket(77, 6);
		TELEPORT = new ClientPacket(78, 9);
		CLAN_KICKUSER = new ClientPacket(79, -1);
		IF_BUTTON1 = new ClientPacket(80, 8);
		FREECAM_EXIT = new ClientPacket(81, 0);
		EVENT_APPLET_FOCUS = new ClientPacket(82, 1);
		DOCHEAT = new ClientPacket(83, -1);
		MOUSE_MOVE = new ClientPacket(84, -1);
		IF_BUTTON6 = new ClientPacket(85, 8);
		IF_BUTTON9 = new ClientPacket(86, 8);
		OPNPCE = new ClientPacket(87, 2);
		OPOBJ4 = new ClientPacket(88, 7);
		OPLOC1 = new ClientPacket(89, 7);
		IF_BUTTON5 = new ClientPacket(90, 8);
		RESUME_STRINGDIALOG = new ClientPacket(91, -1);
		OPPLAYER8 = new ClientPacket(92, 3);
		MOUSE_WHEEL = new ClientPacket(93, 2);
		OPOBJT = new ClientPacket(94, 15);
		EVENT_KEYBOARD = new ClientPacket(95, -2);
		DETECT_MODIFIED_CLIENT = new ClientPacket(96, 4);
		OPLOCU = new ClientPacket(97, 15);
		LOGIN_TIMINGS = new ClientPacket(98, -1);
		OPOBJ1 = new ClientPacket(99, 7);
		OPPLAYER4 = new ClientPacket(100, 3);
		IF_BUTTONX = new ClientPacket(101, 9);
	}

	ClientPacket(int var1, int var2) {
		this.id = var1;
		this.length = var2;
	}
}
