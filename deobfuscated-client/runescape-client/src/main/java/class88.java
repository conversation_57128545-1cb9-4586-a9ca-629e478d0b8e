import net.runelite.mapping.Export;
import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("dm")
public class class88 {
	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1231;
	@ObfuscatedName("aj")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1242;
	@ObfuscatedName("an")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1238;
	@ObfuscatedName("ai")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1233;
	@ObfuscatedName("al")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1230;
	@ObfuscatedName("ac")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1235;
	@ObfuscatedName("aa")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1236;
	@ObfuscatedName("am")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1237;
	@ObfuscatedName("ah")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1241;
	@ObfuscatedName("ag")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1239;
	@ObfuscatedName("au")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1247;
	@ObfuscatedName("ar")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1234;
	@ObfuscatedName("ad")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1240;
	@ObfuscatedName("af")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	@Export("field1390")
	static final class88 field1390;
	@ObfuscatedName("ak")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1244;
	@ObfuscatedName("az")
	@ObfuscatedSignature(
		descriptor = "Ldm;"
	)
	static final class88 field1245;
	@ObfuscatedName("aw")
	@ObfuscatedGetter(
		intValue = 1413942161
	)
	int field1246;

	static {
		field1231 = new class88(0);
		field1242 = new class88(1);
		field1238 = new class88(2);
		field1233 = new class88(3);
		field1230 = new class88(4);
		field1235 = new class88(5);
		field1236 = new class88(6);
		field1237 = new class88(7);
		field1241 = new class88(8);
		field1239 = new class88(9);
		field1247 = new class88(10);
		field1234 = new class88(11);
		field1240 = new class88(12);
		field1390 = new class88(13);
		field1244 = new class88(14);
		field1245 = new class88(15);
	}

	class88(int var1) {
		this.field1246 = var1;
	}
}
