import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;

@ObfuscatedName("oo")
public class class387 {
	@ObfuscatedName("aj")
	@ObfuscatedGetter(
		intValue = 2116598355
	)
	static final int field4252;
	@ObfuscatedName("an")
	@ObfuscatedGetter(
		intValue = -1998126187
	)
	static final int field4253;
	@ObfuscatedName("ae")
	@ObfuscatedGetter(
		intValue = -1630360769
	)
	static final int field4269;

	static {
		field4252 = Coord.method7263(256);
		field4253 = Coord.method7263(256);
		int var0 = field4252;
		var0 = field4253;
		Coord.method7263(100);
		Coord.method7263(200);
		Coord.method7263(1);
		Coord.method7263(1);
		field4269 = Coord.method7274(100);
		Coord.method7225(field4269);
		Coord.method7263(1);
		Coord.method7263(1);
	}
}
