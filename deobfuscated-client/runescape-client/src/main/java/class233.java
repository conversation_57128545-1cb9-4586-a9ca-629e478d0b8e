import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("iz")
public class class233 {
	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "Liz;"
	)
	public static final class233 field2522;
	@ObfuscatedName("aj")
	@ObfuscatedSignature(
		descriptor = "Liz;"
	)
	public static final class233 field2523;

	static {
		field2522 = new class233();
		field2523 = new class233();
	}

	class233() {
	}

	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "()[Liz;"
	)
	public static class233[] method4874() {
		return new class233[]{field2522, field2523};
	}
}
