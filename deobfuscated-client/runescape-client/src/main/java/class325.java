import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("ml")
public class class325 {
	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3493;
	@ObfuscatedName("aj")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3486;
	@ObfuscatedName("an")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3482;
	@ObfuscatedName("ai")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3483;
	@ObfuscatedName("al")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3484;
	@ObfuscatedName("ac")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3485;
	@ObfuscatedName("aa")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3490;
	@ObfuscatedName("am")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3487;
	@ObfuscatedName("ah")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3491;
	@ObfuscatedName("ag")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3489;
	@ObfuscatedName("au")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3492;
	@ObfuscatedName("ar")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3480;
	@ObfuscatedName("ad")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3481;
	@ObfuscatedName("af")
	@ObfuscatedSignature(
		descriptor = "Lml;"
	)
	public static final class325 field3488;

	static {
		field3493 = new class325(2);
		field3486 = new class325(4);
		field3482 = new class325(7);
		field3483 = new class325(7);
		field3484 = new class325(7);
		field3485 = new class325(20);
		field3490 = new class325(14);
		field3487 = new class325(11);
		field3491 = new class325(14);
		field3489 = new class325(4);
		field3492 = new class325(24);
		field3480 = new class325(-2);
		field3481 = new class325(17);
		field3488 = new class325(6);
	}

	@ObfuscatedSignature(
		descriptor = "(I)V",
		garbageValue = "2"
	)
	class325(int var1) {
	}

	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "(Lps;I)V",
		garbageValue = "-1710506864"
	)
	public static void method6831(AbstractArchive var0) {
	}
}
