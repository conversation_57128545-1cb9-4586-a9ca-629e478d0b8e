import net.runelite.mapping.ObfuscatedName;

@ObfuscatedName("vv")
public class class552 {
	@ObfuscatedName("an")
	static final char[] field5564;
	@ObfuscatedName("ai")
	static final char[] field5561;

	static {
		field5564 = new char[]{' ', ' ', '_', '-', 'à', 'á', 'â', 'ä', 'ã', 'À', 'Á', 'Â', 'Ä', 'Ã', 'è', 'é', 'ê', 'ë', 'È', 'É', 'Ê', 'Ë', 'í', 'î', 'ï', 'Í', 'Î', 'Ï', 'ò', 'ó', 'ô', 'ö', 'õ', 'Ò', 'Ó', 'Ô', 'Ö', 'Õ', 'ù', 'ú', 'û', 'ü', 'Ù', 'Ú', 'Û', 'Ü', 'ç', 'Ç', 'ÿ', 'Ÿ', 'ñ', 'Ñ', 'ß'};
		field5561 = new char[]{'[', ']', '#'};
	}
}
