import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("vq")
public interface class558 {
	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "(Ljava/lang/Object;Lwt;I)V",
		garbageValue = "872276318"
	)
	void vmethod10652(Object var1, Buffer var2);

	@ObfuscatedName("aj")
	@ObfuscatedSignature(
		descriptor = "(Lwt;B)Ljava/lang/Object;",
		garbageValue = "-108"
	)
	Object vmethod10657(Buffer var1);
}
