import net.runelite.mapping.Export;
import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("pz")
public class class403 {
	@ObfuscatedName("ap")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	static final class403 field4734;
	@ObfuscatedName("aj")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4718;
	@ObfuscatedName("an")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4719;
	@ObfuscatedName("ai")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4720;
	@ObfuscatedName("al")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4721;
	@ObfuscatedName("ac")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4738;
	@ObfuscatedName("aa")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4723;
	@ObfuscatedName("am")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4724;
	@ObfuscatedName("ah")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4717;
	@ObfuscatedName("ag")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4739;
	@ObfuscatedName("au")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4727;
	@ObfuscatedName("ar")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4725;
	@ObfuscatedName("ad")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4729;
	@ObfuscatedName("af")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4730;
	@ObfuscatedName("ak")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4726;
	@ObfuscatedName("az")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4728;
	@ObfuscatedName("aw")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4732;
	@ObfuscatedName("at")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4741;
	@ObfuscatedName("ae")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4735;
	@ObfuscatedName("av")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4736;
	@ObfuscatedName("ao")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4737;
	@ObfuscatedName("as")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4733;
	@ObfuscatedName("ax")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4722;
	@ObfuscatedName("ab")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	public static final class403 field4740;
	@ObfuscatedName("aq")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	static final class403 field4731;
	@ObfuscatedName("ay")
	@ObfuscatedSignature(
		descriptor = "Lpz;"
	)
	static final class403 field4742;
	@ObfuscatedName("ki")
	@ObfuscatedSignature(
		descriptor = "Lpu;"
	)
	static Archive field4745;
	@ObfuscatedName("pf")
	@ObfuscatedSignature(
		descriptor = "[Lwy;"
	)
	@Export("mapSceneSprites")
	static IndexedSprite[] mapSceneSprites;
	@ObfuscatedName("ba")
	@ObfuscatedGetter(
		intValue = -897419331
	)
	public final int field4743;

	static {
		field4734 = new class403(255);
		field4718 = new class403(0);
		field4719 = new class403(1);
		field4720 = new class403(2);
		field4721 = new class403(3);
		field4738 = new class403(4);
		field4723 = new class403(5);
		field4724 = new class403(6);
		field4717 = new class403(7);
		field4739 = new class403(8);
		field4727 = new class403(9);
		field4725 = new class403(10);
		field4729 = new class403(11);
		field4730 = new class403(12);
		field4726 = new class403(13);
		field4728 = new class403(14);
		field4732 = new class403(15);
		field4741 = new class403(17);
		field4735 = new class403(18);
		field4736 = new class403(19);
		field4737 = new class403(20);
		field4733 = new class403(21);
		field4722 = new class403(22);
		field4740 = new class403(23);
		field4731 = new class403(24);
		field4742 = new class403(25);
	}

	class403(int var1) {
		this.field4743 = var1;
	}
}
