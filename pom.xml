<?xml version="1.0" encoding="UTF-8"?>
<!--
 Copyright (c) 2016-2017, <PERSON> <<EMAIL>>
 All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions are met:

 1. Redistributions of source code must retain the above copyright notice, this
    list of conditions and the following disclaimer.
 2. Redistributions in binary form must reproduce the above copyright notice,
    this list of conditions and the following disclaimer in the documentation
    and/or other materials provided with the distribution.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 WARRANTIES OF ME<PERSON><PERSON><PERSON><PERSON><PERSON>ITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES
 (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON>UBSTITUTE GOODS OR SERVICES;
 LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>net.runelite</groupId>
	<artifactId>runelite-parent</artifactId>
	<version>1.11.13-SNAPSHOT</version>
	<packaging>pom</packaging>

	<name>RuneLite</name>
	<description>Open source RuneScape client</description>
	<url>http://runelite.net</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.build.outputTimestamp>1</project.build.outputTimestamp>
		<lombok.version>1.18.30</lombok.version>
		<logback.version>1.2.9</logback.version>
		<slf4j.version>1.7.25</slf4j.version>
		<flatlaf.version>3.2.5-rl4</flatlaf.version>

		<maven.javadoc.skip>true</maven.javadoc.skip>
		<checkstyle.skip>true</checkstyle.skip>
		<glslang.path></glslang.path>
	</properties>

	<licenses>
		<license>
			<name>2-Clause BSD License</name>
			<url>https://opensource.org/licenses/BSD-2-Clause</url>
		</license>
	</licenses>

	<inceptionYear>2014</inceptionYear>

	<scm>
		<url>https://github.com/runelite/runelite</url>
		<connection>scm:git:git://github.com/runelite/runelite</connection>
		<developerConnection>scm:git:**************:runelite/runelite</developerConnection>
		<tag>HEAD</tag>
	</scm>

	<developers>
		<developer>
			<id>Adam-</id>
			<name>Adam</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<issueManagement>
		<system>GitHub Issues</system>
		<url>https://github.com/runelite/runelite/issues</url>
	</issueManagement>

	<repositories>
		<repository>
			<id>central</id>
			<name>Central Repository</name>
			<url>https://repo.maven.apache.org/maven2</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>runelite</id>
			<name>RuneLite</name>
			<url>https://repo.runelite.net</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>jcenter</id>
			<name>JCenter Repository</name>
			<url>https://jcenter.bintray.com/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>central</id>
			<name>Central Repository</name>
			<url>https://repo.maven.apache.org/maven2</url>
			<releases>
				<updatePolicy>never</updatePolicy>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
		<pluginRepository>
			<id>runelite-plugins</id>
			<name>RuneLite Plugins</name>
			<url>https://repo.runelite.net</url>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

	<modules>
		<module>cache</module>
		<module>runelite-api</module>
		<module>runelite-client</module>
		<module>runelite-jshell</module>
		<module>runelite-maven-plugin</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>23.2-jre</version>
			</dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>com.google.code.gson</groupId>
				<artifactId>gson</artifactId>
				<version>2.8.5</version>
			</dependency>
			<dependency>
				<groupId>com.google.code.findbugs</groupId>
				<artifactId>jsr305</artifactId>
				<version>3.0.2</version>
			</dependency>
			<dependency>
				<groupId>ch.qos.logback</groupId>
				<artifactId>logback-classic</artifactId>
				<version>${logback.version}</version>
			</dependency>
			<dependency>
				<groupId>ch.qos.logback</groupId>
				<artifactId>logback-core</artifactId>
				<version>${logback.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-api</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-simple</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.inject</groupId>
				<artifactId>guice-bom</artifactId>
				<version>4.1.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.2</version>
				<configuration>
					<enableAssertions>true</enableAssertions>
					<argLine>-Xmx512m -Duser.language=en -Duser.region=US</argLine>
					<systemProperties>
						<glslang.path>${glslang.path}</glslang.path>
					</systemProperties>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-release-plugin</artifactId>
				<version>2.5.3</version>
			</plugin>
			<plugin>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok-maven-plugin</artifactId>
				<version>*********</version>
				<configuration>
					<!-- Delomboked sources are only used for javadoc generation, and can be
					skipped when javadoc is not being run. -->
					<skip>${maven.javadoc.skip}</skip>
					<sourceDirectory>${project.basedir}/src/main/java</sourceDirectory>
					<!-- The default output directory is in generated-sources, which is
					treated as a source root, even with addOutputDirectory false. So,
					place it in top level build directory instead. -->
					<outputDirectory>${project.build.directory}/delombok</outputDirectory>
					<addOutputDirectory>false</addOutputDirectory>
				</configuration>
				<executions>
					<execution>
						<phase>generate-sources</phase>
						<goals>
							<goal>delombok</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<!--			<plugin>-->
			<!--				<groupId>org.apache.maven.plugins</groupId>-->
			<!--				<artifactId>maven-javadoc-plugin</artifactId>-->
			<!--				<version>3.5.0</version>-->
			<!--				<configuration>-->
			<!--					&lt;!&ndash; Javadoc the delombok sources &ndash;&gt;-->
			<!--					<sourcepath>${project.build.directory}/delombok</sourcepath>-->
			<!--					&lt;!&ndash;default bottom uses outputTimestamp for the year and generates 2014-1970-->
			<!--					<bottom>Copyright &#169; {inceptionYear}</bottom>-->
			<!--				</configuration>-->
			<!--				&lt;!&ndash; maven-release-plugin runs maven-javadoc-plugin:jar as one of its-->
			<!--				     release steps, so we will run it as well for the CI. Note this-->
			<!--				     defaults to disabled due to the maven.javadoc.skip property above. &ndash;&gt;-->
			<!--				<executions>-->
			<!--					<execution>-->
			<!--						<id>attach-javadocs</id>-->
			<!--						<goals>-->
			<!--							<goal>jar</goal>-->
			<!--						</goals>-->
			<!--					</execution>-->
			<!--				</executions>-->
			<!--			</plugin>-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<version>2.17</version>
				<dependencies>
					<dependency>
						<groupId>com.puppycrawl.tools</groupId>
						<artifactId>checkstyle</artifactId>
						<version>8.3</version>
					</dependency>
				</dependencies>
				<executions>
					<execution>
						<id>verify-style</id>
						<phase>process-classes</phase>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<configLocation>checkstyle.xml</configLocation>
					<sourceDirectories>
						<!-- exclude generated sources from checkstyle https://stackoverflow.com/a/30406454/7189686 -->
						<sourceDirectory>${project.build.sourceDirectory}</sourceDirectory>
					</sourceDirectories>
					<includeTestSourceDirectory>true</includeTestSourceDirectory>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<executions>
					<execution>
						<id>default-compile</id>
						<phase>none</phase>
					</execution>
					<execution>
						<id>default-testCompile</id>
						<phase>none</phase>
					</execution>
					<execution>
						<id>compile</id>
						<phase>compile</phase>
						<goals>
							<goal>compile</goal>
						</goals>
					</execution>
					<execution>
						<id>testCompile</id>
						<phase>test-compile</phase>
						<goals>
							<goal>testCompile</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<release>11</release>
				</configuration>
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-assembly-plugin</artifactId>
					<version>3.2.0</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-jar-plugin</artifactId>
					<version>3.2.0</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.6.1</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-deploy-plugin</artifactId>
					<version>2.8.2</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-plugin-plugin</artifactId>
					<version>3.6.0</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-pmd-plugin</artifactId>
					<version>3.22.0</version>
					<dependencies>
						<dependency>
							<groupId>net.sourceforge.pmd</groupId>
							<artifactId>pmd-core</artifactId>
							<version>7.2.0</version>
						</dependency>
						<dependency>
							<groupId>net.sourceforge.pmd</groupId>
							<artifactId>pmd-java</artifactId>
							<version>7.2.0</version>
						</dependency>
					</dependencies>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
</project>
