<?xml version="1.0" encoding="UTF-8"?>
<!--
 Copyright (c) 2016-2017, <PERSON> <<EMAIL>>
 All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions are met:

 1. Redistributions of source code must retain the above copyright notice, this
    list of conditions and the following disclaimer.
 2. Redistributions in binary form must reproduce the above copyright notice,
    this list of conditions and the following disclaimer in the documentation
    and/or other materials provided with the distribution.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 WARRANTIES OF ME<PERSON><PERSON><PERSON><PERSON><PERSON>ITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES
 (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON>UBSTITUTE GOODS OR SERVICES;
 LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>net.runelite</groupId>
		<artifactId>runelite-parent</artifactId>
		<version>1.11.13-SNAPSHOT</version>
	</parent>

	<artifactId>client</artifactId>
	<name>RuneLite Client</name>

	<properties>
		<jarsigner.skip>true</jarsigner.skip>
		<pmd.skip>true</pmd.skip>
		<git.commit.id.abbrev>nogit</git.commit.id.abbrev>
		<git.dirty>false</git.dirty>
		<shade.skip>false</shade.skip>
		<microbot.version>1.9.7</microbot.version>
		<microbot.commit.sha>nogit</microbot.commit.sha>
	</properties>

	<distributionManagement>
		<snapshotRepository>
			<id>microbot-release</id>
			<url>http://**************:8081/repository/microbot-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>

	<dependencies>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20230227</version>
		</dependency>
		<!-- RoaringBitmap library -->
		<dependency>
			<groupId>org.roaringbitmap</groupId>
			<artifactId>RoaringBitmap</artifactId>
			<version>0.9.44</version>
		</dependency>
		<!-- CFR decompiler library -->
		<dependency>
			<groupId>org.benf</groupId>
			<artifactId>cfr</artifactId>
			<version>0.152</version>
		</dependency>
		<dependency>
			<groupId>com.microsoft.signalr</groupId>
			<artifactId>signalr</artifactId>
			<version>7.0.4</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.13.5</version>
		</dependency>
		<dependency>
			<groupId>com.github.joonasvali.naturalmouse</groupId>
			<artifactId>naturalmouse</artifactId>
			<version>2.0.3</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
		</dependency>
		<dependency>
			<groupId>net.sf.jopt-simple</groupId>
			<artifactId>jopt-simple</artifactId>
			<version>5.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<exclusions>
				<!--
				compile time annotations for static analysis in Guava
				https://github.com/google/guava/wiki/UseGuavaInYourBuild#what-about-guavas-own-dependencies
				-->
				<exclusion>
					<groupId>com.google.code.findbugs</groupId>
					<artifactId>jsr305</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.errorprone</groupId>
					<artifactId>error_prone_annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.j2objc</groupId>
					<artifactId>j2objc-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.codehaus.mojo</groupId>
					<artifactId>animal-sniffer-annotations</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.xerial</groupId>
			<artifactId>sqlite-jdbc</artifactId>
			<version>3.42.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.inject</groupId>
			<artifactId>guice</artifactId>
			<classifier>no_aop</classifier>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
		<dependency>
			<groupId>net.runelite</groupId>
			<artifactId>flatlaf</artifactId>
			<version>${flatlaf.version}</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.2</version>
		</dependency>
		<dependency>
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna</artifactId>
			<version>5.9.0</version>
		</dependency>
		<dependency>
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna-platform</artifactId>
			<version>5.9.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.code.findbugs</groupId>
			<artifactId>jsr305</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jetbrains</groupId>
			<artifactId>annotations</artifactId>
			<version>23.0.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-javalite</artifactId>
			<version>3.21.12</version>
		</dependency>
		<dependency>
			<groupId>net.runelite</groupId>
			<artifactId>rlawt</artifactId>
			<version>1.5</version>
		</dependency>

		<!-- lwjgl core -->
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl</artifactId>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl</artifactId>
			<classifier>natives-linux</classifier>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl</artifactId>
			<classifier>natives-macos</classifier>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl</artifactId>
			<classifier>natives-macos-arm64</classifier>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl</artifactId>
			<classifier>natives-windows-x86</classifier>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl</artifactId>
			<classifier>natives-windows</classifier>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl</artifactId>
			<classifier>natives-windows-arm64</classifier>
			<scope>runtime</scope>
		</dependency>

		<!-- lwjgl opengl -->
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl-opengl</artifactId>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl-opengl</artifactId>
			<classifier>natives-linux</classifier>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl-opengl</artifactId>
			<classifier>natives-macos</classifier>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl-opengl</artifactId>
			<classifier>natives-macos-arm64</classifier>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl-opengl</artifactId>
			<classifier>natives-windows-x86</classifier>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl-opengl</artifactId>
			<classifier>natives-windows</classifier>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl-opengl</artifactId>
			<classifier>natives-windows-arm64</classifier>
			<scope>runtime</scope>
		</dependency>

		<!-- lwjgl opencl -->
		<dependency>
			<groupId>org.lwjgl</groupId>
			<artifactId>lwjgl-opencl</artifactId>
		</dependency>

		<dependency>
			<groupId>net.runelite</groupId>
			<artifactId>runelite-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>net.runelite</groupId>
			<artifactId>jshell</artifactId>
			<version>${project.version}</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>net.runelite</groupId>
			<artifactId>injected-client</artifactId>
			<version>${project.version}</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>net.runelite.arn</groupId>
			<artifactId>http-api</artifactId>
			<version>1.2.21</version>
		</dependency>
		<dependency>
			<groupId>net.runelite</groupId>
			<artifactId>discord</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>net.runelite</groupId>
			<artifactId>orange-extensions</artifactId>
			<version>1.1</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-library</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>3.1.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.google.inject.extensions</groupId>
			<artifactId>guice-testlib</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.google.inject.extensions</groupId>
			<artifactId>guice-grapher</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>3.14.9</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>mockwebserver</artifactId>
			<version>3.14.9</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.13.5</version>
			<scope>compile</scope>
		</dependency>

		<!-- Microbot -->
		<!-- ASM -->
		<dependency>
			<groupId>org.ow2.asm</groupId>
			<artifactId>asm</artifactId>
			<version>9.0</version>
		</dependency>
		<dependency>
			<groupId>org.ow2.asm</groupId>
			<artifactId>asm-util</artifactId>
			<version>9.0</version>
		</dependency>
		<dependency>
			<groupId>org.ow2.asm</groupId>
			<artifactId>asm-commons</artifactId>
			<version>9.0</version>
		</dependency>
		<!-- Microbot end -->

	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.lwjgl</groupId>
				<artifactId>lwjgl-bom</artifactId>
				<version>3.3.2</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<excludes>
					<exclude>logback.xml</exclude>
				</excludes>
				<filtering>true</filtering>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>logback.xml</include>
				</includes>
				<filtering>false</filtering>
			</resource>
		</resources>
		<plugins>
<!--			<plugin>-->
<!--				<groupId>org.apache.maven.plugins</groupId>-->
<!--				<artifactId>maven-jar-plugin</artifactId>-->
<!--				<configuration>-->
<!--					<excludes>-->
<!--						<exclude>**/.clang-format</exclude>-->
<!--					</excludes>-->
<!--				</configuration>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<goals>-->
<!--							<goal>test-jar</goal>-->
<!--						</goals>-->
<!--					</execution>-->
<!--				</executions>-->
<!--			</plugin>-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.0.2</version>
				<configuration>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>ttf</nonFilteredFileExtension>
						<nonFilteredFileExtension>png</nonFilteredFileExtension>
						<nonFilteredFileExtension>gif</nonFilteredFileExtension>
						<nonFilteredFileExtension>wav</nonFilteredFileExtension>
						<nonFilteredFileExtension>zip</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>

			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>3.1.0</version>
				<executions>
					<execution>
						<id>copy-example-plugin</id>
						<phase>verify</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<!-- Copy the main shaded JAR (the executable one) -->
								<copy file="${project.build.directory}/microbot-${microbot.version}.jar"
									  todir="C:\Users\<USER>\Documents\git\containerized_runescape\runelite_package"
									  failonerror="false"/>
								<!-- Copy the example plugin JAR -->
								<copy file="${project.build.directory}/example-plugin.jar"
									  todir="C:\Users\<USER>\Documents\git\containerized_runescape\external_plugins"
									  failonerror="false"/>
								<!-- Copy the tutorialislandexternal plugin JAR -->
								<copy file="${project.build.directory}/tutorialislandexternal-plugin.jar"
									  todir="C:\Users\<USER>\Documents\git\containerized_runescape\external_plugins"
									  failonerror="false"/>

							</target>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<version>3.3.0</version>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<outputFile>target/microbot-${microbot.version}.jar</outputFile>
							<skip>${shade.skip}</skip>
							<shadedArtifactAttached>true</shadedArtifactAttached>
							<shadedClassifierName>shaded</shadedClassifierName>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/versions/**/module-info.class</exclude>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
										<exclude>net/runelite/client/plugins/microbot/sideloaded/**</exclude>
									</excludes>
								</filter>
							</filters>

							<transformers>
								<transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
									<manifestEntries>
										<Main-Class>net.runelite.client.RuneLite</Main-Class>
										<Multi-Release>true</Multi-Release>
									</manifestEntries>
								</transformer>
							</transformers>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<version>3.2.0</version>
				<executions>
				</executions>
			</plugin>

			<!-- Dynamic sideloaded plugin builder -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>3.1.0</version>
				<executions>
					<execution>
						<id>build-dynamic-sideloaded-plugins</id>
						<phase>package</phase>
						<goals>
							<goal>java</goal>
						</goals>
						<configuration>
							<mainClass>net.runelite.client.plugins.microbot.util.SideloadedPluginBuilder</mainClass>
							<arguments>
								<argument>${project.build.outputDirectory}</argument>
								<argument>${project.build.directory}</argument>
								<argument>C:\Users\<USER>\Documents\git\containerized_runescape\x11-setup</argument>
							</arguments>
							<classpathScope>compile</classpathScope>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jarsigner-plugin</artifactId>
				<version>1.4</version>
				<executions>
					<execution>
						<id>sign</id>
						<goals>
							<goal>sign</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<skip>${jarsigner.skip}</skip>
					<keystore>${jarsigner.keystore}</keystore>
					<alias>${jarsigner.alias}</alias>
					<storepass>${jarsigner.storepass}</storepass>
					<keypass>${jarsigner.keypass}</keypass>
				</configuration>
			</plugin>
			<plugin>
				<groupId>net.runelite</groupId>
				<artifactId>runelite-maven-plugin</artifactId>
				<version>${project.version}</version>
				<executions>
					<execution>
						<id>assemble</id>
						<goals>
							<goal>assemble</goal>
						</goals>
						<configuration>
							<scriptDirectory>src/main/scripts</scriptDirectory>
							<outputDirectory>${project.build.outputDirectory}/runelite</outputDirectory>
							<componentsFile>../runelite-api/src/main/interfaces/interfaces.toml</componentsFile>
						</configuration>
					</execution>
					<execution>
						<id>build-index</id>
						<goals>
							<goal>build-index</goal>
						</goals>
						<configuration>
							<archiveOverlayDirectory>${project.build.outputDirectory}/runelite</archiveOverlayDirectory>
							<indexFile>${project.build.outputDirectory}/runelite/index</indexFile>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<configuration>
					<sourceDirectories>
						<!-- exclude generated sources from checkstyle https://stackoverflow.com/a/30406454/7189686 -->
						<sourceDirectory>${project.build.sourceDirectory}</sourceDirectory>
						<sourceDirectory>${project.basedir}/src/main/java11</sourceDirectory>
					</sourceDirectories>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-pmd-plugin</artifactId>
				<configuration>
					<failOnViolation>true</failOnViolation>
					<printFailingErrors>true</printFailingErrors>
					<rulesets>
						<ruleset>${basedir}/pmd-ruleset.xml</ruleset>
					</rulesets>
					<linkXRef>false</linkXRef>
					<analysisCache>true</analysisCache>
					<excludes>
						<exclude>**/RuntimeTypeAdapterFactory.java</exclude>
						<exclude>net/runelite/client/party/Party.java</exclude>
					</excludes>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>pl.project13.maven</groupId>
				<artifactId>git-commit-id-plugin</artifactId>
				<version>2.2.6</version>
				<executions>
					<execution>
						<id>query-git-info</id>
						<goals>
							<goal>revision</goal>
						</goals>
						<configuration>
							<failOnNoGitDirectory>false</failOnNoGitDirectory>
							<failOnUnableToExtractRepoInfo>false</failOnUnableToExtractRepoInfo>
							<gitDescribe>
								<skip>true</skip>
							</gitDescribe>
							<includeOnlyProperties>
								<includeOnlyProperty>git.commit.id.abbrev</includeOnlyProperty>
								<includeOnlyProperty>git.dirty</includeOnlyProperty>
							</includeOnlyProperties>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<executions>
					<execution>
						<id>default-compile</id>
						<phase>none</phase>
					</execution>
					<execution>
						<id>default-testCompile</id>
						<phase>none</phase>
					</execution>
					<execution>
						<id>compile</id>
						<phase>compile</phase>
						<goals>
							<goal>compile</goal>
						</goals>
					</execution>
					<execution>
						<id>testCompile</id>
						<phase>test-compile</phase>
						<goals>
							<goal>testCompile</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
