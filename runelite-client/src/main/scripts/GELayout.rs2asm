.id                       806
.int_arg_count            7
.obj_arg_count            0
   iload                  6
   invoke                 41
   get_varbit             4439
   iconst                 1
   sub                   
   istore                 7
   iconst                 105
   iconst                 118
   iconst                 150
   iload                  7
   enum                  
   istore                 8
   iload                  8
   iconst                 -1
   if_icmpeq              LABEL16
   jump                   LABEL37
LABEL16:
   iconst                 1
   iload                  2
   if_sethide            
   iconst                 0
   iload                  3
   if_sethide            
   iconst                 1
   iload                  4
   if_sethide            
   iconst                 1
   iload                  5
   if_sethide            
   iload                  0
   iload                  1
   cc_find               
   iconst                 1
   if_icmpeq              LABEL34
   jump                   LABEL36
LABEL34:
   sconst                 "Grand Exchange"
   sconst                 "setGETitle"  ;
   runelite_callback     ;
   cc_settext            
LABEL36:
   return                
LABEL37:
   iconst                 0
   iload                  2
   if_sethide            
   iconst                 0
   iload                  2
   if_settrans           
   iconst                 -1
   istore                 9
   iconst                 0
   istore                 10
   iload                  7
   invoke                 5733
   istore                 10
   istore                 9
   iload                  7
   stockmarket_isofferempty
   iconst                 1
   if_icmpeq              LABEL56
   jump                   LABEL78
LABEL56:
   iload                  9
   iconst                 -1
   if_icmpeq              LABEL60
   jump                   LABEL78
LABEL60:
   iconst                 1
   iload                  3
   if_sethide            
   iconst                 1
   iload                  4
   if_sethide            
   iconst                 0
   iload                  5
   if_sethide            
   iload                  0
   iload                  1
   cc_find               
   iconst                 1
   if_icmpeq              LABEL75
   jump                   LABEL77
LABEL75:
   sconst                 "Grand Exchange: Set up offer"
   cc_settext            
LABEL77:
   return                
LABEL78:
   iconst                 1
   iload                  3
   if_sethide            
   iconst                 0
   iload                  4
   if_sethide            
   iconst                 1
   iload                  5
   if_sethide            
   iload                  0
   iload                  1
   cc_find               
   iconst                 1
   if_icmpeq              LABEL93
   jump                   LABEL95
LABEL93:
   sconst                 "Grand Exchange: Offer status"
   cc_settext            
LABEL95:
   return                
