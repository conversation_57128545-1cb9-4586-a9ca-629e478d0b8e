.id                       89
.int_arg_count            1
.obj_arg_count            0
   iconst                 0
   istore                 1
   iconst                 2
   istore                 2
   iconst                 103
   istore                 3
   iconst                 4
   istore                 4
   iconst                 23
   istore                 5
   invoke                 900
   istore                 6
   iconst                 103
   iconst                 105
   iconst                 1136
   iload                  6
   enum                  
   iconst                 0
   if_icmpgt              LABEL20
   jump                   LABEL69
LABEL20:
   iload                  6
   iconst                 1745
   if_icmpeq              LABEL24
   jump                   LABEL36
LABEL24:
   iconst                 0
   iconst                 102
   iconst                 103
   iconst                 105
   iconst                 1960
   iload                  6
   enum                  
   iconst                 30
   istore                 5
   istore                 1
   istore                 3
   istore                 2
LABEL36:
   iload                  6
   iconst                 1745
   if_icmpeq              LABEL40
   jump                   LABEL47
LABEL40:
   get_varc_int           1220
   iconst                 1
   if_icmpeq              LABEL44
   jump                   LABEL47
LABEL44:
   iconst                 4
   istore                 4
   jump                   LABEL69
LABEL47:
   get_varc_int           41
   iconst                 1337
   if_icmpeq              LABEL51
   jump                   LABEL60
LABEL51:
   invoke                 922
   iconst                 1
   if_icmpeq              LABEL55
   jump                   LABEL60
LABEL55:
   iload                  4
   iload                  5
   add                   
   istore                 4
   jump                   LABEL69
LABEL60:
   iload                  4
   iconst                 73
   iconst                 73
   iload                  6
   iconst                 10551392
   enum                  
   if_getheight          
   add                   
   istore                 4
LABEL69:
   iload                  4
   istore                 7
   iconst                 10682368
   if_getwidth           
   istore                 8
   iconst                 0
   istore                 9
   iconst                 105
   iconst                 73
   iconst                 580
   iload                  9
   enum                  
   istore                 10
   iconst                 0
   istore                 11
   sconst                 ""
   ostore                 0
   reboottimer           
   iconst                 0
   if_icmpgt              LABEL90
   jump                   LABEL167
LABEL90:
   reboottimer           
   iconst                 50
   div                   
   iconst                 60
   mod                   
   istore                 11
   iload                  11
   iconst                 10
   if_icmplt              LABEL100
   jump                   LABEL111
LABEL100:
   sconst                 "System update in: "
   reboottimer           
   iconst                 3000
   div                   
   tostring              
   sconst                 ":0"
   iload                  11
   tostring              
   join_string            4
   ostore                 0
   jump                   LABEL121
LABEL111:
   sconst                 "System update in: "
   reboottimer           
   iconst                 3000
   div                   
   tostring              
   sconst                 ":"
   iload                  11
   tostring              
   join_string            4
   ostore                 0
LABEL121:
   iload                  7
   oload                  0
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 16776960
   iconst                 1
   invoke                 199
   add                   
   istore                 7
   iload                  10
   if_clearops           
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouserepeat   
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouseleave    
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 0
   iload                  10
   if_setsize            
   iload                  9
   iconst                 1
   add                   
   istore                 9
   iconst                 105
   iconst                 73
   iconst                 580
   iload                  9
   enum                  
   istore                 10
LABEL167:
   iconst                 -1
   istore                 12
   iconst                 -1
   istore                 13
   sconst                 ""
   ostore                 1
   iconst                 0
   istore                 14
   sconst                 ""
   ostore                 2
   iconst                 0
   istore                 15
   sconst                 ""
   ostore                 3
   sconst                 ""
   ostore                 4
   iconst                 -1
   istore                 16
   iconst                 0
   istore                 17
   sconst                 "<col=00ffff>"
   ostore                 5
   sconst                 "<col=ffff00>"
   ostore                 6
   oload                  5
   oload                  6
   invoke                 4485
   ostore                 6
   ostore                 5
   get_varc_int           55
   get_varc_int           202
   if_icmpge              LABEL200
   jump                   LABEL332
LABEL200:
   get_varc_int           55
   clientclock           
   iconst                 3000
   sub                   
   if_icmpgt              LABEL206
   jump                   LABEL332
LABEL206:
   iconst                 14
   chat_gethistorylength 
   iconst                 0
   if_icmpgt              LABEL211
   jump                   LABEL332
LABEL211:
   iconst                 14
   iconst                 0
   chat_gethistoryex_bytypeandline
   istore                 15
   ostore                 2
   istore                 14
   ostore                 0
   ostore                 3
   ostore                 1
   istore                 13
   istore                 12
   iload                  12
   iconst                 -1
   if_icmpne              LABEL226
   jump                   LABEL332
LABEL226:
   oload                  0
   invoke                 2066
   istore                 16
   ostore                 4
   ostore                 0
   iload                  16
   iconst                 4
   if_icmpne              LABEL238
   reboottimer           
   iconst                 0
   if_icmple              LABEL238
   jump                   LABEL332
LABEL238:
   iload                  7
   oload                  2
   oload                  6
   oload                  0
   sconst                 "</col>"
   join_string            3
   sconst                 "null"
   invoke                 4742
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 16776960
   iconst                 1
   invoke                 199
   add                   
   istore                 7
   iload                  10
   if_clearops           
   oload                  4
   string_length         
   iconst                 0
   if_icmpgt              LABEL267
   jump                   LABEL296
LABEL267:
   iload                  16
   iconst                 -1
   if_icmpne              LABEL271
   jump                   LABEL296
LABEL271:
   iconst                 6
   sconst                 "Open"
   iload                  10
   if_setop              
   iconst                 7
   sconst                 "Check"
   iload                  10
   if_setop              
   iconst                 2065
   iload                  10
   if_getlayer           
   iload                  9
   iconst                 16777215
   sconst                 "iii"
   iload                  10
   if_setonmouserepeat   
   iconst                 2065
   iload                  10
   if_getlayer           
   iload                  9
   iconst                 16776960
   sconst                 "iii"
   iload                  10
   if_setonmouseleave    
   jump                   LABEL304
LABEL296:
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouserepeat   
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouseleave    
LABEL304:
   iconst                 10
   sconst                 "Clear history"
   iload                  10
   if_setop              
   sconst                 "<col=ff9040>"
   sconst                 "Notification"
   sconst                 "</col>"
   join_string            3
   iload                  10
   if_setopbase          
   iconst                 2064
   iconst                 -2147483644
   iconst                 10
   oload                  4
   iload                  16
   sconst                 "iisi"
   iload                  10
   if_setonop            
   iload                  9
   iconst                 1
   add                   
   istore                 9
   iconst                 105
   iconst                 73
   iconst                 580
   iload                  9
   enum                  
   istore                 10
LABEL332:
   iload                  0
   istore                 12
   iconst                 0
   istore                 18
   invoke                 4487
   istore                 19
   get_varp               287
   iconst                 1
   if_icmpeq              LABEL342
   jump                   LABEL584
LABEL342:
   get_varc_int           41
   iconst                 1337
   if_icmpne              LABEL349
   get_varbit             4089
   iconst                 0
   if_icmpeq              LABEL349
   jump                   LABEL584
LABEL349:
   invoke                 7831
   iconst                 1
   if_icmpeq              LABEL353
   jump                   LABEL584
LABEL353:
   iload                  12
   iconst                 -1
   if_icmpne              LABEL357
   jump                   LABEL584
LABEL357:
   iload                  10
   iconst                 -1
   if_icmpne              LABEL361
   jump                   LABEL584
LABEL361:
   iload                  7
   iload                  4
   sub                   
   iconst                 57
   if_icmplt              LABEL367
   jump                   LABEL584
LABEL367:
   iload                  12
   chat_gethistoryex_byuid
   istore                 15
   ostore                 2                 ; timestamp
   istore                 14
   ostore                 0
   ostore                 3
   ostore                 1
   istore                 13
   istore                 18
   iload                  18
   oload                  1
   iload                  13
   iload                  14
   invoke                 91
   iconst                 1
   if_icmpeq              CHAT_FILTER       ; Jump to our new label instead
   jump                   LABEL580
CHAT_FILTER:
   oload                  0                 ; Load the message
   iconst                 1                 ; Gets changed to 0 if message is blocked
   iload                  18                ; Load the messageType
   iload                  12                ; Load the id of the messageNode
   sconst                 "chatFilterCheck"
   runelite_callback     
   pop_int               ; Pop the id of the messageNode
   pop_int               ; Pop the messageType
   iconst                 1                 ; 2nd half of conditional
   ostore                 0                 ; Override the message with our filtered message
   if_icmpeq              LABEL385          ; Check if we are building this message
   jump                   LABEL580
LABEL385:
   iconst                 1 ; splitpmbox
   iload                  12 ; message uid
   sconst                 "" ; message channel
   oload                  1 ; message name
   oload                  0 ; message
   oload                  2 ; message timestamp
   sconst                 "chatMessageBuilding"
   runelite_callback     
   pop_int                  ; uid
   pop_int                  ; splitpmbox
   ostore                 2 ; message timestamp
   ostore                 0 ; message
   ostore                 1 ; message name
   pop_object               ; message channel
   iload                  18
   switch                
      3: LABEL388
      7: LABEL388
      6: LABEL417
      5: LABEL446
   jump                   LABEL484
LABEL388:
   iload                  7
   oload                  2
   oload                  5
   sconst                 "splitPrivChatUsernameColor"
   runelite_callback     
   sconst                 "From "
   oload                  1
   sconst                 ":"
   sconst                 "</col>"
   join_string            5
   oload                  5
   invoke                 4742
   oload                  5
   oload                  0
   sconst                 "</col>"
   join_string            3
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 65535
   iconst                 1
   invoke                 203
   add                   
   istore                 7
   jump                   LABEL503
LABEL417:
   iload                  7
   oload                  2
   oload                  5
   sconst                 "splitPrivChatUsernameColor"
   runelite_callback     
   sconst                 "To "
   oload                  1
   sconst                 ":"
   sconst                 "</col>"
   join_string            5
   oload                  5
   invoke                 4742
   oload                  5
   oload                  0
   sconst                 "</col>"
   join_string            3
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 65535
   iconst                 1
   invoke                 203
   add                   
   istore                 7
   jump                   LABEL503
LABEL446:
   iload                  7
   oload                  2
   oload                  5
   oload                  0
   sconst                 "</col>"
   join_string            3
   oload                  5
   invoke                 4742
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 65535
   iconst                 1
   invoke                 199
   add                   
   istore                 7
   iload                  19
   iconst                 0
   if_icmpeq              LABEL472
   jump                   LABEL483
LABEL472:
   iload                  13
   iconst                 500
   add                   
   iconst                 1
   add                   
   set_varc_int           65
   iconst                 664
   iconst                 0
   sconst                 "i"
   iconst                 10616832
   if_setontimer         
LABEL483:
   jump                   LABEL503
LABEL484:
   iload                  7
   oload                  2
   oload                  0
   sconst                 "null"
   invoke                 4742
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 65535
   iconst                 1
   invoke                 199
   add                   
   istore                 7
LABEL503:
   iload                  10
   if_clearops           
   iload                  18
   iconst                 3
   if_icmpeq              LABEL515
   iload                  18
   iconst                 6
   if_icmpeq              LABEL515
   iload                  18
   iconst                 7
   if_icmpeq              LABEL515
   jump                   LABEL558
LABEL515:
   iload                  14
   iconst                 1
   if_icmpeq              LABEL519
   jump                   LABEL524
LABEL519:
   iconst                 7
   sconst                 "Message"
   iload                  10
   if_setop              
   jump                   LABEL532
LABEL524:
   iconst                 7
   sconst                 "Add friend"
   iload                  10
   if_setop              
   iconst                 8
   sconst                 "Add ignore"
   iload                  10
   if_setop              
LABEL532:
   iconst                 9
   sconst                 "Report"
   iload                  10
   if_setop              
   oload                  1
   invoke                 2759
   iconst                 1
   if_icmpeq              LABEL541
   jump                   LABEL545
LABEL541:
   iconst                 10
   sconst                 "Crown Info"
   iload                  10
   if_setop              
LABEL545:
   sconst                 "<col=ffffff>"
   oload                  1
   sconst                 "</col>"
   join_string            3
   iload                  10
   if_setopbase          
   iconst                 88
   iconst                 -2147483644
   sconst                 "event_opbase"
   sconst                 "is"
   iload                  10
   if_setonop            
   jump                   LABEL562
LABEL558:
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonop            
LABEL562:
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouserepeat   
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouseleave    
   iload                  9
   iconst                 1
   add                   
   istore                 9
   iconst                 105
   iconst                 73
   iconst                 580
   iload                  9
   enum                  
   istore                 10
LABEL580:
   iload                  12
   chat_getprevuid       
   istore                 12
   jump                   LABEL353
LABEL584:
   iload                  10
   iconst                 -1
   if_icmpne              LABEL588
   jump                   LABEL671
LABEL588:
   iload                  10
   if_clearops           
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouserepeat   
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouseleave    
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 0
   iload                  10
   if_setsize            
   iconst                 10682368
   iload                  9
   iconst                 4
   multiply              
   cc_find               
   iconst                 1
   if_icmpeq              LABEL616
   jump                   LABEL620
LABEL616:
   sconst                 ""
   cc_settext            
   iconst                 1
   cc_sethide            
LABEL620:
   iconst                 10682368
   iload                  9
   iconst                 4
   multiply              
   iconst                 1
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL630
   jump                   LABEL634
LABEL630:
   sconst                 ""
   cc_settext            
   iconst                 1
   cc_sethide            
LABEL634:
   iconst                 10682368
   iload                  9
   iconst                 4
   multiply              
   iconst                 2
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL644
   jump                   LABEL648
LABEL644:
   sconst                 ""
   cc_settext            
   iconst                 1
   cc_sethide            
LABEL648:
   iconst                 10682368
   iload                  9
   iconst                 4
   multiply              
   iconst                 3
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL658
   jump                   LABEL660
LABEL658:
   iconst                 1
   cc_sethide            
LABEL660:
   iload                  9
   iconst                 1
   add                   
   istore                 9
   iconst                 105
   iconst                 73
   iconst                 580
   iload                  9
   enum                  
   istore                 10
   jump                   LABEL584
LABEL671:
   return                
