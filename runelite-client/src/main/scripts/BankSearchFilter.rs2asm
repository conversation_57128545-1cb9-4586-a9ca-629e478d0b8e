.id                       279 ; [proc,bankmain_filteritem]
.int_arg_count            1
.obj_arg_count            0
   iconst                 -1                 ; return value
   iload                  0                  ; load item id
   get_varc_string        359
   lowercase
   sconst                 "bankSearchFilter" ; push event name
   runelite_callback                         ; invoke callback
   pop_int                                   ; pop item id
   pop_object                                ; pop search string
   istore                 1                  ; store return value
   iload                  1                  ; continue if it is still -1
   iconst                 -1
   if_icmpeq              CONTINUE
   iload                  1
   return                                    ; return rv

CONTINUE:
   sconst                 ""
   ostore                 0
   sconst                 ""
   ostore                 1
   invoke                 514
   iconst                 1
   if_icmpeq              LABEL8
   jump                   LABEL34
LABEL8:
   get_varc_string        359                ; Skip truncating of varcstr 22 by not calling 280
   lowercase             ; instead get the var directly and lowercase it
   ostore                 1
   oload                  1
   string_length         
   iconst                 0
   if_icmpgt              LABEL15
   jump                   LABEL34
LABEL15:
   iload                  0
   iconst                 -1
   if_icmpne              LABEL19
   jump                   LABEL23
LABEL19:
   iload                  0
   oc_name               
   lowercase             
   ostore                 0
LABEL23:
   oload                  0
   oload                  1
   iconst                 0
   string_indexof_string 
   iconst                 -1
   if_icmpne              LABEL30
   jump                   LABEL32
LABEL30:
   iconst                 1
   return                
LABEL32:
   iconst                 0
   return                
LABEL34:
   iconst                 1
   return                
   iconst                 -1
   return                
