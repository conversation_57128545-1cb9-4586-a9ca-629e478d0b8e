.id                       129
.int_arg_count            7
.obj_arg_count            0
; callback "friendsChatSetText"
;   Fired just before the client pops the name off the stack
;     Modified by the friendnotes plugin to show the icon
; callback "friendsChatSetPosition"
;   Fired just before the client sets the position of "ignored person changed their name" icon
;     Modified by the friendnotes plugin to offset the name changed icon
   iload                  1
   iconst                 2
   iconst                 3
   sconst                 "Sort by name"
   iload                  0
   iload                  1
   iload                  2
   iload                  3
   iload                  4
   iload                  5
   iload                  6
   invoke                 1653
   iload                  2
   iconst                 0
   iconst                 1
   sconst                 "Legacy sort"
   iload                  0
   iload                  1
   iload                  2
   iload                  3
   iload                  4
   iload                  5
   iload                  6
   invoke                 1653
   iload                  3
   cc_deleteall          
   iconst                 0
   istore                 7
   iconst                 0
   istore                 8
   sconst                 ""
   ostore                 0
   sconst                 ""
   ostore                 1
   iconst                 0
   istore                 9
   iconst                 15
   istore                 10
   iconst                 -1
   istore                 11
   ignore_count          
   istore                 12
   iload                  12
   iconst                 0
   if_icmplt              LABEL46
   jump                   LABEL67
LABEL46:
   get_varbit             8119
   iconst                 1
   if_icmpeq              LABEL50
   jump                   LABEL57
LABEL50:
   sconst                 "Loading ignore list"
   sconst                 "<br>"
   sconst                 "Please wait..."
   join_string            3
   iload                  5
   if_settext            
   jump                   LABEL63
LABEL57:
   sconst                 "You must set a name"
   sconst                 "<br>"
   sconst                 "before using this."
   join_string            3
   iload                  5
   if_settext            
LABEL63:
   iconst                 1
   iload                  0
   if_sethide            
   jump                   LABEL281
LABEL67:
   iload                  12
   iconst                 0
   if_icmpeq              LABEL71
   jump                   LABEL83
LABEL71:
   sconst                 "You may ignore users by using the button below, or by "
   sconst                 "right-clicking"
   sconst                 "long pressing"
   invoke                 1971
   sconst                 " on a message from them and selecting to add them to your ignore list."
   join_string            3
   iload                  5
   if_settext            
   iconst                 1
   iload                  0
   if_sethide            
   jump                   LABEL281
LABEL83:
   invoke                 1972
   istore                 11
   iload                  11
   iconst                 1
   if_icmpeq              LABEL89
   jump                   LABEL94
LABEL89:
   iconst                 8
   iconst                 5
   iload                  10
   scale                 
   istore                 10
LABEL94:
   sconst                 ""
   iload                  5
   if_settext            
   iconst                 0
   iload                  0
   if_sethide            
   3640                  
   get_varc_int           184
   switch                
      1: LABEL104
      2: LABEL107
      3: LABEL110
   jump                   LABEL112
LABEL104:
   iconst                 0
   3641                  
   jump                   LABEL112
LABEL107:
   iconst                 1
   3642                  
   jump                   LABEL112
LABEL110:
   iconst                 0
   3642                  
LABEL112:
   3643                  
LABEL113:
   iload                  7
   iload                  12
   if_icmplt              LABEL117
   jump                   LABEL273
LABEL117:
   iload                  7
   ignore_getname        
   ostore                 1
   ostore                 0
   iload                  3
   iconst                 4
   iload                  8
   iconst                 0
   cc_create             
   iload                  8
   iconst                 1
   add                   
   istore                 8
   oload                  0
   sconst                 "friendsChatSetText"
   runelite_callback     
   cc_settext            
   iconst                 0
   iload                  10
   iconst                 1
   iconst                 0
   cc_setsize            
   iconst                 0
   iload                  9
   iconst                 1
   iconst                 0
   cc_setposition        
   iconst                 16777215
   cc_setcolour          
   iconst                 495
   cc_settextfont        
   iconst                 0
   iconst                 1
   iconst                 0
   cc_settextalign       
   iconst                 1
   cc_settextshadow      
   sconst                 "<col=ff9040>"
   oload                  0
   sconst                 "</col>"
   join_string            3
   cc_setopbase          
   iconst                 1
   sconst                 "Delete"
   cc_setop              
   iload                  3
   iconst                 5
   iload                  8
   iconst                 0
   cc_create              1
   iload                  8
   iconst                 1
   add                   
   istore                 8
   iconst                 14
   iconst                 14
   iconst                 0
   iconst                 0
   cc_setsize             1
   oload                  0
   iconst                 190
   iconst                 495
   parawidth             
   iconst                 3
   add                   
   iload                  9
   iload                  10
   iconst                 14
   sub                   
   iconst                 2
   div                   
   add                   
   iconst                 0
   iconst                 0
   sconst                 "friendsChatSetPosition"
   runelite_callback     
   cc_setposition         1
   iconst                 1093
   cc_setgraphic          1
   iconst                 3355443
   cc_setgraphicshadow    1
   oload                  1
   string_length         
   iconst                 0
   if_icmpgt              LABEL199
   jump                   LABEL248
LABEL199:
   iload                  11
   iconst                 1
   if_icmpeq              LABEL203
   jump                   LABEL217
LABEL203:
   iconst                 10
   sconst                 "Reveal previous name"
   cc_setop              
   iconst                 130
   iconst                 -2147483644
   sconst                 "event_opbase"
   iconst                 -2147483645
   cc_getid              
   cc_getid               1
   oload                  1
   oload                  0
   sconst                 "isiiiss"
   cc_setonop            
   jump                   LABEL245
LABEL217:
   sconst                 "Previous name:"
   sconst                 "<br>"
   oload                  1
   join_string            3
   ostore                 1
   iconst                 526
   iconst                 -2147483645
   iconst                 -2147483643
   iload                  6
   oload                  1
   iconst                 25
   iconst                 190
   sconst                 "iiisii"
   cc_setonmouserepeat   
   iconst                 40
   iload                  6
   sconst                 "i"
   cc_setonmouseleave    
   iconst                 130
   iconst                 -2147483644
   sconst                 "event_opbase"
   iconst                 -1
   iconst                 -1
   iconst                 -1
   sconst                 "null"
   sconst                 "null"
   sconst                 "isiiiss"
   cc_setonop            
LABEL245:
   iconst                 0
   cc_sethide             1
   jump                   LABEL264
LABEL248:
   iconst                 40
   iload                  6
   sconst                 "i"
   cc_setonmouseover     
   iconst                 1
   cc_sethide             1
   iconst                 130
   iconst                 -2147483644
   sconst                 "event_opbase"
   iconst                 -1
   iconst                 -1
   iconst                 -1
   sconst                 "null"
   sconst                 "null"
   sconst                 "isiiiss"
   cc_setonop            
LABEL264:
   iload                  7
   iconst                 1
   add                   
   iload                  9
   iload                  10
   add                   
   istore                 9
   istore                 7
   jump                   LABEL113
LABEL273:
   iload                  12
   iconst                 1
   if_icmpge              LABEL277
   jump                   LABEL281
LABEL277:
   iload                  9
   iconst                 5
   add                   
   istore                 9
LABEL281:
   iload                  9
   iload                  3
   if_getheight          
   if_icmpgt              LABEL286
   jump                   LABEL296
LABEL286:
   iconst                 0
   iload                  9
   iload                  3
   if_setscrollsize      
   iload                  4
   iload                  3
   iload                  3
   if_getscrolly         
   invoke                 72
   jump                   LABEL304
LABEL296:
   iconst                 0
   iconst                 0
   iload                  3
   if_setscrollsize      
   iload                  4
   iload                  3
   iconst                 0
   invoke                 72
LABEL304:
   return                
