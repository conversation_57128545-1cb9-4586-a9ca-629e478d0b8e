.id                       909
.int_arg_count            2
.obj_arg_count            0
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551388
   enum                  
   istore                 2
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551390
   enum                  
   istore                 3
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551303
   enum                  
   istore                 4
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551311
   enum                  
   istore                 5
   iconst                 -1
   istore                 6
   iconst                 103
   iconst                 105
   iconst                 1960
   iload                  1
   enum                  
   istore                 7
   iconst                 103
   iconst                 105
   iconst                 1961
   iload                  1
   enum                  
   istore                 8
   iconst                 103
   iconst                 105
   iconst                 1135
   iload                  1
   enum                  
   istore                 9
   iconst                 103
   iconst                 105
   iconst                 1136
   iload                  1
   enum                  
   istore                 10
   iconst                 0
   istore                 11
   iconst                 0
   istore                 12
   iconst                 0
   istore                 13
   iconst                 0
   istore                 14
   iconst                 0
   istore                 15
   iconst                 0
   istore                 16
   iload                  0
   if_getwidth           
   istore                 17
   iload                  0
   if_getheight          
   istore                 18
   iload                  1
   iconst                 1745
   if_icmpeq              LABEL72
   jump                   LABEL92
LABEL72:
   iconst                 0
   iload                  17
   iconst                 39387155
   if_getwidth           
   sub                   
   invoke                 1045
   istore                 15
   iconst                 0
   iload                  18
   iconst                 39387155
   if_getheight          
   sub                   
   invoke                 1045
   istore                 16
   iload                  1
   invoke                 7412
   iload                  1
   invoke                 7568
   iload                  1
   invoke                 919
LABEL92:
   get_varbit             4606
   iconst                 0
   if_icmpne              LABEL96
   jump                   LABEL287
LABEL96:
   get_varbit             4606
   iconst                 2
   if_icmpeq              LABEL100
   jump                   LABEL109
LABEL100:
   iconst                 512
   iconst                 220
   viewport_setfov       
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 0
   viewport_clampfov     
   jump                   LABEL140
LABEL109:
   get_varbit             4606
   iconst                 3
   if_icmpeq              LABEL113
   jump                   LABEL132
LABEL113:
   iconst                 256
   iconst                 256
   viewport_setfov       
   iload                  1
   iconst                 1129
   if_icmpeq              LABEL120
   jump                   LABEL126
LABEL120:
   iconst                 512
   iconst                 512
   iconst                 512
   iconst                 512
   viewport_clampfov     
   jump                   LABEL131
LABEL126:
   iconst                 192
   iconst                 656
   iconst                 448
   iconst                 512
   viewport_clampfov     
LABEL131:
   jump                   LABEL140
LABEL132:
   iconst                 256
   iconst                 256
   viewport_setfov       
   iconst                 512
   iconst                 512
   iconst                 512
   iconst                 512
   viewport_clampfov     
LABEL140:
   iconst                 50
   cam_setfollowheight   
   iload                  2
   iconst                 -1
   if_icmpne              LABEL146
   jump                   LABEL286
LABEL146:
   iload                  3
   iconst                 -1
   if_icmpne              LABEL150
   jump                   LABEL286
LABEL150:
   viewport_geteffectivesize
   istore                 12
   istore                 11
   iconst                 0
   iload                  17
   iload                  11
   sub                   
   invoke                 1045
   iconst                 0
   iload                  18
   iload                  12
   sub                   
   invoke                 1045
   istore                 14
   istore                 13
   iload                  11
   iload                  12
   iconst                 0
   iconst                 0
   iload                  2
   if_setsize            
   iload                  11
   iconst                 0
   iload                  15
   iload                  13
   sub                   
   invoke                 1045
   sub                   
   iload                  12
   iconst                 0
   iload                  16
   iload                  14
   sub                   
   invoke                 1045
   sub                   
   iconst                 0
   iconst                 0
   iload                  3
   if_setsize            
   iload                  4
   iconst                 -1
   if_icmpne              LABEL193
   jump                   LABEL276
LABEL193:
   iload                  5
   iconst                 -1
   if_icmpne              LABEL197
   jump                   LABEL276
LABEL197:
   iload                  13
   iload                  15
   sub                   
   iconst                 2
   div                   
   iload                  14
   iload                  16
   sub                   
   iconst                 2
   div                   
   istore                 14
   istore                 13
   iconst                 0
   iload                  7
   iload                  13
   sub                   
   invoke                 1045
   iconst                 0
   iload                  9
   iload                  13
   sub                   
   invoke                 1045
   istore                 9
   istore                 7
   iconst                 0
   iload                  8
   iload                  14
   sub                   
   invoke                 1045
   iconst                 0
   iload                  10
   iload                  14
   sub                   
   invoke                 1045
   istore                 10
   istore                 8
   iload                  7
   iload                  8
   iconst                 0
   iconst                 0
   iload                  4
   if_setposition        
   iload                  7
   iload                  8
   iconst                 0
   iconst                 0
   iload                  5
   if_setposition        
   iload                  7
   iload                  9
   add                   
   iload                  8
   iload                  10
   add                   
   iconst                 1
   iconst                 1
   iload                  4
   if_setsize            
   iload                  7
   iload                  9
   add                   
   iload                  8
   iload                  10
   add                   
   iconst                 1
   iconst                 1
   iload                  5
   if_setsize            
   iload                  1
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551310
   enum                  
   iload                  5
   iload                  9
   iload                  10
   invoke                 910
   jump                   LABEL286
LABEL276:
   iload                  1
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551310
   enum                  
   iload                  3
   iconst                 0
   iconst                 0
   invoke                 910
LABEL286:
   jump                   LABEL403
LABEL287:
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 0
   viewport_clampfov     
   get_varc_int           73
   iconst                 128
   sconst                 "outerZoomLimit"
   runelite_callback     
   if_icmpge              LABEL296
   jump                   LABEL312
LABEL296:
   get_varc_int           73
   iconst                 896
   sconst                 "innerZoomLimit"
   runelite_callback     
   if_icmple              LABEL300
   jump                   LABEL312
LABEL300:
   get_varc_int           74
   iconst                 128
   sconst                 "outerZoomLimit"
   runelite_callback     
   if_icmpge              LABEL304
   jump                   LABEL312
LABEL304:
   get_varc_int           74
   iconst                 896
   sconst                 "innerZoomLimit"
   runelite_callback     
   if_icmple              LABEL308
   jump                   LABEL312
LABEL308:
   get_varc_int           73
   get_varc_int           74
   invoke                 42
   jump                   LABEL315
LABEL312:
   iconst                 512
   iconst                 512
   invoke                 42
LABEL315:
   viewport_geteffectivesize
   istore                 12
   istore                 11
   iload                  2
   iconst                 -1
   if_icmpne              LABEL322
   jump                   LABEL403
LABEL322:
   iload                  3
   iconst                 -1
   if_icmpne              LABEL326
   jump                   LABEL403
LABEL326:
   iload                  11
   iload                  12
   iconst                 0
   iconst                 0
   iload                  2
   if_setsize            
   iload                  11
   iload                  15
   sub                   
   iload                  12
   iload                  16
   sub                   
   iconst                 0
   iconst                 0
   iload                  3
   if_setsize            
   iload                  4
   iconst                 -1
   if_icmpne              LABEL346
   jump                   LABEL393
LABEL346:
   iload                  5
   iconst                 -1
   if_icmpne              LABEL350
   jump                   LABEL393
LABEL350:
   iload                  7
   iload                  8
   iconst                 0
   iconst                 0
   iload                  4
   if_setposition        
   iload                  7
   iload                  8
   iconst                 0
   iconst                 0
   iload                  5
   if_setposition        
   iload                  7
   iload                  9
   add                   
   iload                  8
   iload                  10
   add                   
   iconst                 1
   iconst                 1
   iload                  4
   if_setsize            
   iload                  7
   iload                  9
   add                   
   iload                  8
   iload                  10
   add                   
   iconst                 1
   iconst                 1
   iload                  5
   if_setsize            
   iload                  1
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551310
   enum                  
   iload                  5
   iload                  9
   iload                  10
   invoke                 910
   jump                   LABEL403
LABEL393:
   iload                  1
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551310
   enum                  
   iload                  3
   iconst                 0
   iconst                 0
   invoke                 910
LABEL403:
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551312
   enum                  
   istore                 19
   iload                  19
   iconst                 -1
   if_icmpne              LABEL413
   jump                   LABEL457
LABEL413:
   invoke                 1972
   iconst                 0
   if_icmpeq              LABEL417
   jump                   LABEL451
LABEL417:
   iload                  19
   if_hassub             
   iconst                 1
   if_icmpeq              LABEL422
   jump                   LABEL451
LABEL422:
   get_varc_int           173
   iconst                 -2
   if_icmpeq              LABEL426
   jump                   LABEL433
LABEL426:
   iconst                 512
   iconst                 0
   iconst                 0
   iconst                 1
   iload                  19
   if_setsize            
   jump                   LABEL450
LABEL433:
   get_varc_int           173
   iconst                 -3
   if_icmpeq              LABEL437
   jump                   LABEL444
LABEL437:
   iconst                 0
   iconst                 0
   iconst                 1
   iconst                 1
   iload                  19
   if_setsize            
   jump                   LABEL450
LABEL444:
   iconst                 512
   iconst                 334
   iconst                 0
   iconst                 0
   iload                  19
   if_setsize            
LABEL450:
   jump                   LABEL457
LABEL451:
   iconst                 512
   iconst                 334
   iconst                 0
   iconst                 0
   iload                  19
   if_setsize            
LABEL457:
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551315
   enum                  
   istore                 19
   iconst                 0
   istore                 20
   iconst                 0
   istore                 21
   iload                  19
   iconst                 -1
   if_icmpne              LABEL471
   jump                   LABEL519
LABEL471:
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551305
   enum                  
   if_hassub             
   iconst                 1
   if_icmpeq              LABEL480
   jump                   LABEL489
LABEL480:
   get_varbit             4692
   iconst                 0
   if_icmpne              LABEL484
   jump                   LABEL487
LABEL484:
   iconst                 0
   istore                 21
   jump                   LABEL489
LABEL487:
   iconst                 38
   istore                 21
LABEL489:
   invoke                 1972
   iconst                 1
   if_icmpeq              LABEL493
   jump                   LABEL511
LABEL493:
   get_varbit             542
   iconst                 0
   if_icmpeq              LABEL497
   jump                   LABEL511
LABEL497:
   get_varbit             6254
   iconst                 0
   if_icmpeq              LABEL501
   jump                   LABEL506
LABEL501:
   iconst                 181
   iconst                 4
   add                   
   istore                 20
   jump                   LABEL510
LABEL506:
   iconst                 84
   iconst                 4
   add                   
   istore                 20
LABEL510:
   jump                   LABEL513
LABEL511:
   iconst                 0
   istore                 20
LABEL513:
   iload                  20
   iload                  21
   iconst                 2
   iconst                 0
   iload                  19
   if_setposition        
LABEL519:
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551305
   enum                  
   istore                 19
   iconst                 0
   istore                 20
   iload                  19
   iconst                 -1
   if_icmpne              LABEL531
   jump                   LABEL553
LABEL531:
   invoke                 1972
   iconst                 1
   if_icmpeq              LABEL535
   jump                   LABEL545
LABEL535:
   get_varbit             6254
   iconst                 0
   if_icmpeq              LABEL539
   jump                   LABEL542
LABEL539:
   iconst                 181
   istore                 20
   jump                   LABEL544
LABEL542:
   iconst                 84
   istore                 20
LABEL544:
   jump                   LABEL547
LABEL545:
   iconst                 0
   istore                 20
LABEL547:
   iload                  20
   iconst                 0
   iconst                 1
   iconst                 1
   iload                  19
   if_setsize            
LABEL553:
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551330
   enum                  
   iload                  1
   invoke                 920
   return                
