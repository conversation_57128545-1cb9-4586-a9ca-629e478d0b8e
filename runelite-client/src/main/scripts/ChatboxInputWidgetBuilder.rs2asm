.id                       223
.int_arg_count            0
.obj_arg_count            0
   sconst                 "<col=0000ff>"
   ostore                 0
   iconst                 0
   istore                 0
   iconst                 6250335
   istore                 1
   invoke                 921
   iconst                 1
   if_icmpeq              LABEL10
   jump                   LABEL20
LABEL10:
   sconst                 "<col=9090ff>"
   iconst                 16777215
   iconst                 12566463
   istore                 1
   istore                 0
   ostore                 0
   iconst                 1
   iconst                 10616888
   if_settextshadow      
   jump                   LABEL23
LABEL20:
   iconst                 0
   iconst                 10616888
   if_settextshadow      
LABEL23:
   iload                  0
   iconst                 10616888
   if_setcolour          
   get_varc_string        335
   string_length         
   istore                 2
   get_varc_string        335
   escape                
   ostore                 1
   sconst                 ""
   ostore                 2
   iconst                 0
   istore                 3
   get_varbit             8119
   iconst                 1
   if_icmpeq              LABEL40
   jump                   LABEL167
LABEL40:
   invoke                 3160
   iconst                 1
   if_icmpeq              LABEL44
   jump                   LABEL49
LABEL44:
   invoke                 7776
   chat_playername       
   join_string            2
   ostore                 2
   jump                   LABEL66
LABEL49:
   invoke                 5849
   iconst                 1
   if_icmpeq              LABEL53
   jump                   LABEL58
LABEL53:
   sconst                 "<img=52>"
   chat_playername       
   join_string            2
   ostore                 2
   jump                   LABEL66
LABEL58:
   iconst                 105
   iconst                 115
   iconst                 1894
   get_varbit             1777
   enum                  
   chat_playername       
   join_string            2
   ostore                 2
LABEL66:
   get_varc_int           945
   switch                
      1: LABEL69
      2: LABEL74
      3: LABEL79
      4: LABEL84
   jump                   LABEL95
LABEL69:
   oload                  2
   sconst                 " (channel)"
   append                
   ostore                 2
   jump                   LABEL95
LABEL74:
   oload                  2
   sconst                 " (clan)"
   append                
   ostore                 2
   jump                   LABEL95
LABEL79:
   oload                  2
   sconst                 " (guest clan)"
   append                
   ostore                 2
   jump                   LABEL95
LABEL84:
   invoke                 5262
   iconst                 1
   if_icmpeq              LABEL88
   jump                   LABEL93
LABEL88:
   oload                  2
   sconst                 " (group)"
   append                
   ostore                 2
   jump                   LABEL95
LABEL93:
   iconst                 0
   set_varc_int           945
LABEL95:
   oload                  2
   sconst                 ": "
   oload                  0
   oload                  1
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 2
   get_varc_string        335
   invoke                 1353
   iconst                 1
   if_icmpeq              LABEL108
   jump                   LABEL120
LABEL108:
   iload                  2
   iconst                 79
   if_icmplt              LABEL112
   jump                   LABEL119
LABEL112:
   oload                  2
   oload                  0
   sconst                 "*"
   sconst                 "</col>"
   join_string            3
   append                
   ostore                 2
LABEL119:
   jump                   LABEL131
LABEL120:
   iload                  2
   iconst                 80
   if_icmplt              LABEL124
   jump                   LABEL131
LABEL124:
   oload                  2
   oload                  0
   sconst                 "*"
   sconst                 "</col>"
   join_string            3
   append                
   ostore                 2
LABEL131:
   oload                  2
   iconst                 2147483647
   iconst                 495
   parawidth             
   istore                 3
   iload                  3
   iconst                 10616888
   if_getwidth           
   if_icmpgt              LABEL141
   jump                   LABEL147
LABEL141:
   iconst                 2
   iconst                 2
   iconst                 0
   iconst                 10616888
   if_settextalign       
   jump                   LABEL152
LABEL147:
   iconst                 0
   iconst                 2
   iconst                 0
   iconst                 10616888
   if_settextalign       
LABEL152:
   iconst                 10616888
   if_clearops           
   iconst                 -1
   sconst                 ""
   iconst                 10616888
   if_setonmouserepeat   
   iconst                 -1
   sconst                 ""
   iconst                 10616888
   if_setonmouseleave    
   iconst                 -1
   sconst                 ""
   iconst                 10616888
   if_setonop            
   jump                   LABEL226
LABEL167:
   invoke                 3160
   iconst                 1
   if_icmpeq              LABEL171
   jump                   LABEL176
LABEL171:
   invoke                 7776
   sconst                 " You must set a name before you can chat."
   join_string            2
   ostore                 2
   jump                   LABEL193
LABEL176:
   invoke                 5849
   iconst                 1
   if_icmpeq              LABEL180
   jump                   LABEL185
LABEL180:
   sconst                 "<img=52>"
   sconst                 " You must set a name before you can chat."
   join_string            2
   ostore                 2
   jump                   LABEL193
LABEL185:
   iconst                 105
   iconst                 115
   iconst                 1894
   get_varbit             1777
   enum                  
   sconst                 " You must set a name before you can chat."
   join_string            2
   ostore                 2
LABEL193:
   iconst                 1
   iconst                 2
   iconst                 0
   iconst                 10616888
   if_settextalign       
   iconst                 10
   sconst                 "Configure"
   iconst                 10616888
   if_setop              
   sconst                 "<col=ff9040>"
   sconst                 "Display name"
   sconst                 "</col>"
   join_string            3
   iconst                 10616888
   if_setopbase          
   iconst                 45
   iconst                 -2147483645
   iload                  1
   sconst                 "ii"
   iconst                 10616888
   if_setonmouserepeat   
   iconst                 45
   iconst                 -2147483645
   iload                  0
   sconst                 "ii"
   iconst                 10616888
   if_setonmouseleave    
   iconst                 489
   iconst                 -2147483644
   iconst                 1024
   sconst                 "ii"
   iconst                 10616888
   if_setonop            
LABEL226:
   oload                  2
   iconst                 10616888
   if_settext            
   sconst                 "setChatboxInput"
   runelite_callback     
   iconst                 3
   iconst                 16
   iconst                 1
   iconst                 0
   iconst                 10616888
   if_setsize            
   return                
