.id                       300
.int_arg_count            0
.obj_arg_count            0
   get_varc_int           11
   iconst                 1
   if_icmpeq              LABEL4
   jump                   LABEL5
LABEL4:
   if_close              
LABEL5:
   iconst                 11
   invoke                 677
   sconst                 "Show items whose names contain the following text:"
   sconst                 "setSearchBankInputText"  ; load event name
   runelite_callback     ; invoke callback
   iconst                 ********
   if_settext            
   sconst                 ""
   invoke                 222
   sconst                 ""
   ostore                 0
   iconst                 112
   iconst                 -**********
   iconst                 -**********
   oload                  0
   sconst                 "iis"
   iconst                 ********
   if_setonkey           
   iconst                 138
   sconst                 ""
   iconst                 ********
   if_setondialogabort   
   invoke                 1972
   iconst                 1
   if_icmpeq              LABEL29
   jump                   LABEL32
LABEL29:
   iconst                 0
   iconst                 80
   invoke                 1983
LABEL32:
   return                
