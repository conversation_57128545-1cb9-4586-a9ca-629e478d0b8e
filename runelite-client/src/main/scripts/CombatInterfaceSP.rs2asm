.id                       327
.int_arg_count            1
.obj_arg_count            0
   ; Attach specbar redraw listeners to the weapon name text instead of to
   ; auto retaliate text (which is var0). Test by enabling "Hide auto retaliate"
   ; and using a spec.
   iconst                 :combat:weapon_name
   istore                 0        ; overwrite script parameter which is the auto retaliate text
   iload                  0
   invoke                 187
   iconst                 186
   iload                  0
   iconst                 301
   iconst                 300
   iconst                 3784
   iconst                 4600
   iconst                 284
   iconst                 5
   sconst                 "iY"
   iload                  0
   if_setonvartransmit   
   iconst                 186
   iload                  0
   iconst                 3
   iconst                 1
   sconst                 "iY"
   iload                  0
   if_setonstattransmit  
   iconst                 186
   iload                  0
   iconst                 94
   iconst                 1
   sconst                 "iY"
   iload                  0
   if_setoninvtransmit   
   return                
