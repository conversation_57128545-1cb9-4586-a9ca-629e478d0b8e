.id                       396
.int_arg_count            2
.obj_arg_count            0
   invoke                 1007
   istore                 2
   sconst                 "Total level:"
   sconst                 "<br>"
   iload                  2
   tostring              
   join_string            3
   iload                  0
   sconst                 "skillTabTotalLevel" ; push event name
   runelite_callback     ; invoke callback
   if_settext            
   iload                  0
   if_clearops           
   sconst                 ""
   ostore                 0
   sconst                 ""
   ostore                 1
   map_members           
   iconst                 1
   if_icmpeq              LABEL22
   get_varc_int           103
   iconst                 1
   if_icmpeq              LABEL22
   jump                   LABEL28
LABEL22:
   sconst                 "Total XP:"
   ostore                 0
   invoke                 1008
   invoke                 1009
   ostore                 1
   jump                   LABEL37
LABEL28:
   sconst                 "Total XP:|Free Total Level:"
   ostore                 0
   invoke                 1008
   invoke                 1009
   sconst                 "|"
   invoke                 1320
   tostring              
   join_string            3
   ostore                 1
LABEL37:
   iconst                 1736
   invoke                 3697
   iconst                 1
   if_icmpeq              LABEL42
   jump                   LABEL57
LABEL42:
   oload                  0
   sconst                 "|"
   invoke                 7699
   join_string            2
   append                
   ostore                 0
   oload                  1
   sconst                 "|"
   iconst                 -1
   invoke                 7698
   sconst                 ","
   invoke                 46
   join_string            2
   append                
   ostore                 1
LABEL57:
   invoke                 1972
   iconst                 1
   if_icmpeq              LABEL61
   jump                   LABEL92
LABEL61:
   iconst                 1
   sconst                 "Toggle Total XP"
   iload                  0
   if_setop              
   iconst                 2367
   iconst                 -2147483644
   iconst                 -2147483645
   iconst                 -1
   iload                  1
   oload                  0
   oload                  1
   iconst                 495
   sconst                 "iiiissi"
   iload                  0
   if_setonop            
   get_varc_int           218
   iload                  0
   if_icmpeq              LABEL80
   jump                   LABEL91
LABEL80:
   get_varc_int           217
   iconst                 -1
   if_icmpeq              LABEL84
   jump                   LABEL91
LABEL84:
   iload                  0
   iconst                 -1
   iload                  1
   oload                  0
   oload                  1
   iconst                 495
   invoke                 2344
LABEL91:
   jump                   LABEL112
LABEL92:
   iconst                 992
   iconst                 -2147483645
   iconst                 -1
   iload                  1
   oload                  0
   oload                  1
   iconst                 495
   iconst                 25
   iconst                 5
   div                   
   sconst                 "iiissii"
   iload                  0
   if_setonmouserepeat   
   iconst                 40
   iload                  1
   sconst                 "i"
   iload                  0
   if_setonmouseleave    
   iconst                 0
   set_varc_int           2
LABEL112:
   return                
