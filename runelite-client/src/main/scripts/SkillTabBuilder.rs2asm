.id                       395
.int_arg_count            3
.obj_arg_count            1
   iconst                 83
   iconst                 49
   iconst                 1497
   iload                  0
   enum                  
   istore                 3
   iload                  0
   stat                  
   tostring              
   cc_settext            
   iload                  0
   stat_base             
   istore                 4
   iload                  0                   ; load the skill id from arguments
   iload                  4                   ; load the current real skill level
   sconst                 "skillTabBaseLevel" ; push event name
   runelite_callback     ; invoke callback
   istore                 4                   ; store the (possibly) edited real skill level
   pop_int                                    ; pop the skill id we pushed
   iload                  4
   tostring              
   cc_settext             1
   iload                  0
   stat_xp               
   istore                 5
   sconst                 ","
   ostore                 1
   oload                  0
   sconst                 " XP:"
   join_string            2
   ostore                 2
   iload                  5
   oload                  1
   invoke                 46
   ostore                 3
   iconst                 0
   istore                 6
   get_varbit             4181
   iconst                 0
   if_icmpeq              LABEL35
   jump                   LABEL66
LABEL35:
   iload                  4
   iconst                 99
   sconst                 "skillTabMaxLevel"   ; push event name
   runelite_callback     ; invoke callback
   if_icmplt              LABEL39
   jump                   LABEL65
LABEL39:
   iconst                 105
   iconst                 105
   iconst                 256
   iload                  4
   iconst                 1
   add                   
   enum                  
   istore                 6
   oload                  2
   sconst                 "|Next level at:|Remaining XP:"
   append                
   ostore                 2
   oload                  3
   sconst                 "|"
   iload                  6
   oload                  1
   invoke                 46
   sconst                 "|"
   iload                  6
   iload                  5
   sub                   
   oload                  1
   invoke                 46
   join_string            4
   append                
   ostore                 3
LABEL65:
   jump                   LABEL84
LABEL66:
   oload                  2
   sconst                 "|Next level at:"
   append                
   ostore                 2
   oload                  3
   sconst                 "|"
   iconst                 105
   iconst                 105
   iconst                 256
   iload                  4
   iconst                 1
   add                   
   enum                  
   oload                  1
   invoke                 46
   join_string            2
   append                
   ostore                 3
LABEL84:
   iconst                 1736
   invoke                 3697
   iconst                 1
   if_icmpeq              LABEL89
   jump                   LABEL104
LABEL89:
   oload                  2
   sconst                 "|"
   invoke                 7699
   join_string            2
   append                
   ostore                 2
   oload                  3
   sconst                 "|"
   iload                  0
   invoke                 7698
   oload                  1
   invoke                 46
   join_string            2
   append                
   ostore                 3
LABEL104:
   iconst                 0
   istore                 7
   iconst                 0
   istore                 8
   iconst                 0
   istore                 9
   iconst                 0
   istore                 10
   invoke                 1138
   iconst                 0
   if_icmpne              LABEL116
   jump                   LABEL326
LABEL116:
   iload                  0
   invoke                 1936
   istore                 7
   iload                  7
   iconst                 -1
   if_icmpne              LABEL123
   jump                   LABEL157
LABEL123:
   iload                  7
   iconst                 10
   div                   
   istore                 7
   iload                  7
   iload                  5
   if_icmpgt              LABEL131
   jump                   LABEL157
LABEL131:
   oload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP to regain:"
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 2
   oload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   iload                  7
   iload                  5
   sub                   
   oload                  1
   invoke                 46
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 3
   iconst                 1
   istore                 8
LABEL157:
   iload                  8
   iconst                 0
   if_icmpeq              LABEL161
   jump                   LABEL326
LABEL161:
   get_varp               1588
   iconst                 0
   if_icmpgt              LABEL165
   jump                   LABEL326
LABEL165:
   iload                  0
   switch                
      0: LABEL168
      2: LABEL168
      6: LABEL168
      4: LABEL221
      1: LABEL274
   jump                   LABEL326
LABEL168:
   iconst                 20
   invoke                 2031
   istore                 10
   iload                  10
   iconst                 0
   if_icmpgt              LABEL175
   jump                   LABEL198
LABEL175:
   oload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 2
   oload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   iload                  10
   oload                  1
   invoke                 46
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 3
   jump                   LABEL220
LABEL198:
   iconst                 1
   istore                 9
   oload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 2
   oload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "NONE"
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 3
LABEL220:
   jump                   LABEL326
LABEL221:
   iconst                 30
   invoke                 2031
   istore                 10
   iload                  10
   iconst                 0
   if_icmpgt              LABEL228
   jump                   LABEL251
LABEL228:
   oload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 2
   oload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   iload                  10
   oload                  1
   invoke                 46
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 3
   jump                   LABEL273
LABEL251:
   iconst                 1
   istore                 9
   oload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 2
   oload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "NONE"
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 3
LABEL273:
   jump                   LABEL326
LABEL274:
   iconst                 40
   invoke                 2031
   istore                 10
   iload                  10
   iconst                 0
   if_icmpgt              LABEL281
   jump                   LABEL304
LABEL281:
   oload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 2
   oload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   iload                  10
   oload                  1
   invoke                 46
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 3
   jump                   LABEL326
LABEL304:
   iconst                 1
   istore                 9
   oload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 2
   oload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "NONE"
   sconst                 "</col>"
   join_string            4
   append                
   ostore                 3
LABEL326:
   iload                  1
   iconst                 5
   cc_find                1
   iconst                 1
   if_icmpeq              LABEL332
   jump                   LABEL342
LABEL332:
   iload                  9
   iconst                 1
   if_icmpeq              LABEL336
   jump                   LABEL339
LABEL336:
   iconst                 0
   cc_sethide             1
   jump                   LABEL341
LABEL339:
   iconst                 1
   cc_sethide             1
LABEL341:
   jump                   LABEL370
LABEL342:
   iload                  1
   iconst                 5
   iconst                 5
   iconst                 0
   cc_create              1
   iconst                 6
   iconst                 0
   iconst                 0
   iconst                 1
   cc_setposition         1
   iconst                 19
   iconst                 19
   iconst                 0
   iconst                 0
   cc_setsize             1
   iconst                 940
   cc_setgraphic          1
   iconst                 65793
   cc_setgraphicshadow    1
   iload                  9
   iconst                 1
   if_icmpeq              LABEL365
   jump                   LABEL368
LABEL365:
   iconst                 0
   cc_sethide             1
   jump                   LABEL370
LABEL368:
   iconst                 1
   cc_sethide             1
LABEL370:
   iload                  3
   iconst                 1
   if_icmpeq              LABEL374
   jump                   LABEL393
LABEL374:
   map_members           
   iconst                 0
   if_icmpeq              LABEL378
   jump                   LABEL393
LABEL378:
   get_varc_int           103
   iconst                 0
   if_icmpeq              LABEL382
   jump                   LABEL393
LABEL382:
   sconst                 "<col=ff0000>"
   oload                  0
   sconst                 ":"
   sconst                 "</col>"
   join_string            4
   ostore                 2
   sconst                 "<col=ff0000>"
   sconst                 "Members Only"
   sconst                 "</col>"
   join_string            3
   ostore                 3
LABEL393:
   invoke                 1972
   iconst                 1
   if_icmpeq              LABEL397
   jump                   LABEL424
LABEL397:
   iconst                 2367
   iconst                 -2147483644
   iconst                 -2147483645
   iconst                 -1
   iload                  2
   oload                  2
   oload                  3
   iconst                 495
   sconst                 "iiiissi"
   iload                  1
   if_setonop            
   get_varc_int           218
   iload                  1
   if_icmpeq              LABEL412
   jump                   LABEL423
LABEL412:
   get_varc_int           217
   iconst                 -1
   if_icmpeq              LABEL416
   jump                   LABEL423
LABEL416:
   iload                  1
   iconst                 -1
   iload                  2
   oload                  2
   oload                  3
   iconst                 495
   invoke                 2344
LABEL423:
   jump                   LABEL439
LABEL424:
   iconst                 992
   iconst                 -2147483645
   iconst                 -1
   iload                  2
   oload                  2
   oload                  3
   iconst                 495
   iconst                 25
   iconst                 5
   div                   
   sconst                 "iiissii"
   iload                  1
   if_setonmouserepeat   
   iconst                 0
   set_varc_int           2
LABEL439:
   return                
