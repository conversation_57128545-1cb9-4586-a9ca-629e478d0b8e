#
# Copyright (c) 2022 Abex
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
#    list of conditions and the following disclaimer.
# 2. Redistributions in binary form must reproduce the above copyright notice,
#    this list of conditions and the following disclaimer in the documentation
#    and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
# ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

# https://www.formdev.com/flatlaf/properties-files/
# https://www.formdev.com/flatlaf/client-properties/#JComponent

# suppress inspection "UnusedProperty" for whole file

# Values from ColorScheme are injected as @VARs

@background=@DARK_GRAY
@foreground=@TEXT
@accentColor=@BRAND_ORANGE
@buttonBackground=@CONTROL
@componentBackground=@CONTROL
@menuBackground=lighten(@background, 4%)
@accentFocusColor=@MEDIUM_GRAY
@selectionBackground=@MEDIUM_GRAY

ButtonUI=net.runelite.client.ui.laf.RuneLiteButtonUI
Button.arc=0
Button.borderColor=@BORDER
Button.foreground=#FFF

Button.toolbar.hoverBackground=$Button.hoverBackground
Button.toolbar.pressedBackground=$Button.pressedBackground
Button.toolbar.selectedBackground=$Button.selectedBackground

[style]Button.iconButton=\
  rolloverIconAlpha: 0.7; \
  toolbar.hoverBackground: #0000; \
  toolbar.selectedBackground: #0000; \
  border: null; \
  buttonType: "borderless"
[style]CheckBox.iconButton=\
  rolloverIconAlpha: 0.7; \
  border: null
[style]ToggleButton.iconButton=$[style]Button.iconButton
[style]RadioButton.iconButton=$[style]CheckBox.iconButton

# substance would always apply the border insets, even when it wasn't painted
[style].legacyIconButton=border: 3,10,3,10,#0000

CheckBoxUI=net.runelite.client.ui.laf.RuneLiteCheckBoxUI
CheckBox.icon.background=@CONTROL
CheckBox.icon.borderColor=@BORDER
CheckBox.icon.selectedBorderColor=@BORDER
CheckBox.icon.size=22
CheckBox.icon.focusWidth=0

Component.innerFocusWidth=0
Component.innerOutlineWidth=0
Component.arc=0
Component.borderColor=@BORDER

CheckBox.arc=0

MenuBar.background=@DARKER_GRAY
MenuBar.border=0,0,0,0

MenuItem.iconTextGap=0
MenuItem.minimumIconSize=0,0

PasswordField.showRevealButton=true
PasswordField.font=dialog 12

RadioButtonUI=net.runelite.client.ui.laf.RuneLiteRadioButtonUI

RootPaneUI=net.runelite.client.ui.laf.RuneLiteRootPaneUI

ScrollBarUI=net.runelite.client.ui.laf.RuneLiteScrollBarUI
ScrollBar.track=@SCROLL_TRACK
ScrollBar.thumb=@MEDIUM_GRAY
ScrollBar.width=7
ScrollBar.minimumButtonSize=7, 7
ScrollBar.minimumThumbSize=7, 7
ScrollPane.border=0,0,0,0

Spinner.buttonBackground=$Spinner.background

ProgressBar.arc=0

TabbedPaneUI=net.runelite.client.ui.laf.RuneLiteTabbedPaneUI
TabbedPane.buttonArc=0
TabbedPane.evenlyWrapTabs=true
TabbedPane.expandWrappedTabs=false
TabbedPane.rotateTabRuns=false
TabbedPane.inactiveUnderlineColor=@accentUnderlineColor

TitlePane.centerTitleIfMenuBarEmbedded=false
TitlePane.embeddedForeground=@foreground
TitlePane.menuBarTitleGap=5
TitlePane.background=@DARKER_GRAY
TitlePane.unifiedBackground=false
TitlePane.useWindowDecorations=false
TitlePane.font=bold
TitlePane.buttonSize=33,23

ToolBar.background=@DARKER_GRAY

ToolTip.background=@menuBackground

ToggleButtonUI=net.runelite.client.ui.laf.RuneLiteToggleButtonUI
