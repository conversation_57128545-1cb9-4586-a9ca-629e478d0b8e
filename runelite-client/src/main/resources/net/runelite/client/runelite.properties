runelite.title=Microbot
runelite.version=${project.version}
microbot.version=${microbot.version}
microbot.commit=${microbot.commit.sha}
runelite.commit=${git.commit.id.abbrev}
runelite.dirty=${git.dirty}
runelite.discord.enable=true
runelite.discord.appid=409416265891971072
runelite.discord.invite=https://discord.gg/ArdAhnN
runelite.github.link=https://github.com/runelite/runelite/discussions
runelite.wiki.link=https://github.com/runelite/runelite/wiki
runelite.patreon.link=https://www.patreon.com/runelite
runelite.wiki.troubleshooting.link=https://github.com/runelite/runelite/wiki/Troubleshooting-problems-with-the-client
runelite.wiki.building.link=https://github.com/runelite/runelite/wiki/Building-with-IntelliJ-IDEA#client-failing-to-start
runelite.dnschange.link=https://1.1.1.1/dns/#setup-instructions
runelite.jav_config=https://oldschool.config.runescape.com/jav_config.ws
runelite.jav_config_backup=https://static.runelite.net/jav_config.ws
runelite.pluginhub.url=https://repo.runelite.net/plugins
runelite.pluginhub.version=${project.version}
runelite.imgur.client.id=30d71e5f6860809
runelite.api.base=https://api.runelite.net/runelite-${project.version}
runelite.session=https://api.runelite.net/session
runelite.static.base=https://static.runelite.net
runelite.ws=https://api.runelite.net/ws2
runelite.config=https://static.runelite.net/config.json
runelite.osrstwitter.link=https://twitter.com/OldSchoolRS
runelite.oauth.redirect=https://runelite.net/logged-in
