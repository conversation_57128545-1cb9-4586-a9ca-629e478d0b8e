/*
 * Copyright (c) 2018, <PERSON>to <https://github.com/devLotto>
 * Copyright (c) 2018, <PERSON><PERSON><PERSON> <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *   list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *   this list of conditions and the following disclaimer in the documentation
 *   and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.interfacestyles;

import lombok.Getter;
import net.runelite.api.gameval.SpriteID;
import static net.runelite.client.plugins.interfacestyles.Skin.AROUND_2005;
import static net.runelite.client.plugins.interfacestyles.Skin.AROUND_2006;
import static net.runelite.client.plugins.interfacestyles.Skin.AROUND_2010;

@Getter
enum SpriteOverride
{
	TAB_COMBAT(SpriteID.SideIcons.COMBAT, AROUND_2005, AROUND_2010),
	TAB_STATS(SpriteID.SideIcons.STATS, AROUND_2005, AROUND_2010),
	TAB_QUESTS(SpriteID.SideiconsInterface.QUESTS, AROUND_2005),
	TAB_QUESTS_PURPLE_KOUREND_1299(SpriteID.SideiconsInterface.KOUREND, AROUND_2005),
	TAB_QUESTS_RED_MINIGAMES(SpriteID.SideiconsInterface.MINIGAMES, AROUND_2005),
	TAB_QUESTS_GREEN_ACHIEVEMENT_DIARIES(SpriteID.SideiconsInterface.ACHIEVEMENT_DIARIES, AROUND_2005),
	TAB_QUESTS_BROWN_CHARACTER_SUMMARY(SpriteID.SideiconsInterface.CHARACTER_SUMMARY, AROUND_2005),
	TAB_QUESTS_ORANGE_ADVENTURE_PATHS(SpriteID.SideiconsInterface.ADVENTURE_PATHS, AROUND_2005),
	TAB_INVENTORY(SpriteID.SideIcons.INVENTORY, AROUND_2005, AROUND_2010),
	TAB_EQUIPMENT(SpriteID.SideIcons.EQUIPMENT, AROUND_2005, AROUND_2010),
	TAB_PRAYER(SpriteID.SideiconsInterface.PRAYER, AROUND_2005, AROUND_2010),
	TAB_MAGIC(SpriteID.SideiconsInterface.MAGIC, AROUND_2005, AROUND_2010),
	TAB_MAGIC_SPELLBOOK_ANCIENT_MAGICKS(SpriteID.SideiconsInterface.SPELLBOOK_ANCIENT_MAGICKS, AROUND_2005),
	TAB_MAGIC_SPELLBOOK_LUNAR(SpriteID.SideiconsInterface.SPELLBOOK_LUNAR, AROUND_2005),
	TAB_MAGIC_SPELLBOOK_ARCEUUS(SpriteID.SideiconsInterface.SPELLBOOK_ARCEUUS, AROUND_2005),
	TAB_CLAN_CHAT(SpriteID.SideIcons.FRIENDS_CHAT, AROUND_2005, AROUND_2010),
	TAB_FRIENDS(SpriteID.SideiconsInterface.FRIENDS, AROUND_2005, AROUND_2010),
	TAB_IGNORES(SpriteID.SideiconsInterface.IGNORES, AROUND_2005, AROUND_2010),
	TAB_LOGOUT(SpriteID.SideIcons.LOGOUT, AROUND_2005, AROUND_2010),
	TAB_OPTIONS(SpriteID.SideIcons.OPTIONS, AROUND_2005, AROUND_2010),
	TAB_EMOTES(SpriteID.SideIcons.EMOTES, AROUND_2005, AROUND_2010),
	TAB_MUSIC(SpriteID.SideIcons.MUSIC, AROUND_2005, AROUND_2010),
	TAB_CHATBOX(SpriteID.CHAT_BACKGROUND, AROUND_2005),

	QUESTS_PAGE_ICON_CHARACTER_SUMMARY(SpriteID.AchievementDiaryIcons.BROWN_CHARACTER_SUMMARY, AROUND_2005),
	QUESTS_PAGE_ICON_QUESTS(SpriteID.AchievementDiaryIcons.BLUE_QUESTS, AROUND_2005),
	QUESTS_PAGE_ICON_ACHIEVEMENT_DIARIES(SpriteID.AchievementDiaryIcons.GREEN_ACHIEVEMENT_DIARIES, AROUND_2005),
	QUESTS_PAGE_ICON_KOUREND_FAVOUR(SpriteID.AchievementDiaryIcons.PURPLE_KOUREND, AROUND_2005),
	QUESTS_PAGE_ICON_ADVENTURE_PATHS(SpriteID.AchievementDiaryIcons.ORANGE_ADVENTURE_PATHS, AROUND_2005),

	BUTTON_FRIENDS(SpriteID.OptionsIconsSmall.FRIENDS, AROUND_2005),
	BUTTON_IGNORES(SpriteID.OptionsIconsSmall.IGNORES, AROUND_2005),

	SKILL_ATTACK(SpriteID.Staticons.ATTACK, AROUND_2010),
	SKILL_STRENGTH(SpriteID.Staticons.STRENGTH, AROUND_2010),
	SKILL_DEFENCE(SpriteID.Staticons.DEFENCE, AROUND_2010),
	SKILL_RANGED(SpriteID.Staticons.RANGED, AROUND_2010),
	SKILL_PRAYER(SpriteID.Staticons.PRAYER, AROUND_2005, AROUND_2010),
	SKILL_MAGIC(SpriteID.Staticons.MAGIC, AROUND_2010),
	SKILL_HITPOINTS(SpriteID.Staticons.HITPOINTS, AROUND_2010),
	SKILL_AGILITY(SpriteID.Staticons.AGILITY, AROUND_2010),
	SKILL_HERBLORE(SpriteID.Staticons.HERBLORE, AROUND_2010),
	SKILL_THIEVING(SpriteID.Staticons.THIEVING, AROUND_2010),
	SKILL_CRAFTING(SpriteID.Staticons.CRAFTING, AROUND_2010),
	SKILL_FLETCHING(SpriteID.Staticons.FLETCHING, AROUND_2010),
	SKILL_MINING(SpriteID.Staticons.MINING, AROUND_2010),
	SKILL_SMITHING(SpriteID.Staticons.SMITHING, AROUND_2010),
	SKILL_FISHING(SpriteID.Staticons.FISHING, AROUND_2010),
	SKILL_COOKING(SpriteID.Staticons.COOKING, AROUND_2010),
	SKILL_FIREMAKING(SpriteID.Staticons.FIREMAKING, AROUND_2010),
	SKILL_WOODCUTTING(SpriteID.Staticons.WOODCUTTING, AROUND_2010),
	SKILL_RUNECRAFT(SpriteID.Staticons2.RUNECRAFT, AROUND_2010),
	SKILL_SLAYER(SpriteID.Staticons2.SLAYER, AROUND_2010),
	SKILL_HUNTER(SpriteID.Staticons2.HUNTER, AROUND_2010),
	SKILL_CONSTRUCTION(SpriteID.Staticons2.CONSTRUCTION, AROUND_2010),

	COMPASS(SpriteID.COMPASS, AROUND_2005),
	WINDOW_CLOSE_BUTTON_RED_X(SpriteID.CloseButtons.RED_X, AROUND_2010),
	WINDOW_CLOSE_BUTTON_RED_X_HOVERED(SpriteID.CloseButtons.RED_X_HOVERED, AROUND_2010),
	WINDOW_CLOSE_BUTTON_BROWN_X(SpriteID.CloseButtons.BROWN_X, AROUND_2010),
	WINDOW_CLOSE_BUTTON_BROWN_X_HOVERED(SpriteID.CloseButtons.BROWN_X_HOVERED, AROUND_2010),
	MINIMAP_ORB_FRAME(SpriteID.OrbFrame.FRAME, AROUND_2010),
	MINIMAP_ORB_FRAME_HOVERED(SpriteID.OrbFrame.FRAME_HOVERED, AROUND_2010),
	MINIMAP_ORB_XP(SpriteID.OrbXp.ORB, AROUND_2010),
	MINIMAP_ORB_XP_ACTIVATED(SpriteID.OrbXp.ACTIVATED, AROUND_2010),
	MINIMAP_ORB_XP_HOVERED(SpriteID.OrbXp.HOVERED, AROUND_2010),
	MINIMAP_ORB_XP_ACTIVATED_HOVERED(SpriteID.OrbXp.ACTIVATED_HOVERED, AROUND_2010),
	MINIMAP_ORB_WORLD_MAP_FRAME(SpriteID.RING_30, AROUND_2010),
	MINIMAP_ORB_WORLD_MAP_PLANET(SpriteID.WorldmapIcon.PLANET, AROUND_2010),

	//CHATBOX(SpriteID.CHATBOX, AROUND_2005, AROUND_2006),
	CHATBOX_BUTTONS_BACKGROUND_STONES(SpriteID.MAIN_STONES_BOTTOM, AROUND_2005, AROUND_2006),
	CHATBOX_BUTTON(SpriteID.ChatTabButton.BUTTON, AROUND_2005, AROUND_2006),
	CHATBOX_BUTTON_HOVERED(SpriteID.ChatTabButton.HOVERED, AROUND_2005, AROUND_2006),
	CHATBOX_BUTTON_NEW_MESSAGES(SpriteID.ChatTabButton.NEW_MESSAGES, AROUND_2005, AROUND_2006),
	CHATBOX_BUTTON_SELECTED(SpriteID.ChatTabButton.SELECTED, AROUND_2005, AROUND_2006),
	CHATBOX_BUTTON_SELECTED_HOVERED(SpriteID.ChatTabButton.SELECTED_HOVERED, AROUND_2005, AROUND_2006),
	CHATBOX_REPORT_BUTTON(SpriteID.ReportButton.BUTTON, AROUND_2005, AROUND_2006),
	CHATBOX_REPORT_BUTTON_HOVERED(SpriteID.ReportButton.HOVERED, AROUND_2005, AROUND_2006),

	SCROLLBAR_ARROW_UP(SpriteID.ScrollbarV2.ARROW_UP, AROUND_2005),
	SCROLLBAR_ARROW_DOWN(SpriteID.ScrollbarV2.ARROW_DOWN, AROUND_2005),
	SCROLLBAR_THUMB_TOP(SpriteID.ScrollbarDraggerV2.TOP, AROUND_2005),
	SCROLLBAR_THUMB_MIDDLE(SpriteID.ScrollbarDraggerV2.MIDDLE, AROUND_2005),
	SCROLLBAR_THUMB_BOTTOM(SpriteID.ScrollbarDraggerV2.BOTTOM, AROUND_2005),
	SCROLLBAR_THUMB_MIDDLE_DARK(SpriteID.ScrollbarDraggerV2.TRACK, AROUND_2005),

	TAB_STONE_TOP_LEFT_SELECTED(SpriteID.SideStoneHighlights.TOP_LEFT, AROUND_2010),
	TAB_STONE_TOP_RIGHT_SELECTED(SpriteID.SideStoneHighlights.TOP_RIGHT, AROUND_2010),
	TAB_STONE_BOTTOM_LEFT_SELECTED(SpriteID.SideStoneHighlights.BOTTOM_LEFT, AROUND_2010),
	TAB_STONE_BOTTOM_RIGHT_SELECTED(SpriteID.SideStoneHighlights.BOTTOM_RIGHT, AROUND_2010),
	TAB_STONE_MIDDLE_SELECTED(SpriteID.SideStoneHighlights.MIDDLE, AROUND_2010),

	FIXED_MODE_SIDE_PANEL_BACKGROUND(SpriteID.SIDE_BACKGROUND, AROUND_2005, AROUND_2006),
	FIXED_MODE_TABS_ROW_BOTTOM(SpriteID.SIDE_BACKGROUND_BOTTOM, AROUND_2005, AROUND_2006, AROUND_2010),

	OLD_SCHOOl_MODE_SIDE_PANEL_EDGE_LEFT_UPPER(SpriteID.SIDE_BACKGROUND_LEFT1, AROUND_2005, AROUND_2006, AROUND_2010),
	OLD_SCHOOl_MODE_SIDE_PANEL_EDGE_LEFT_LOWER(SpriteID.SIDE_BACKGROUND_LEFT2, AROUND_2005, AROUND_2006, AROUND_2010),
	OLD_SCHOOl_MODE_SIDE_PANEL_EDGE_RIGHT(SpriteID.SIDE_BACKGROUND_RIGHT, AROUND_2005, AROUND_2006, AROUND_2010),

	FIXED_MODE_TABS_TOP_ROW(SpriteID.SIDE_BACKGROUND_TOP, AROUND_2005, AROUND_2006, AROUND_2010),
	FIXED_MODE_MINIMAP_LEFT_EDGE(SpriteID.MINI_LEFT, AROUND_2005, AROUND_2006, AROUND_2010),
	FIXED_MODE_MINIMAP_RIGHT_EDGE(SpriteID.MINI_RIGHT, AROUND_2005, AROUND_2006, AROUND_2010),
	FIXED_MODE_WINDOW_FRAME_EDGE_TOP(SpriteID.MAIN_TOP, AROUND_2005, AROUND_2006, AROUND_2010),
	FIXED_MODE_MINIMAP_AND_COMPASS_FRAME(SpriteID.FIXED_MINIMAP_COVER, AROUND_2005, AROUND_2006, AROUND_2010),
	FIXED_MODE_MINIMAP_FRAME_BOTTOM(SpriteID.MINI_BOTTOM, AROUND_2005, AROUND_2006),
	FIXED_MODE_TOP_RIGHT_CORNER(SpriteID.MINI_TOPRIGHT, AROUND_2005, AROUND_2006),

	RESIZEABLE_MODE_TABS_TOP_ROW(SpriteID.OsrsStretchSideTopbottom.TOP_ROW, AROUND_2010),
	RESIZEABLE_MODE_TABS_BOTTOM_ROW(SpriteID.OsrsStretchSideTopbottom.BOTTOM_ROW, AROUND_2010),
	RESIZEABLE_MODE_SIDE_PANEL_EDGE_LEFT(SpriteID.OsrsStretchSideColumns.EDGE_LEFT, AROUND_2010),
	RESIZEABLE_MODE_SIDE_PANEL_EDGE_RIGHT(SpriteID.OsrsStretchSideColumns.EDGE_RIGHT, AROUND_2010),
	RESIZEABLE_MODE_MINIMAP_AND_COMPASS_FRAME(SpriteID.OSRS_STRETCH_MAPSURROUND, AROUND_2010),
	RESIZEABLE_MODE_TAB_STONE_MIDDLE(SpriteID.PreEocStones.MIDDLE, AROUND_2010),
	RESIZEABLE_MODE_TAB_STONE_MIDDLE_SELECTED(SpriteID.PreEocStones.MIDDLE_SELECTED, AROUND_2010);

	private final int spriteID;
	private final Skin[] skin;

	SpriteOverride(int spriteID, Skin... skin)
	{
		this.spriteID = spriteID;
		this.skin = skin;
	}
}