/*
 * Copyright (c) 2018, <PERSON><PERSON><PERSON>, <https://github.com/Kamielvf>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON>UBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.screenmarkers;

import java.awt.Rectangle;
import java.awt.event.MouseEvent;
import javax.swing.SwingUtilities;
import net.runelite.client.input.MouseAdapter;

class ScreenMarkerMouseListener extends MouseAdapter
{
	private final ScreenMarkerPlugin plugin;

	ScreenMarkerMouseListener(ScreenMarkerPlugin plugin)
	{
		this.plugin = plugin;
	}

	@Override
	public MouseEvent mouseClicked(MouseEvent event)
	{
		if (SwingUtilities.isMiddleMouseButton(event))
		{
			return event;
		}

		event.consume();
		return event;
	}

	@Override
	public MouseEvent mousePressed(MouseEvent event)
	{
		if (SwingUtilities.isMiddleMouseButton(event))
		{
			return event;
		}

		if (SwingUtilities.isLeftMouseButton(event))
		{
			final Rectangle bounds = plugin.getSelectedWidgetBounds();

			if (bounds != null)
			{
				plugin.startCreation(bounds.getLocation(), bounds.getSize());
			}
			else
			{
				plugin.startCreation(event.getPoint());
			}
		}
		else if (plugin.isCreatingScreenMarker())
		{
			plugin.finishCreation(true);
		}

		event.consume();
		return event;
	}

	@Override
	public MouseEvent mouseReleased(MouseEvent event)
	{
		if (SwingUtilities.isMiddleMouseButton(event))
		{
			return event;
		}

		if (SwingUtilities.isLeftMouseButton(event) && plugin.isCreatingScreenMarker())
		{
			/* Set the creation panel as "ready" (because the marker area as been drawn) */
			plugin.completeSelection();
		}

		event.consume();
		return event;
	}

	@Override
	public MouseEvent mouseDragged(MouseEvent event)
	{
		if (!plugin.isCreatingScreenMarker())
		{
			return event;
		}

		if (SwingUtilities.isLeftMouseButton(event))
		{
			plugin.resizeMarker(event.getPoint());
		}

		return event;
	}
}
