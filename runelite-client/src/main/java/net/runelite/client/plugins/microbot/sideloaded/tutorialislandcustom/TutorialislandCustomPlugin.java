package net.runelite.client.plugins.microbot.sideloaded.tutorialislandcustom;

import com.google.inject.Provides;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.runelite.client.config.ConfigManager;
import net.runelite.client.eventbus.Subscribe;
import net.runelite.client.events.ConfigChanged;
import net.runelite.client.plugins.Plugin;
import net.runelite.client.plugins.PluginDescriptor;
import net.runelite.client.ui.overlay.OverlayManager;

import javax.inject.Inject;
import java.awt.*;

@PluginDescriptor(
        name = PluginDescriptor.Default + "TutorialIsland Instant123",
        description = "Microbot tutorialIsland plugin (CUSTOM)",
        tags = {"TutorialIsland", "microbot"},
        enabledByDefault = false
)
@Slf4j
public class TutorialislandCustomPlugin extends Plugin {

    @Getter
    private boolean toggleMusic;
    @Getter
    private boolean toggleRoofs;
    @Getter
    private boolean toggleLevelUp;
    @Getter
    private boolean toggleShiftDrop;
    @Getter
    private boolean toggleDevOverlay;
    
    
    @Inject
    private TutorialIslandCustomConfig config;
    @Provides
    TutorialIslandCustomConfig provideConfig(ConfigManager configManager) {
        return configManager.getConfig(TutorialIslandCustomConfig.class);
    }

    @Inject
    private OverlayManager overlayManager;
    @Inject
    private TutorialIslandCustomOverlay tutorialIslandOverlay;

    @Inject
    TutorialIslandCustomScript tutorialIslandScript;


    @Override
    protected void startUp() throws AWTException {
        toggleMusic = config.toggleMusic();
        toggleRoofs = config.toggleRoofs();
        toggleLevelUp = config.toggleDisableLevelUp();
        toggleShiftDrop = config.toggleShiftDrop();
        toggleDevOverlay = config.toggleDevOverlay();
        
        if (overlayManager != null) {
            overlayManager.add(tutorialIslandOverlay);
        }
        
        tutorialIslandScript.run(config);
    }

    protected void shutDown() {
        tutorialIslandScript.shutdown();
        overlayManager.remove(tutorialIslandOverlay);
    }

    @Subscribe
    public void onConfigChanged(final ConfigChanged event) {
        if (!event.getGroup().equals(TutorialIslandCustomConfig.configGroup)) return;

        if (event.getKey().equals(TutorialIslandCustomConfig.toggleMusic)) {
            toggleMusic = config.toggleMusic();
        }
        
        if (event.getKey().equals(TutorialIslandCustomConfig.toggleRoofs)) {
            toggleRoofs = config.toggleRoofs();
        }

        if (event.getKey().equals(TutorialIslandCustomConfig.toggleLevelUp)) {
            toggleLevelUp = config.toggleDisableLevelUp();
        }

        if (event.getKey().equals(TutorialIslandCustomConfig.toggleShiftDrop)) {
            toggleShiftDrop = config.toggleShiftDrop();
        }

        if (event.getKey().equals(TutorialIslandCustomConfig.toggleDevOverlay)) {
            toggleDevOverlay = config.toggleDevOverlay();
        }
    }
}
