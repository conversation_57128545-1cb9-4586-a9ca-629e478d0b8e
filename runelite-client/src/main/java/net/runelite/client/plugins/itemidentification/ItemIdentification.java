/*
 * Copyright (c) 2019, Hydrox6 <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.itemidentification;

import com.google.common.collect.ImmutableMap;
import java.util.Map;
import java.util.function.Predicate;
import lombok.AllArgsConstructor;
import net.runelite.api.gameval.ItemID;

enum ItemIdentification
{
	//Seeds
	GUAM_SEED(Type.SEED_HERB, "Guam", "G", ItemID.GUAM_SEED),
	MARRENTILL_SEED(Type.SEED_HERB, "Marren", "M", ItemID.MARRENTILL_SEED),
	TARROMIN_SEED(Type.SEED_HERB, "Tarro", "TAR", ItemID.TARROMIN_SEED),
	HARRALANDER_SEED(Type.SEED_HERB, "Harra", "HAR", ItemID.HARRALANDER_SEED),
	RANARR_SEED(Type.SEED_HERB, "Ranarr", "R", ItemID.RANARR_SEED),
	TOADFLAX_SEED(Type.SEED_HERB, "Toad", "TOA", ItemID.TOADFLAX_SEED),
	IRIT_SEED(Type.SEED_HERB, "Irit", "I", ItemID.IRIT_SEED),
	AVANTOE_SEED(Type.SEED_HERB, "Avan", "A", ItemID.AVANTOE_SEED),
	KWUARM_SEED(Type.SEED_HERB, "Kwuarm", "K", ItemID.KWUARM_SEED),
	SNAPDRAGON_SEED(Type.SEED_HERB, "Snap", "S", ItemID.SNAPDRAGON_SEED),
	CADANTINE_SEED(Type.SEED_HERB, "Cadan", "C", ItemID.CADANTINE_SEED),
	LANTADYME_SEED(Type.SEED_HERB, "Lanta", "L", ItemID.LANTADYME_SEED),
	DWARF_WEED_SEED(Type.SEED_HERB, "Dwarf", "D", ItemID.DWARF_WEED_SEED),
	TORSTOL_SEED(Type.SEED_HERB, "Torstol", "TOR", ItemID.TORSTOL_SEED),
	HUASCA_SEED(Type.SEED_HERB, "Huasca", "HUA", ItemID.HUASCA_SEED),

	REDBERRY_SEED(Type.SEED_BERRY, "Redberry", "RED", ItemID.REDBERRY_BUSH_SEED),
	CADAVABERRY_SEED(Type.SEED_BERRY, "Cadava", "CAD", ItemID.CADAVABERRY_BUSH_SEED),
	DWELLBERRY_SEED(Type.SEED_BERRY, "Dwell", "DWEL", ItemID.DWELLBERRY_BUSH_SEED),
	JANGERBERRY_SEED(Type.SEED_BERRY, "Janger", "JANG", ItemID.JANGERBERRY_BUSH_SEED),
	WHITEBERRY_SEED(Type.SEED_BERRY, "White", "WHIT", ItemID.WHITEBERRY_BUSH_SEED),
	POISON_IVY_SEED(Type.SEED_BERRY, "Ivy", "POI", ItemID.POISONIVY_BUSH_SEED),

	GRAPE_SEED(Type.SEED_SPECIAL, "Grape", "GRA", ItemID.GRAPE_SEED),
	MUSHROOM_SPORE(Type.SEED_SPECIAL, "Mushroom", "MUSH", ItemID.MUSHROOM_SEED),
	BELLADONNA_SEED(Type.SEED_SPECIAL, "Bella", "BELL", ItemID.BELLADONNA_SEED),
	SEAWEED_SPORE(Type.SEED_SPECIAL, "Seaweed", "SW", ItemID.SEAWEED_SEED),
	HESPORI_SEED(Type.SEED_SPECIAL, "Hespori", "HES", ItemID.HESPORI_SEED),
	KRONOS_SEED(Type.SEED_SPECIAL, "Kronos", "KRO", ItemID.KRONOS_SEED),
	IASOR_SEED(Type.SEED_SPECIAL, "Iasor", "IA", ItemID.IASOR_SEED),
	ATTAS_SEED(Type.SEED_SPECIAL, "Attas", "AT", ItemID.ATTAS_SEED),
	CACTUS_SEED(Type.SEED_SPECIAL, "Cactus", "CAC", ItemID.CACTUS_SEED),
	POTATO_CACTUS_SEED(Type.SEED_SPECIAL, "P.Cact", "P.CAC", ItemID.POTATO_CACTUS_SEED),

	ACORN(Type.SEED_TREE, "Oak", "OAK", ItemID.ACORN),
	WILLOW_SEED(Type.SEED_TREE, "Willow", "WIL", ItemID.WILLOW_SEED),
	MAPLE_SEED(Type.SEED_TREE, "Maple", "MAP", ItemID.MAPLE_SEED),
	YEW_SEED(Type.SEED_TREE, "Yew", "YEW", ItemID.YEW_SEED),
	MAGIC_SEED(Type.SEED_TREE, "Magic", "MAG", ItemID.MAGIC_TREE_SEED),
	REDWOOD_SEED(Type.SEED_TREE, "Red", "RED", ItemID.REDWOOD_TREE_SEED),
	TEAK_SEED(Type.SEED_TREE, "Teak", "TEAK", ItemID.TEAK_SEED),
	MAHOGANY_SEED(Type.SEED_TREE, "Mahog", "MAH", ItemID.MAHOGANY_SEED),
	CRYSTAL_ACORN(Type.SEED_TREE, "Crystal", "CRY", ItemID.CRYSTAL_TREE_SEED),
	CELASTRUS_SEED(Type.SEED_TREE, "Celas", "CEL", ItemID.CELASTRUS_TREE_SEED),
	SPIRIT_SEED(Type.SEED_TREE, "Spirit", "SPI", ItemID.SPIRIT_TREE_SEED),
	CALQUAT_SEED(Type.SEED_TREE, "Calquat", "CAL", ItemID.CALQUAT_TREE_SEED),

	APPLE_TREE_SEED(Type.SEED_FRUIT_TREE, "Apple", "APP", ItemID.APPLE_TREE_SEED),
	BANANA_TREE_SEED(Type.SEED_FRUIT_TREE, "Banana", "BAN", ItemID.BANANA_TREE_SEED),
	ORANGE_TREE_SEED(Type.SEED_FRUIT_TREE, "Orange", "ORA", ItemID.ORANGE_TREE_SEED),
	CURRY_TREE_SEED(Type.SEED_FRUIT_TREE, "Curry", "CUR", ItemID.CURRY_TREE_SEED),
	PINEAPPLE_SEED(Type.SEED_FRUIT_TREE, "Pine.A", "PINE", ItemID.PINEAPPLE_TREE_SEED),
	PAPAYA_TREE_SEED(Type.SEED_FRUIT_TREE, "Papaya", "PAPA", ItemID.PAPAYA_TREE_SEED),
	PALM_TREE_SEED(Type.SEED_FRUIT_TREE, "Palm", "PALM", ItemID.PALM_TREE_SEED),
	DRAGONFRIUT_TREE_SEED(Type.SEED_FRUIT_TREE, "Dragon", "DRA", ItemID.DRAGONFRUIT_TREE_SEED),

	POTATO_SEED(Type.SEED_ALLOTMENT, "Potato", "POT", ItemID.POTATO_SEED),
	ONION_SEED(Type.SEED_ALLOTMENT, "Onion", "ONI", ItemID.ONION_SEED),
	CABBAGE_SEED(Type.SEED_ALLOTMENT, "Cabbage", "CAB", ItemID.CABBAGE_SEED),
	TOMATO_SEED(Type.SEED_ALLOTMENT, "Tomato", "TOM", ItemID.TOMATO_SEED),
	SWEETCORN_SEED(Type.SEED_ALLOTMENT, "S.Corn", "CORN", ItemID.SWEETCORN_SEED),
	STRAWBERRY_SEED(Type.SEED_ALLOTMENT, "S.Berry", "STRA", ItemID.STRAWBERRY_SEED),
	WATERMELON_SEED(Type.SEED_ALLOTMENT, "Melon", "MEL", ItemID.WATERMELON_SEED),
	SNAPE_GRASS_SEED(Type.SEED_ALLOTMENT, "Snape", "SNA", ItemID.SNAPE_GRASS_SEED),

	MARIGOLD_SEED(Type.SEED_FLOWER, "Marigo", "MARI", ItemID.MARIGOLD_SEED),
	ROSEMARY_SEED(Type.SEED_FLOWER, "Rosemar", "ROSE", ItemID.ROSEMARY_SEED),
	NASTURTIUM_SEED(Type.SEED_FLOWER, "Nastur", "NAS", ItemID.NASTURTIUM_SEED),
	WOAD_SEED(Type.SEED_FLOWER, "Woad", "WOAD", ItemID.WOAD_SEED),
	LIMPWURT_SEED(Type.SEED_FLOWER, "Limpwurt", "LIMP", ItemID.LIMPWURT_SEED),
	WHITE_LILY_SEED(Type.SEED_FLOWER, "Lily", "LILY", ItemID.WHITE_LILY_SEED),

	BARLEY_SEED(Type.HOPS_SEED, "Barley", "BAR", ItemID.BARLEY_SEED),
	HAMMERSTONE_SEED(Type.HOPS_SEED, "Hammer", "HAMM", ItemID.HAMMERSTONE_HOP_SEED),
	ASGARNIAN_SEED(Type.HOPS_SEED, "Asgar", "ASG", ItemID.ASGARNIAN_HOP_SEED),
	JUTE_SEED(Type.HOPS_SEED, "Jute", "JUTE", ItemID.JUTE_SEED),
	YANILLIAN_SEED(Type.HOPS_SEED, "Yani", "YAN", ItemID.YANILLIAN_HOP_SEED),
	KRANDORIAN_SEED(Type.HOPS_SEED, "Krand", "KRA", ItemID.KRANDORIAN_HOP_SEED),
	WILDBLOOD_SEED(Type.HOPS_SEED, "Wild.B", "WILD", ItemID.WILDBLOOD_HOP_SEED),

	//Sacks
	SACK(Type.SACK, "Empty", "EMP", ItemID.SACK_EMPTY),
	CABBAGE_SACK(Type.SACK, "Cabbage", "CAB", ItemID.SACK_CABBAGE_1, ItemID.SACK_CABBAGE_2, ItemID.SACK_CABBAGE_3, ItemID.SACK_CABBAGE_4, ItemID.SACK_CABBAGE_5, ItemID.SACK_CABBAGE_6, ItemID.SACK_CABBAGE_7, ItemID.SACK_CABBAGE_8, ItemID.SACK_CABBAGE_9, ItemID.SACK_CABBAGE_10),
	ONION_SACK(Type.SACK, "Onion", "ONI", ItemID.SACK_ONION_1, ItemID.SACK_ONION_2, ItemID.SACK_ONION_3, ItemID.SACK_ONION_4, ItemID.SACK_ONION_5, ItemID.SACK_ONION_6, ItemID.SACK_ONION_7, ItemID.SACK_ONION_8, ItemID.SACK_ONION_9, ItemID.SACK_ONION_10),
	POTATO_SACK(Type.SACK, "Potato", "POT", ItemID.SACK_POTATO_1, ItemID.SACK_POTATO_2, ItemID.SACK_POTATO_3, ItemID.SACK_POTATO_4, ItemID.SACK_POTATO_5, ItemID.SACK_POTATO_6, ItemID.SACK_POTATO_7, ItemID.SACK_POTATO_8, ItemID.SACK_POTATO_9, ItemID.SACK_POTATO_10),

	//Herbs
	GUAM(Type.HERB, "Guam", "G", ItemID.GUAM_LEAF, ItemID.UNIDENTIFIED_GUAM),
	MARRENTILL(Type.HERB, "Marren", "M", ItemID.MARENTILL, ItemID.UNIDENTIFIED_MARENTILL),
	TARROMIN(Type.HERB, "Tarro", "TAR", ItemID.TARROMIN, ItemID.UNIDENTIFIED_TARROMIN),
	HARRALANDER(Type.HERB, "Harra", "HAR", ItemID.HARRALANDER, ItemID.UNIDENTIFIED_HARRALANDER),
	RANARR(Type.HERB, "Ranarr", "R", ItemID.RANARR_WEED, ItemID.UNIDENTIFIED_RANARR),
	TOADFLAX(Type.HERB, "Toad", "TOA", ItemID.TOADFLAX, ItemID.UNIDENTIFIED_TOADFLAX),
	IRIT(Type.HERB, "Irit", "I", ItemID.IRIT_LEAF, ItemID.UNIDENTIFIED_IRIT),
	AVANTOE(Type.HERB, "Avan", "A", ItemID.AVANTOE, ItemID.UNIDENTIFIED_AVANTOE),
	KWUARM(Type.HERB, "Kwuarm", "K", ItemID.KWUARM, ItemID.UNIDENTIFIED_KWUARM),
	SNAPDRAGON(Type.HERB, "Snap", "S", ItemID.SNAPDRAGON, ItemID.UNIDENTIFIED_SNAPDRAGON),
	CADANTINE(Type.HERB, "Cadan", "C", ItemID.CADANTINE, ItemID.UNIDENTIFIED_CADANTINE),
	LANTADYME(Type.HERB, "Lanta", "L", ItemID.LANTADYME, ItemID.UNIDENTIFIED_LANTADYME),
	DWARF_WEED(Type.HERB, "Dwarf", "D", ItemID.DWARF_WEED, ItemID.UNIDENTIFIED_DWARF_WEED),
	TORSTOL(Type.HERB, "Torstol", "TOR", ItemID.TORSTOL, ItemID.UNIDENTIFIED_TORSTOL),
	HUASCA(Type.HERB, "Huasca", "HUA", ItemID.HUASCA, ItemID.UNIDENTIFIED_HUASCA),

	ARDRIGAL(Type.HERB, "Ardrig", "ARD", ItemID.ARDRIGAL, ItemID.UNIDENTIFIED_ARDRIGAL),
	ROGUES_PURSE(Type.HERB, "Rogue", "ROG", ItemID.ROGUES_PURSE, ItemID.UNIDENTIFIED_ROGUES_PURSE),
	SITO_FOIL(Type.HERB, "Sito", "SF", ItemID.SITO_FOIL, ItemID.UNIDENTIFIED_SITO_FOIL),
	SNAKE_WEED(Type.HERB, "Snake", "SW", ItemID.SNAKE_WEED, ItemID.UNIDENTIFIED_SNAKE_WEED),
	VOLENCIA_MOSS(Type.HERB, "Volenc", "V", ItemID.VOLENCIA_MOSS, ItemID.UNIDENTIFIED_VOLENCIA_MOSS),

	//Logs
	RED_LOGS(Type.LOGS, "Red", "RED", ItemID.RED_LOGS),
	GREEN_LOGS(Type.LOGS, "Green", "GRE", ItemID.GREEN_LOGS),
	BLUE_LOGS(Type.LOGS, "Blue", "BLU", ItemID.BLUE_LOGS),
	WHITE_LOGS(Type.LOGS, "White", "WHI", ItemID.TRAIL_LOGS_WHITE),
	PURPLE_LOGS(Type.LOGS, "Purple", "PUR", ItemID.TRAIL_LOGS_PURPLE),

	SCRAPEY_TREE_LOGS(Type.LOGS, "Scrapey", "SCRAP", ItemID.BREW_SCRAPEY_LOGS),

	LOG(Type.LOGS, "Log", "LOG", ItemID.LOGS),
	ACHEY_TREE_LOG(Type.LOGS, "Achey", "ACH", ItemID.ACHEY_TREE_LOGS),
	OAK_LOG(Type.LOGS, "Oak", "OAK", ItemID.OAK_LOGS),
	WILLOW_LOG(Type.LOGS, "Willow", "WIL", ItemID.WILLOW_LOGS),
	TEAK_LOG(Type.LOGS, "Teak", "TEAK", ItemID.TEAK_LOGS),
	JUNIPER_LOG(Type.LOGS, "Juniper", "JUN", ItemID.JUNIPER_LOGS),
	MAPLE_LOG(Type.LOGS, "Maple", "MAP", ItemID.MAPLE_LOGS),
	MAHOGANY_LOG(Type.LOGS, "Mahog", "MAH", ItemID.MAHOGANY_LOGS),
	ARCTIC_PINE_LOG(Type.LOGS, "Arctic", "ARC", ItemID.ARCTIC_PINE_LOG),
	YEW_LOG(Type.LOGS, "Yew", "YEW", ItemID.YEW_LOGS),
	BLISTERWOOD_LOG(Type.LOGS, "Blister", "BLI", ItemID.BLISTERWOOD_LOGS),
	MAGIC_LOG(Type.LOGS, "Magic", "MAG", ItemID.MAGIC_LOGS),
	REDWOOD_LOG(Type.LOGS, "Red", "RED", ItemID.REDWOOD_LOGS),

	PYRE_LOGS(Type.LOGS_PYRE, "Pyre", "P", ItemID.LOGS_PYRE),
	ARCTIC_PYRE_LOGS(Type.LOGS_PYRE, "Art P", "AP", ItemID.ARCTIC_PINE_LOGS_PYRE),
	OAK_PYRE_LOGS(Type.LOGS_PYRE, "Oak P", "OAKP", ItemID.OAK_LOGS_PYRE),
	WILLOW_PYRE_LOGS(Type.LOGS_PYRE, "Wil P", "WILP", ItemID.WILLOW_LOGS_PYRE),
	TEAK_PYRE_LOGS(Type.LOGS_PYRE, "Teak P", "TEAKP", ItemID.TEAK_LOGS_PYRE),
	MAPLE_PYRE_LOGS(Type.LOGS_PYRE, "Map P", "MAPP", ItemID.MAPLE_LOGS_PYRE),
	MAHOGANY_PYRE_LOGS(Type.LOGS_PYRE, "Mah P", "MAHP", ItemID.MAHOGANY_LOGS_PYRE),
	YEW_PYRE_LOGS(Type.LOGS_PYRE, "Yew P", "YEWP", ItemID.YEW_LOGS_PYRE),
	MAGIC_PYRE_LOGS(Type.LOGS_PYRE, "Mag P", "MAGP", ItemID.MAGIC_LOGS_PYRE),
	REDWOOD_PYRE_LOGS(Type.LOGS_PYRE, "Red P", "REDP", ItemID.REDWOOD_LOGS_PYRE),

	//Planks
	PLANK(Type.PLANK, "Plank", "PLANK", ItemID.WOODPLANK),
	OAK_PLANK(Type.PLANK, "Oak", "OAK", ItemID.PLANK_OAK),
	TEAK_PLANK(Type.PLANK, "Teak", "TEAK", ItemID.PLANK_TEAK),
	MAHOGANY_PLANK(Type.PLANK, "Mahog", "MAH", ItemID.PLANK_MAHOGANY),
	WAXWOOD_PLANK(Type.PLANK, "Wax", "WAX", ItemID.DADDYSHOME_WAXWOOD_PLANK),
	MALLIGNUM_ROOT_PLANK(Type.PLANK, "Mallig", "MALL", ItemID.RAIDS_PLANK),

	//Saplings
	OAK_SAPLING(Type.SAPLING, "Oak", "OAK", ItemID.PLANTPOT_OAK_SAPLING, ItemID.PLANTPOT_ACORN, ItemID.PLANTPOT_ACORN_WATERED),
	WILLOW_SAPLING(Type.SAPLING, "Willow", "WIL", ItemID.PLANTPOT_WILLOW_SAPLING, ItemID.PLANTPOT_WILLOW_SEED, ItemID.PLANTPOT_WILLOW_SEED_WATERED),
	MAPLE_SAPLING(Type.SAPLING, "Maple", "MAP", ItemID.PLANTPOT_MAPLE_SAPLING, ItemID.PLANTPOT_MAPLE_SEED, ItemID.PLANTPOT_MAPLE_SEED_WATERED),
	YEW_SAPLING(Type.SAPLING, "Yew", "YEW", ItemID.PLANTPOT_YEW_SAPLING, ItemID.PLANTPOT_YEW_SEED, ItemID.PLANTPOT_YEW_SEED_WATERED),
	MAGIC_SAPLING(Type.SAPLING, "Magic", "MAG", ItemID.PLANTPOT_MAGIC_TREE_SAPLING, ItemID.PLANTPOT_MAGIC_TREE_SEED, ItemID.PLANTPOT_MAGIC_TREE_SEED_WATERED),
	REDWOOD_SAPLING(Type.SAPLING, "Red", "RED", ItemID.PLANTPOT_REDWOOD_TREE_SAPLING, ItemID.PLANTPOT_REDWOOD_TREE_SEED, ItemID.PLANTPOT_REDWOOD_TREE_SEED_WATERED),
	SPIRIT_SAPLING(Type.SAPLING, "Spirit", "SPI", ItemID.PLANTPOT_SPIRIT_TREE_SAPLING, ItemID.PLANTPOT_SPIRIT_TREE_SEED, ItemID.PLANTPOT_SPIRIT_TREE_SEED_WATERED),
	CRYSTAL_SAPLING(Type.SAPLING, "Crystal", "CRY", ItemID.PLANTPOT_CRYSTAL_TREE_SAPLING, ItemID.PLANTPOT_CRYSTAL_TREE_SEED, ItemID.PLANTPOT_CRYSTAL_TREE_SEED_WATERED),

	APPLE_SAPLING(Type.SAPLING, "Apple", "APP", ItemID.PLANTPOT_APPLE_SAPLING, ItemID.PLANTPOT_APPLE_SEED, ItemID.PLANTPOT_APPLE_SEED_WATERED),
	BANANA_SAPLING(Type.SAPLING, "Banana", "BAN", ItemID.PLANTPOT_BANANA_SAPLING, ItemID.PLANTPOT_BANANA_SEED, ItemID.PLANTPOT_BANANA_SEED_WATERED),
	ORANGE_SAPLING(Type.SAPLING, "Orange", "ORA", ItemID.PLANTPOT_ORANGE_SAPLING, ItemID.PLANTPOT_ORANGE_SEED, ItemID.PLANTPOT_ORANGE_SEED_WATERED),
	CURRY_SAPLING(Type.SAPLING, "Curry", "CUR", ItemID.PLANTPOT_CURRY_SAPLING, ItemID.PLANTPOT_CURRY_SEED, ItemID.PLANTPOT_CURRY_SEED_WATERED),
	PINEAPPLE_SAPLING(Type.SAPLING, "Pine", "PINE", ItemID.PLANTPOT_PINEAPPLE_SAPLING, ItemID.PLANTPOT_PINEAPPLE_SEED, ItemID.PLANTPOT_PINEAPPLE_SEED_WATERED),
	PAPAYA_SAPLING(Type.SAPLING, "Papaya", "PAP", ItemID.PLANTPOT_PAPAYA_SAPLING, ItemID.PLANTPOT_PAPAYA_SEED, ItemID.PLANTPOT_PAPAYA_SEED_WATERED),
	PALM_SAPLING(Type.SAPLING, "Palm", "PALM", ItemID.PLANTPOT_PALM_SAPLING, ItemID.PLANTPOT_PALM_SEED, ItemID.PLANTPOT_PALM_SEED_WATERED),
	DRAGONFRUIT_SAPLING(Type.SAPLING, "Dragon", "DRAG", ItemID.PLANTPOT_DRAGONFRUIT_SAPLING, ItemID.PLANTPOT_DRAGONFRUIT_SEED, ItemID.PLANTPOT_DRAGONFRUIT_SEED_WATERED),

	TEAK_SAPLING(Type.SAPLING, "Teak", "TEAK", ItemID.PLANTPOT_TEAK_SAPLING, ItemID.PLANTPOT_TEAK_SEED, ItemID.PLANTPOT_TEAK_SEED_WATERED),
	MAHOGANY_SAPLING(Type.SAPLING, "Mahog", "MAHOG", ItemID.PLANTPOT_MAHOGANY_SAPLING, ItemID.PLANTPOT_MAHOGANY_SEED, ItemID.PLANTPOT_MAHOGANY_SEED_WATERED),
	CALQUAT_SAPLING(Type.SAPLING, "Calquat", "CALQ", ItemID.PLANTPOT_CALQUAT_SAPLING, ItemID.PLANTPOT_CALQUAT_SEED, ItemID.PLANTPOT_CALQUAT_SEED_WATERED),
	CELASTRUS_SAPLING(Type.SAPLING, "Celas", "CEL", ItemID.PLANTPOT_CELASTRUS_TREE_SAPLING, ItemID.PLANTPOT_CELASTRUS_TREE_SEED, ItemID.PLANTPOT_CELASTRUS_TREE_SEED_WATERED),

	//Compost
	COMPOST(Type.COMPOST, "Compost", "COM", ItemID.BUCKET_COMPOST),
	SUPERCOMPOST(Type.COMPOST, "Sup Com", "SCOM", ItemID.BUCKET_SUPERCOMPOST),
	ULTRACOMPOST(Type.COMPOST, "Ult Com", "UCOM", ItemID.BUCKET_ULTRACOMPOST),

	//Ores
	COPPER_ORE(Type.ORE, "Copper", "COP", ItemID.COPPER_ORE),
	TIN_ORE(Type.ORE, "Tin", "TIN", ItemID.TIN_ORE),
	IRON_ORE(Type.ORE, "Iron", "IRO", ItemID.IRON_ORE),
	SILVER_ORE(Type.ORE, "Silver", "SIL", ItemID.SILVER_ORE),
	COAL_ORE(Type.ORE, "Coal", "COA", ItemID.COAL),
	GOLD_ORE(Type.ORE, "Gold", "GOL", ItemID.GOLD_ORE),
	MITHRIL_ORE(Type.ORE, "Mithril", "MIT", ItemID.MITHRIL_ORE),
	ADAMANTITE_ORE(Type.ORE, "Adaman", "ADA", ItemID.ADAMANTITE_ORE),
	RUNITE_ORE(Type.ORE, "Runite", "RUN", ItemID.RUNITE_ORE),

	RUNE_ESSENCE(Type.ORE, "R.Ess", "R.E.", ItemID.BLANKRUNE),
	PURE_ESSENCE(Type.ORE, "P.Ess", "P.E.", ItemID.BLANKRUNE_HIGH),

	PAYDIRT(Type.ORE, "Paydirt", "PAY", ItemID.PAYDIRT),
	AMETHYST(Type.ORE, "Amethy", "AME", ItemID.AMETHYST),
	LOVAKITE_ORE(Type.ORE, "Lovakit", "LOV", ItemID.LOVAKITE_ORE),
	BLURITE_ORE(Type.ORE, "Blurite", "BLU", ItemID.BLURITE_ORE),
	ELEMENTAL_ORE(Type.ORE, "Element", "ELE", ItemID.ELEMENTAL_WORKSHOP_ORE),
	DAEYALT_ORE(Type.ORE, "Daeyalt", "DAE", ItemID.SANG_JAS_ORE),
	LUNAR_ORE(Type.ORE, "Lunar", "LUN", ItemID.QUEST_LUNAR_MAGIC_ORE),

	//Bars
	BRONZE_BAR(Type.BAR, "Bronze", "BRO", ItemID.BRONZE_BAR),
	IRON_BAR(Type.BAR, "Iron", "IRO", ItemID.IRON_BAR),
	SILVER_BAR(Type.BAR, "Silver", "SIL", ItemID.SILVER_BAR),
	STEEL_BAR(Type.BAR, "Steel", "STE", ItemID.STEEL_BAR),
	GOLD_BAR(Type.BAR, "Gold", "GOL", ItemID.GOLD_BAR),
	MITHRIL_BAR(Type.BAR, "Mithril", "MIT", ItemID.MITHRIL_BAR),
	ADAMANTITE_BAR(Type.BAR, "Adaman", "ADA", ItemID.ADAMANTITE_BAR),
	RUNITE_BAR(Type.BAR, "Runite", "RUN", ItemID.RUNITE_BAR),

	BLURITE_BAR(Type.BAR, "Blurite", "BLU", ItemID.BLURITE_BAR),
	ELEMENTAL_METAL(Type.BAR, "Elemental", "ELE", ItemID.ELEMENTAL_WORKSHOP_BAR),
	LOVAKITE_BAR(Type.BAR, "Lovakite", "LOV", ItemID.LOVAKITE_BAR),
	LUNAR_BAR(Type.BAR, "Lunar", "LUN", ItemID.QUEST_LUNAR_MAGIC_BAR),

	//Gems
	SAPPHIRE(Type.GEM, "Sapphir", "S", ItemID.UNCUT_SAPPHIRE, ItemID.SAPPHIRE),
	EMERALD(Type.GEM, "Emerald", "E", ItemID.UNCUT_EMERALD, ItemID.EMERALD),
	RUBY(Type.GEM, "Ruby", "R", ItemID.UNCUT_RUBY, ItemID.RUBY),
	DIAMOND(Type.GEM, "Diamon", "DI", ItemID.UNCUT_DIAMOND, ItemID.DIAMOND),
	OPAL(Type.GEM, "Opal", "OP", ItemID.UNCUT_OPAL, ItemID.OPAL),
	JADE(Type.GEM, "Jade", "J", ItemID.UNCUT_JADE, ItemID.JADE),
	RED_TOPAZ(Type.GEM, "Topaz", "T", ItemID.UNCUT_RED_TOPAZ, ItemID.RED_TOPAZ),
	DRAGONSTONE(Type.GEM, "Dragon", "DR", ItemID.UNCUT_DRAGONSTONE, ItemID.DRAGONSTONE),
	ONYX(Type.GEM, "Onyx", "ON", ItemID.UNCUT_ONYX, ItemID.ONYX),
	ZENYTE(Type.GEM, "Zenyte", "Z", ItemID.UNCUT_ZENYTE, ItemID.ZENYTE),
	SHADOW_DIAMOND(Type.GEM, "Shadow", "SHD", ItemID.FD_DARK_DIAMOND),
	BLOOD_DIAMOND(Type.GEM, "Blood", "BD", ItemID.FD_BLOOD_DIAMOND),
	ICE_DIAMOND(Type.GEM, "Ice", "ID", ItemID.FD_ICEDIAMOND),
	SMOKE_DIAMOND(Type.GEM, "Smoke", "SMD", ItemID.FD_DIAMOND_FIRE),

	// Potions
	ATTACK(Type.POTION, "Att", "A", ItemID._4DOSE1ATTACK, ItemID._3DOSE1ATTACK, ItemID._2DOSE1ATTACK, ItemID._1DOSE1ATTACK),
	ATTACK_MIX(Type.POTION, "Att", "A", ItemID.BRUTAL_1DOSE1ATTACK, ItemID.BRUTAL_2DOSE1ATTACK),
	STRENGTH(Type.POTION, "Str", "S", ItemID.STRENGTH4, ItemID._3DOSE1STRENGTH, ItemID._2DOSE1STRENGTH, ItemID._1DOSE1STRENGTH),
	STRENGTH_MIX(Type.POTION, "Str", "S", ItemID.BRUTAL_1DOSE1STRENGTH, ItemID.BRUTAL_2DOSE1STRENGTH),
	DEFENCE(Type.POTION, "Def", "D", ItemID._4DOSE1DEFENSE, ItemID._3DOSE1DEFENSE, ItemID._2DOSE1DEFENSE, ItemID._1DOSE1DEFENSE),
	DEFENCE_MIX(Type.POTION, "Def", "D", ItemID.BRUTAL_1DOSE1DEFENSE, ItemID.BRUTAL_2DOSE1DEFENSE),
	COMBAT(Type.POTION, "Com", "C", ItemID._4DOSECOMBAT, ItemID._3DOSECOMBAT, ItemID._2DOSECOMBAT, ItemID._1DOSECOMBAT),
	COMBAT_MIX(Type.POTION, "Com", "C", ItemID.BRUTAL_1DOSECOMBAT, ItemID.BRUTAL_2DOSECOMBAT),
	MAGIC(Type.POTION, "Magic", "M", ItemID._4DOSE1MAGIC, ItemID._3DOSE1MAGIC, ItemID._2DOSE1MAGIC, ItemID._1DOSE1MAGIC),
	MAGIC_MIX(Type.POTION, "Magic", "M", ItemID.BRUTAL_1DOSE1MAGIC, ItemID.BRUTAL_2DOSE1MAGIC),
	RANGING(Type.POTION, "Range", "R", ItemID._4DOSERANGERSPOTION, ItemID._3DOSERANGERSPOTION, ItemID._2DOSERANGERSPOTION, ItemID._1DOSERANGERSPOTION),
	RANGING_MIX(Type.POTION, "Range", "R", ItemID.BRUTAL_1DOSERANGERSPOTION, ItemID.BRUTAL_2DOSERANGERSPOTION),
	BASTION(Type.POTION, "Bastion", "B", ItemID._4DOSEBASTION, ItemID._3DOSEBASTION, ItemID._2DOSEBASTION, ItemID._1DOSEBASTION),
	BATTLEMAGE(Type.POTION, "BatMage", "B.M", ItemID._4DOSEBATTLEMAGE, ItemID._3DOSEBATTLEMAGE, ItemID._2DOSEBATTLEMAGE, ItemID._1DOSEBATTLEMAGE),

	SUPER_ATTACK(Type.POTION, "S.Att", "S.A", ItemID._4DOSE2ATTACK, ItemID._3DOSE2ATTACK, ItemID._2DOSE2ATTACK, ItemID._1DOSE2ATTACK),
	SUPER_ATTACK_MIX(Type.POTION, "S.Att", "S.A", ItemID.BRUTAL_1DOSE2ATTACK, ItemID.BRUTAL_2DOSE2ATTACK),
	SUPER_STRENGTH(Type.POTION, "S.Str", "S.S", ItemID._4DOSE2STRENGTH, ItemID._3DOSE2STRENGTH, ItemID._2DOSE2STRENGTH, ItemID._1DOSE2STRENGTH),
	SUPER_STRENGTH_MIX(Type.POTION, "S.Str", "S.S", ItemID.BRUTAL_1DOSE2STRENGTH, ItemID.BRUTAL_2DOSE2STRENGTH),
	SUPER_DEFENCE(Type.POTION, "S.Def", "S.D", ItemID._4DOSE2DEFENSE, ItemID._3DOSE2DEFENSE, ItemID._2DOSE2DEFENSE, ItemID._1DOSE2DEFENSE),
	SUPER_DEFENCE_MIX(Type.POTION, "S.Def", "S.D", ItemID.BRUTAL_1DOSE2DEFENSE, ItemID.BRUTAL_2DOSE2DEFENSE),
	SUPER_COMBAT(Type.POTION, "S.Com", "S.C", ItemID._4DOSE2COMBAT, ItemID._3DOSE2COMBAT, ItemID._2DOSE2COMBAT, ItemID._1DOSE2COMBAT),
	SUPER_RANGING(Type.POTION, "S.Range", "S.Ra", ItemID.NZONE4DOSE2RANGERSPOTION, ItemID.NZONE3DOSE2RANGERSPOTION, ItemID.NZONE2DOSE2RANGERSPOTION, ItemID.NZONE1DOSE2RANGERSPOTION),
	SUPER_MAGIC(Type.POTION, "S.Magic", "S.M", ItemID.NZONE4DOSE2MAGICPOTION, ItemID.NZONE3DOSE2MAGICPOTION, ItemID.NZONE2DOSE2MAGICPOTION, ItemID.NZONE1DOSE2MAGICPOTION),

	DIVINE_SUPER_ATTACK(Type.POTION, "S.Att", "S.A", ItemID._4DOSEDIVINEATTACK, ItemID._3DOSEDIVINEATTACK, ItemID._2DOSEDIVINEATTACK, ItemID._1DOSEDIVINEATTACK),
	DIVINE_SUPER_DEFENCE(Type.POTION, "S.Def", "S.D", ItemID._4DOSEDIVINEDEFENCE, ItemID._3DOSEDIVINEDEFENCE, ItemID._2DOSEDIVINEDEFENCE, ItemID._1DOSEDIVINEDEFENCE),
	DIVINE_SUPER_STRENGTH(Type.POTION, "S.Str", "S.S", ItemID._4DOSEDIVINESTRENGTH, ItemID._3DOSEDIVINESTRENGTH, ItemID._2DOSEDIVINESTRENGTH, ItemID._1DOSEDIVINESTRENGTH),
	DIVINE_SUPER_COMBAT(Type.POTION, "S.Com", "S.C", ItemID._4DOSEDIVINECOMBAT, ItemID._3DOSEDIVINECOMBAT, ItemID._2DOSEDIVINECOMBAT, ItemID._1DOSEDIVINECOMBAT),
	DIVINE_RANGING(Type.POTION, "Range", "R", ItemID._4DOSEDIVINERANGE, ItemID._3DOSEDIVINERANGE, ItemID._2DOSEDIVINERANGE, ItemID._1DOSEDIVINERANGE),
	DIVINE_MAGIC(Type.POTION, "Magic", "M", ItemID._4DOSEDIVINEMAGIC, ItemID._3DOSEDIVINEMAGIC, ItemID._2DOSEDIVINEMAGIC, ItemID._1DOSEDIVINEMAGIC),
	DIVINE_BASTION(Type.POTION, "Bastion", "B", ItemID._4DOSEDIVINEBASTION, ItemID._3DOSEDIVINEBASTION, ItemID._2DOSEDIVINEBASTION, ItemID._1DOSEDIVINEBASTION),
	DIVINE_BATTLEMAGE(Type.POTION, "BatMage", "B.M", ItemID._4DOSEDIVINEBATTLEMAGE, ItemID._3DOSEDIVINEBATTLEMAGE, ItemID._2DOSEDIVINEBATTLEMAGE, ItemID._1DOSEDIVINEBATTLEMAGE),

	RESTORE(Type.POTION, "Restore", "Re", ItemID._4DOSESTATRESTORE, ItemID._3DOSESTATRESTORE, ItemID._2DOSESTATRESTORE, ItemID._1DOSESTATRESTORE),
	RESTORE_MIX(Type.POTION, "Restore", "Re", ItemID.BRUTAL_1DOSESTATRESTORE, ItemID.BRUTAL_2DOSESTATRESTORE),
	GUTHIX_BALANCE(Type.POTION, "GuthBal", "G.B.", ItemID.BURGH_GUTHIX_BALANCE_4, ItemID.BURGH_GUTHIX_BALANCE_3, ItemID.BURGH_GUTHIX_BALANCE_2, ItemID.BURGH_GUTHIX_BALANCE_1),
	SUPER_RESTORE(Type.POTION, "S.Rest", "S.Re", ItemID._4DOSE2RESTORE, ItemID._3DOSE2RESTORE, ItemID._2DOSE2RESTORE, ItemID._1DOSE2RESTORE),
	SUPER_RESTORE_MIX(Type.POTION, "S.Rest", "S.Re", ItemID.BRUTAL_1DOSE2RESTORE, ItemID.BRUTAL_2DOSE2RESTORE),
	PRAYER(Type.POTION, "Prayer", "P", ItemID._4DOSEPRAYERRESTORE, ItemID._3DOSEPRAYERRESTORE, ItemID._2DOSEPRAYERRESTORE, ItemID._1DOSEPRAYERRESTORE),
	PRAYER_MIX(Type.POTION, "Prayer", "P", ItemID.BRUTAL_1DOSEPRAYERRESTORE, ItemID.BRUTAL_2DOSEPRAYERRESTORE),
	ENERGY(Type.POTION, "Energy", "En", ItemID._4DOSE1ENERGY, ItemID._3DOSE1ENERGY, ItemID._2DOSE1ENERGY, ItemID._1DOSE1ENERGY),
	ENERGY_MIX(Type.POTION, "Energy", "En", ItemID.BRUTAL_1DOSE1ENERGY, ItemID.BRUTAL_2DOSE1ENERGY),
	SUPER_ENERGY(Type.POTION, "S.Energ", "S.En", ItemID._4DOSE2ENERGY, ItemID._3DOSE2ENERGY, ItemID._2DOSE2ENERGY, ItemID._1DOSE2ENERGY),
	SUPER_ENERGY_MIX(Type.POTION, "S.Energ", "S.En", ItemID.BRUTAL_1DOSE2ENERGY, ItemID.BRUTAL_2DOSE2ENERGY),
	STAMINA(Type.POTION, "Stamina", "St", ItemID._4DOSESTAMINA, ItemID._3DOSESTAMINA, ItemID._2DOSESTAMINA, ItemID._1DOSESTAMINA),
	STAMINA_MIX(Type.POTION, "Stamina", "Sta", ItemID.BRUTAL_1DOSESTAMINA, ItemID.BRUTAL_2DOSESTAMINA),
	OVERLOAD(Type.POTION, "Overloa", "OL", ItemID.NZONE4DOSEOVERLOADPOTION, ItemID.NZONE3DOSEOVERLOADPOTION, ItemID.NZONE2DOSEOVERLOADPOTION, ItemID.NZONE1DOSEOVERLOADPOTION),
	ABSORPTION(Type.POTION, "Absorb", "Ab", ItemID.NZONE4DOSEABSORPTIONPOTION, ItemID.NZONE3DOSEABSORPTIONPOTION, ItemID.NZONE2DOSEABSORPTIONPOTION, ItemID.NZONE1DOSEABSORPTIONPOTION),
	BLIGHTED_SUPER_RESTORE(Type.POTION, "BS.Rest", "BS.Re", ItemID.BLIGHTED_4DOSE2RESTORE, ItemID.BLIGHTED_3DOSE2RESTORE, ItemID.BLIGHTED_2DOSE2RESTORE, ItemID.BLIGHTED_1DOSE2RESTORE),
	PRAYER_REGENERATION(Type.POTION, "Pray R.", "P.R.", ItemID._4DOSE1PRAYER_REGENERATION, ItemID._3DOSE1PRAYER_REGENERATION, ItemID._2DOSE1PRAYER_REGENERATION, ItemID._1DOSE1PRAYER_REGENERATION),
	GOADING(Type.POTION, "Goading", "G", ItemID._4DOSEGOADING, ItemID._3DOSEGOADING, ItemID._2DOSEGOADING, ItemID._1DOSEGOADING),
	ZAMORAK_BREW(Type.POTION, "ZammyBr", "Za", ItemID._4DOSEPOTIONOFZAMORAK, ItemID._3DOSEPOTIONOFZAMORAK, ItemID._2DOSEPOTIONOFZAMORAK, ItemID._1DOSEPOTIONOFZAMORAK),
	ZAMORAK_MIX(Type.POTION, "ZammyBr", "Z", ItemID.BRUTAL_1DOSEPOTIONOFZAMORAK, ItemID.BRUTAL_2DOSEPOTIONOFZAMORAK),
	SARADOMIN_BREW(Type.POTION, "SaraBr", "Sa", ItemID._4DOSEPOTIONOFSARADOMIN, ItemID._3DOSEPOTIONOFSARADOMIN, ItemID._2DOSEPOTIONOFSARADOMIN, ItemID._1DOSEPOTIONOFSARADOMIN),
	ANCIENT_BREW(Type.POTION, "AncBr", "A.Br", ItemID._4DOSEANCIENTBREW, ItemID._3DOSEANCIENTBREW, ItemID._2DOSEANCIENTBREW, ItemID._1DOSEANCIENTBREW),
	ANCIENT_MIX(Type.POTION, "AncBr", "Anc", ItemID.BRUTAL_1DOSEANCIENTBREW, ItemID.BRUTAL_2DOSEANCIENTBREW),
	FORGOTTEN_BREW(Type.POTION, "ForgBr", "F.Br", ItemID._4DOSEFORGOTTENBREW, ItemID._3DOSEFORGOTTENBREW, ItemID._2DOSEFORGOTTENBREW, ItemID._1DOSEFORGOTTENBREW),

	ANTIPOISON(Type.POTION, "AntiP", "AP", ItemID._4DOSEANTIPOISON, ItemID._3DOSEANTIPOISON, ItemID._2DOSEANTIPOISON, ItemID._1DOSEANTIPOISON),
	ANTIPOISON_MIX(Type.POTION, "AntiP", "AP", ItemID.BRUTAL_1DOSEANTIPOISON, ItemID.BRUTAL_2DOSEANTIPOISON),
	SUPERANTIPOISON(Type.POTION, "S.AntiP", "S.AP", ItemID._4DOSE2ANTIPOISON, ItemID._3DOSE2ANTIPOISON, ItemID._2DOSE2ANTIPOISON, ItemID._1DOSE2ANTIPOISON),
	ANTIPOISON_SUPERMIX(Type.POTION, "S.AntiP", "S.AP", ItemID.BRUTAL_1DOSE2ANTIPOISON, ItemID.BRUTAL_2DOSE2ANTIPOISON),
	ANTIDOTE_P(Type.POTION, "Antid+", "A+", ItemID.ANTIDOTE_4, ItemID.ANTIDOTE_3, ItemID.ANTIDOTE_2, ItemID.ANTIDOTE_1),
	ANTIDOTE_P_MIX(Type.POTION, "Antid+", "A+", ItemID.BRUTAL_ANTIDOTE_1, ItemID.BRUTAL_ANTIDOTE_2),
	ANTIDOTE_PP(Type.POTION, "Antid++", "A++", ItemID.ANTIDOTE__4, ItemID.ANTIDOTE__3, ItemID.ANTIDOTE__2, ItemID.ANTIDOTE__1),
	ANTIVENOM(Type.POTION, "Anti-V", "AV", ItemID.ANTIVENOM4, ItemID.ANTIVENOM3, ItemID.ANTIVENOM2, ItemID.ANTIVENOM1),
	ANTIVENOM_P(Type.POTION, "Anti-V+", "AV+", ItemID.ANTIVENOM_4, ItemID.ANTIVENOM_3, ItemID.ANTIVENOM_2, ItemID.ANTIVENOM_1),
	EXT_ANTIVENOM_P(Type.POTION, "E.AntiV+", "EAV+", ItemID.EXTENDED_ANTIVENOM_4, ItemID.EXTENDED_ANTIVENOM_3, ItemID.EXTENDED_ANTIVENOM_2, ItemID.EXTENDED_ANTIVENOM_1),

	RELICYMS_BALM(Type.POTION, "Relicym", "R.B", ItemID.RELICYMS_BALM4, ItemID.RELICYMS_BALM3, ItemID.RELICYMS_BALM2, ItemID.RELICYMS_BALM1),
	RELICYMS_MIX(Type.POTION, "Relicym", "R.B", ItemID.BRUTAL_RELICYMS_BALM1, ItemID.BRUTAL_RELICYMS_BALM2),
	SANFEW_SERUM(Type.POTION, "Sanfew", "Sf", ItemID.SANFEW_SALVE_4_DOSE, ItemID.SANFEW_SALVE_3_DOSE, ItemID.SANFEW_SALVE_2_DOSE, ItemID.SANFEW_SALVE_1_DOSE),
	ANTIFIRE(Type.POTION, "Antif", "Af", ItemID._4DOSE1ANTIDRAGON, ItemID._3DOSE1ANTIDRAGON, ItemID._2DOSE1ANTIDRAGON, ItemID._1DOSE1ANTIDRAGON),
	ANTIFIRE_MIX(Type.POTION, "Antif", "Af", ItemID.BRUTAL_1DOSE1ANTIDRAGON, ItemID.BRUTAL_2DOSE1ANTIDRAGON),
	EXTENDED_ANTIFIRE(Type.POTION, "E.Antif", "E.Af", ItemID._4DOSE2ANTIDRAGON, ItemID._3DOSE2ANTIDRAGON, ItemID._2DOSE2ANTIDRAGON, ItemID._1DOSE2ANTIDRAGON),
	EXTENDED_ANTIFIRE_MIX(Type.POTION, "E.Antif", "E.Af", ItemID.BRUTAL_1DOSE2ANTIDRAGON, ItemID.BRUTAL_2DOSE2ANTIDRAGON),
	SUPER_ANTIFIRE(Type.POTION, "S.Antif", "S.Af", ItemID._4DOSE3ANTIDRAGON, ItemID._3DOSE3ANTIDRAGON, ItemID._2DOSE3ANTIDRAGON, ItemID._1DOSE3ANTIDRAGON),
	SUPER_ANTIFIRE_MIX(Type.POTION, "S.Antif", "S.Af", ItemID.BRUTAL_1DOSE3ANTIDRAGON, ItemID.BRUTAL_2DOSE3ANTIDRAGON),
	EXTENDED_SUPER_ANTIFIRE(Type.POTION, "ES.Antif", "ES.Af", ItemID._4DOSE4ANTIDRAGON, ItemID._3DOSE4ANTIDRAGON, ItemID._2DOSE4ANTIDRAGON, ItemID._1DOSE4ANTIDRAGON),
	EXTENDED_SUPER_ANTIFIRE_MIX(Type.POTION, "ES.Antif", "ES.Af", ItemID.BRUTAL_1DOSE4ANTIDRAGON, ItemID.BRUTAL_2DOSE4ANTIDRAGON),

	SERUM_207(Type.POTION, "Ser207", "S7", ItemID.MORT_SERUM4, ItemID.MORT_SERUM3, ItemID.MORT_SERUM2, ItemID.MORT_SERUM1),
	SERUM_208(Type.POTION, "Ser208", "S8", ItemID.MORT_SERUM_PERM4, ItemID.MORT_SERUM_PERM3, ItemID.MORT_SERUM_PERM2, ItemID.MORT_SERUM_PERM1),
	SACRED_OIL(Type.POTION, "SaOil", "SO", ItemID.SACRED_OIL4, ItemID.SACRED_OIL3, ItemID.SACRED_OIL2, ItemID.SACRED_OIL1),
	OLIVE_OIL(Type.POTION, "Olive", "Oli", ItemID.OLIVEOIL4, ItemID.OLIVEOIL3, ItemID.OLIVEOIL2, ItemID.OLIVEOIL1),
	COMPOST_POTION(Type.POTION, "Compost", "COM", ItemID.SUPERCOMPOST_POTION_4, ItemID.SUPERCOMPOST_POTION_3, ItemID.SUPERCOMPOST_POTION_2, ItemID.SUPERCOMPOST_POTION_1),

	AGILITY(Type.POTION, "Agility", "Ag", ItemID._4DOSE1AGILITY, ItemID._3DOSE1AGILITY, ItemID._2DOSE1AGILITY, ItemID._1DOSE1AGILITY),
	AGILITY_MIX(Type.POTION, "Agility", "Ag", ItemID.BRUTAL_1DOSE1AGILITY, ItemID.BRUTAL_2DOSE1AGILITY),
	FISHING(Type.POTION, "Fishing", "Fi", ItemID._4DOSEFISHERSPOTION, ItemID._3DOSEFISHERSPOTION, ItemID._2DOSEFISHERSPOTION, ItemID._1DOSEFISHERSPOTION),
	FISHING_MIX(Type.POTION, "Fishing", "Fi", ItemID.BRUTAL_1DOSEFISHERSPOTION, ItemID.BRUTAL_2DOSEFISHERSPOTION),
	HUNTER(Type.POTION, "Hunter", "Hu", ItemID._4DOSEHUNTING, ItemID._3DOSEHUNTING, ItemID._2DOSEHUNTING, ItemID._1DOSEHUNTING),
	HUNTING_MIX(Type.POTION, "Hunter", "Hu", ItemID.BRUTAL_1DOSE1HUNTING, ItemID.BRUTAL_2DOSE1HUNTING),

	GOBLIN(Type.POTION, "Goblin", "G", ItemID.LOTG_4DOSEGOBLIN, ItemID.LOTG_3DOSEGOBLIN, ItemID.LOTG_2DOSEGOBLIN, ItemID.LOTG_1DOSEGOBLIN),
	MAGIC_ESS(Type.POTION, "MagEss", "M.E", ItemID._4DOSEMAGICESS, ItemID._3DOSEMAGICESS, ItemID._2DOSEMAGICESS, ItemID._1DOSEMAGICESS),
	MAGIC_ESSENCE_MIX(Type.POTION, "MagEss", "M.E", ItemID.BRUTAL_1DOSEMAGICESS, ItemID.BRUTAL_2DOSEMAGICESS),
	REJUVENATION(Type.POTION, "Rejuv", "Rj", ItemID.WINT_POTION4, ItemID.WINT_POTION3, ItemID.WINT_POTION2, ItemID.WINT_POTION1),

	ELDER(Type.POTION, "Elder", "El", ItemID.RAIDS_VIAL_ELDER_WEAK_4, ItemID.RAIDS_VIAL_ELDER_WEAK_3, ItemID.RAIDS_VIAL_ELDER_WEAK_2, ItemID.RAIDS_VIAL_ELDER_WEAK_1, ItemID.RAIDS_VIAL_ELDER_4, ItemID.RAIDS_VIAL_ELDER_3, ItemID.RAIDS_VIAL_ELDER_2, ItemID.RAIDS_VIAL_ELDER_1, ItemID.RAIDS_VIAL_ELDER_STRONG_4, ItemID.RAIDS_VIAL_ELDER_STRONG_3, ItemID.RAIDS_VIAL_ELDER_STRONG_2, ItemID.RAIDS_VIAL_ELDER_STRONG_1),
	TWISTED(Type.POTION, "Twisted", "Tw", ItemID.RAIDS_VIAL_TWISTED_WEAK_4, ItemID.RAIDS_VIAL_TWISTED_WEAK_3, ItemID.RAIDS_VIAL_TWISTED_WEAK_2, ItemID.RAIDS_VIAL_TWISTED_WEAK_1, ItemID.RAIDS_VIAL_TWISTED_4, ItemID.RAIDS_VIAL_TWISTED_3, ItemID.RAIDS_VIAL_TWISTED_2, ItemID.RAIDS_VIAL_TWISTED_1, ItemID.RAIDS_VIAL_TWISTED_STRONG_4, ItemID.RAIDS_VIAL_TWISTED_STRONG_3, ItemID.RAIDS_VIAL_TWISTED_STRONG_2, ItemID.RAIDS_VIAL_TWISTED_STRONG_1),
	KODAI(Type.POTION, "Kodai", "Ko", ItemID.RAIDS_VIAL_KODAI_WEAK_4, ItemID.RAIDS_VIAL_KODAI_WEAK_3, ItemID.RAIDS_VIAL_KODAI_WEAK_2, ItemID.RAIDS_VIAL_KODAI_WEAK_1, ItemID.RAIDS_VIAL_KODAI_4, ItemID.RAIDS_VIAL_KODAI_3, ItemID.RAIDS_VIAL_KODAI_2, ItemID.RAIDS_VIAL_KODAI_1, ItemID.RAIDS_VIAL_KODAI_STRONG_4, ItemID.RAIDS_VIAL_KODAI_STRONG_3, ItemID.RAIDS_VIAL_KODAI_STRONG_2, ItemID.RAIDS_VIAL_KODAI_STRONG_1),
	ANTIPOISON_COX(Type.POTION, "AntiP", "AP", ItemID.RAIDS_VIAL_ANTIPOISON_WEAK_4, ItemID.RAIDS_VIAL_ANTIPOISON_WEAK_3, ItemID.RAIDS_VIAL_ANTIPOISON_WEAK_2, ItemID.RAIDS_VIAL_ANTIPOISON_WEAK_1, ItemID.RAIDS_VIAL_ANTIPOISON_4, ItemID.RAIDS_VIAL_ANTIPOISON_3, ItemID.RAIDS_VIAL_ANTIPOISON_2, ItemID.RAIDS_VIAL_ANTIPOISON_1, ItemID.RAIDS_VIAL_ANTIPOISON_STRONG_4, ItemID.RAIDS_VIAL_ANTIPOISON_STRONG_3, ItemID.RAIDS_VIAL_ANTIPOISON_STRONG_2, ItemID.RAIDS_VIAL_ANTIPOISON_STRONG_1),
	REVITALISATION(Type.POTION, "Revit", "Re", ItemID.RAIDS_VIAL_REVITALISATION_WEAK_4, ItemID.RAIDS_VIAL_REVITALISATION_WEAK_3, ItemID.RAIDS_VIAL_REVITALISATION_WEAK_2, ItemID.RAIDS_VIAL_REVITALISATION_WEAK_1, ItemID.RAIDS_VIAL_REVITALISATION_4, ItemID.RAIDS_VIAL_REVITALISATION_3, ItemID.RAIDS_VIAL_REVITALISATION_2, ItemID.RAIDS_VIAL_REVITALISATION_1, ItemID.RAIDS_VIAL_REVITALISATION_STRONG_4, ItemID.RAIDS_VIAL_REVITALISATION_STRONG_3, ItemID.RAIDS_VIAL_REVITALISATION_STRONG_2, ItemID.RAIDS_VIAL_REVITALISATION_STRONG_1),
	PRAYER_ENHANCE(Type.POTION, "P.Enh", "Enh", ItemID.RAIDS_VIAL_PRAYER_WEAK_4, ItemID.RAIDS_VIAL_PRAYER_WEAK_3, ItemID.RAIDS_VIAL_PRAYER_WEAK_2, ItemID.RAIDS_VIAL_PRAYER_WEAK_1, ItemID.RAIDS_VIAL_PRAYER_4, ItemID.RAIDS_VIAL_PRAYER_3, ItemID.RAIDS_VIAL_PRAYER_2, ItemID.RAIDS_VIAL_PRAYER_1, ItemID.RAIDS_VIAL_PRAYER_STRONG_4, ItemID.RAIDS_VIAL_PRAYER_STRONG_3, ItemID.RAIDS_VIAL_PRAYER_STRONG_2, ItemID.RAIDS_VIAL_PRAYER_STRONG_1),
	XERICS_AID(Type.POTION, "X.Aid", "Aid", ItemID.RAIDS_VIAL_XERICAID_WEAK_4, ItemID.RAIDS_VIAL_XERICAID_WEAK_3, ItemID.RAIDS_VIAL_XERICAID_WEAK_2, ItemID.RAIDS_VIAL_XERICAID_WEAK_1, ItemID.RAIDS_VIAL_XERICAID_4, ItemID.RAIDS_VIAL_XERICAID_3, ItemID.RAIDS_VIAL_XERICAID_2, ItemID.RAIDS_VIAL_XERICAID_1, ItemID.RAIDS_VIAL_XERICAID_STRONG_4, ItemID.RAIDS_VIAL_XERICAID_STRONG_3, ItemID.RAIDS_VIAL_XERICAID_STRONG_2, ItemID.RAIDS_VIAL_XERICAID_STRONG_1),
	OVERLOAD_COX(Type.POTION, "Overloa", "Ovl", ItemID.RAIDS_VIAL_OVERLOAD_WEAK_4, ItemID.RAIDS_VIAL_OVERLOAD_WEAK_3, ItemID.RAIDS_VIAL_OVERLOAD_WEAK_2, ItemID.RAIDS_VIAL_OVERLOAD_WEAK_1, ItemID.RAIDS_VIAL_OVERLOAD_4, ItemID.RAIDS_VIAL_OVERLOAD_3, ItemID.RAIDS_VIAL_OVERLOAD_2, ItemID.RAIDS_VIAL_OVERLOAD_1, ItemID.RAIDS_VIAL_OVERLOAD_STRONG_4, ItemID.RAIDS_VIAL_OVERLOAD_STRONG_3, ItemID.RAIDS_VIAL_OVERLOAD_STRONG_2, ItemID.RAIDS_VIAL_OVERLOAD_STRONG_1),

	NECTAR(Type.POTION, "Nectar", "N", ItemID.TOA_SUPPLY_HEAL_4, ItemID.TOA_SUPPLY_HEAL_3, ItemID.TOA_SUPPLY_HEAL_2, ItemID.TOA_SUPPLY_HEAL_1),
	AMBROSIA(Type.POTION, "Ambros", "A", ItemID.TOA_SUPPLY_PANICHEAL_2, ItemID.TOA_SUPPLY_PANICHEAL_1),
	LIQUID_ADRENALINE(Type.POTION, "L.Adrn", "L.A", ItemID.TOA_SUPPLY_ENERGY_2, ItemID.TOA_SUPPLY_ENERGY_1),
	TEARS_OF_ELIDINIS(Type.POTION, "Tears", "ToE", ItemID.TOA_SUPPLY_PRAYER_4, ItemID.TOA_SUPPLY_PRAYER_3, ItemID.TOA_SUPPLY_PRAYER_2, ItemID.TOA_SUPPLY_PRAYER_1),
	MENAPHITE_REMEDY(Type.POTION, "MenRem", "M.R", ItemID._4DOSESTATRENEWAL, ItemID._3DOSESTATRENEWAL, ItemID._2DOSESTATRENEWAL, ItemID._1DOSESTATRENEWAL),
	MOONLIGHT_POTION(Type.POTION, "Moon", "M", ItemID._1DOSEMOONLIGHTPOTION, ItemID._2DOSEMOONLIGHTPOTION, ItemID._3DOSEMOONLIGHTPOTION, ItemID._4DOSEMOONLIGHTPOTION),

	// Unfinished Potions
	GUAM_POTION(Type.POTION, "Guam", "G", ItemID.GUAMVIAL),
	MARRENTILL_POTION(Type.POTION, "Marren", "M", ItemID.MARRENTILLVIAL),
	TARROMIN_POTION(Type.POTION, "Tarro", "TAR", ItemID.TARROMINVIAL),
	HARRALANDER_POTION(Type.POTION, "Harra", "HAR", ItemID.HARRALANDERVIAL),
	RANARR_POTION(Type.POTION, "Ranarr", "R", ItemID.RANARRVIAL),
	TOADFLAX_POTION(Type.POTION, "Toad", "TOA", ItemID.TOADFLAXVIAL),
	IRIT_POTION(Type.POTION, "Irit", "I", ItemID.IRITVIAL),
	AVANTOE_POTION(Type.POTION, "Avan", "A", ItemID.AVANTOEVIAL),
	KWUARM_POTION(Type.POTION, "Kwuarm", "K", ItemID.KWUARMVIAL),
	SNAPDRAGON_POTION(Type.POTION, "Snap", "S", ItemID.SNAPDRAGONVIAL),
	CADANTINE_POTION(Type.POTION, "Cadan", "C", ItemID.CADANTINEVIAL),
	LANTADYME_POTION(Type.POTION, "Lanta", "L", ItemID.LANTADYMEVIAL),
	DWARF_WEED_POTION(Type.POTION, "Dwarf", "D", ItemID.DWARFWEEDVIAL),
	TORSTOL_POTION(Type.POTION, "Torstol", "TOR", ItemID.TORSTOLVIAL),
	HUASCA_POTION(Type.POTION, "Huasca", "HUA", ItemID.HUASCAVIAL),

	// Moth & butterfly jars
	BLACK_WARLOCK(Type.BUTTERFLY_MOTH_JAR, "B.War", "BW", ItemID.BUTTERFLY_JAR_WARLOCK),
	MOONLIGHT_MOTH(Type.BUTTERFLY_MOTH_JAR, "Moon", "M", ItemID.BUTTERFLY_JAR_MOONMOTH),
	RUBY_HARVEST(Type.BUTTERFLY_MOTH_JAR, "Ruby.H", "RH", ItemID.BUTTERFLY_JAR_RUBY),
	SAPPHIRE_GLACIALIS(Type.BUTTERFLY_MOTH_JAR, "Saph.G", "SG", ItemID.BUTTERFLY_JAR_GLACIALIS),
	SNOWY_KNIGHT(Type.BUTTERFLY_MOTH_JAR, "Snow.K", "SK", ItemID.BUTTERFLY_JAR_SNOWY),
	SUNLIGHT_MOTH(Type.BUTTERFLY_MOTH_JAR, "Sun", "S", ItemID.BUTTERFLY_JAR_SUNMOTH),
	BLACK_WARLOCK_MIX(Type.BUTTERFLY_MOTH_JAR, "B.War x", "BW x", ItemID.HUNTER_MIX_WARLOCK_1DOSE, ItemID.HUNTER_MIX_WARLOCK_2DOSE),
	MOONLIGHT_MOTH_MIX(Type.BUTTERFLY_MOTH_JAR, "Moon x", "M x", ItemID.HUNTER_MIX_MOONMOTH_1DOSE, ItemID.HUNTER_MIX_MOONMOTH_2DOSE),
	RUBY_HARVEST_MIX(Type.BUTTERFLY_MOTH_JAR, "Ruby.H x", "RH x", ItemID.HUNTER_MIX_RUBY_1DOSE, ItemID.HUNTER_MIX_RUBY_2DOSE),
	SAPPHIRE_GLACIALIS_MIX(Type.BUTTERFLY_MOTH_JAR, "Saph.G x", "SG x", ItemID.HUNTER_MIX_GLACIALIS_1DOSE, ItemID.HUNTER_MIX_GLACIALIS_2DOSE),
	SNOWY_KNIGHT_MIX(Type.BUTTERFLY_MOTH_JAR, "Snow.K x", "SK x", ItemID.HUNTER_MIX_SNOWY_1DOSE, ItemID.HUNTER_MIX_SNOWY_2DOSE),
	SUNLIGHT_MOTH_MIX(Type.BUTTERFLY_MOTH_JAR, "Sun x", "S x", ItemID.HUNTER_MIX_SUNMOTH_1DOSE, ItemID.HUNTER_MIX_SUNMOTH_2DOSE),

	// Impling jars
	BABY_IMPLING(Type.IMPLING_JAR, "Baby", "B", ItemID.II_CAPTURED_IMPLING_1),
	YOUNG_IMPLING(Type.IMPLING_JAR, "Young", "Y", ItemID.II_CAPTURED_IMPLING_2),
	GOURMET_IMPLING(Type.IMPLING_JAR, "Gourmet", "G", ItemID.II_CAPTURED_IMPLING_3),
	EARTH_IMPLING(Type.IMPLING_JAR, "Earth", "EAR", ItemID.II_CAPTURED_IMPLING_4),
	ESSENCE_IMPLING(Type.IMPLING_JAR, "Essen", "ESS", ItemID.II_CAPTURED_IMPLING_5),
	ECLECTIC_IMPLING(Type.IMPLING_JAR, "Eclect", "ECL", ItemID.II_CAPTURED_IMPLING_6),
	NATURE_IMPLING(Type.IMPLING_JAR, "Nature", "NAT", ItemID.II_CAPTURED_IMPLING_7),
	MAGPIE_IMPLING(Type.IMPLING_JAR, "Magpie", "M", ItemID.II_CAPTURED_IMPLING_8),
	NINJA_IMPLING(Type.IMPLING_JAR, "Ninja", "NIN", ItemID.II_CAPTURED_IMPLING_9),
	CRYSTAL_IMPLING(Type.IMPLING_JAR, "Crystal", "C", ItemID.II_CAPTURED_IMPLING_12),
	DRAGON_IMPLING(Type.IMPLING_JAR, "Dragon", "D", ItemID.II_CAPTURED_IMPLING_10),
	LUCKY_IMPLING(Type.IMPLING_JAR, "Lucky", "L", ItemID.II_CAPTURED_IMPLING_11),

	// Tablets
	VARROCK_TELEPORT(Type.TABLET, "Varro", "VAR", ItemID.POH_TABLET_VARROCKTELEPORT),
	LUMBRIDGE_TELEPORT(Type.TABLET, "Lumbr", "LUM", ItemID.POH_TABLET_LUMBRIDGETELEPORT),
	FALADOR_TELEPORT(Type.TABLET, "Fala", "FAL", ItemID.POH_TABLET_FALADORTELEPORT),
	CAMELOT_TELEPORT(Type.TABLET, "Cammy", "CAM", ItemID.POH_TABLET_CAMELOTTELEPORT),
	ARDOUGNE_TELEPORT(Type.TABLET, "Ardoug", "ARD", ItemID.POH_TABLET_ARDOUGNETELEPORT),
	WATCHTOWER_TELEPORT(Type.TABLET, "W.tow", "WT", ItemID.POH_TABLET_WATCHTOWERTELEPORT),
	TELEPORT_TO_HOUSE(Type.TABLET, "House", "POH", ItemID.POH_TABLET_TELEPORTTOHOUSE),
	KOUREND_CASTLE_TELEPORT(Type.TABLET, "K.Cast", "KOU", ItemID.POH_TABLET_KOURENDTELEPORT),
	CIVITAS_ILLA_FORTIS_TELEPORT(Type.TABLET, "Civitas", "CIV", ItemID.POH_TABLET_FORTISTELEPORT),

	ENCHANT_SAPPHIRE_OR_OPAL(Type.TABLET, "E.Saph", "E SO", ItemID.POH_TABLET_ENCHANTSAPPHIRE),
	ENCHANT_EMERALD_OR_JADE(Type.TABLET, "E.Emer", "E EJ", ItemID.POH_TABLET_ENCHANTEMERALD),
	ENCHANT_RUBY_OR_TOPAZ(Type.TABLET, "E.Ruby", "E RT", ItemID.POH_TABLET_ENCHANTRUBY),
	ENCHANT_DIAMOND(Type.TABLET, "E.Diam", "E DIA", ItemID.POH_TABLET_ENCHANTDIAMOND),
	ENCHANT_DRAGONSTONE(Type.TABLET, "E.Dstn", "E DS", ItemID.POH_TABLET_ENCHANTDRAGONSTONE),
	ENCHANT_ONYX(Type.TABLET, "E.Onyx", "E ONX", ItemID.POH_TABLET_ENCHANTONYX),

	TELEKINETIC_GRAB(Type.TABLET, "T.grab", "T.GRB", ItemID.POH_TABLET_TELEGRAB),
	BONES_TO_PEACHES(Type.TABLET, "Peach", "BtP", ItemID.POH_TABLET_BONESTOPEACHES),
	BONES_TO_BANANAS(Type.TABLET, "Banana", "BtB", ItemID.POH_TABLET_BONESTOBANANAS),

	RIMMINGTON_TELEPORT(Type.TABLET, "Rimmi", "RIM", ItemID.NZONE_TELETAB_RIMMINGTON),
	TAVERLEY_TELEPORT(Type.TABLET, "Taver", "TAV", ItemID.NZONE_TELETAB_TAVERLEY),
	POLLNIVNEACH_TELEPORT(Type.TABLET, "Pollnv", "POL", ItemID.NZONE_TELETAB_POLLNIVNEACH),
	RELLEKKA_TELEPORT(Type.TABLET, "Rell", "REL", ItemID.NZONE_TELETAB_RELLEKKA),
	BRIMHAVEN_TELEPORT(Type.TABLET, "Brimh", "BRIM", ItemID.NZONE_TELETAB_BRIMHAVEN),
	YANILLE_TELEPORT(Type.TABLET, "Yanille", "YAN", ItemID.NZONE_TELETAB_YANILLE),
	TROLLHEIM_TELEPORT(Type.TABLET, "Trollh", "T.HM", ItemID.NZONE_TELETAB_TROLLHEIM),
	PRIFDDINAS_TELEPORT(Type.TABLET, "Prifd", "PRIF", ItemID.NZONE_TELETAB_PRIFDDINAS),
	HOSIDIUS_TELEPORT(Type.TABLET, "Hosid", "HOS", ItemID.NZONE_TELETAB_KOUREND),

	ANNAKARL_TELEPORT(Type.TABLET, "Annak", "GDZ", ItemID.TABLET_ANNAKARL),
	CARRALLANGER_TELEPORT(Type.TABLET, "Carra", "CAR", ItemID.TABLET_CARRALLANGAR),
	DAREEYAK_TELEPORT(Type.TABLET, "Dareey", "DAR", ItemID.TABLET_DAREEYAK),
	GHORROCK_TELEPORT(Type.TABLET, "Ghorr", "GHRK", ItemID.TABLET_GHORROCK),
	KHARYRLL_TELEPORT(Type.TABLET, "Khary", "KHRL", ItemID.TABLET_KHARYLL),
	LASSAR_TELEPORT(Type.TABLET, "Lass", "LSR", ItemID.TABLET_LASSAR),
	PADDEWWA_TELEPORT(Type.TABLET, "Paddew", "PDW", ItemID.TABLET_PADDEWA),
	SENNTISTEN_TELEPORT(Type.TABLET, "Sennt", "SNT", ItemID.TABLET_SENNTISTEN),

	ARCEUUS_LIBRARY_TELEPORT(Type.TABLET, "Arc.Lib", "A.LIB", ItemID.TELETAB_LUMBRIDGE),
	DRAYNOR_MANOR_TELEPORT(Type.TABLET, "D.Manor", "D.MNR", ItemID.TELETAB_DRAYNOR),
	MIND_ALTAR_TELEPORT(Type.TABLET, "M.Altar", "M.ALT", ItemID.TELETAB_MIND_ALTAR),
	SALVE_GRAVEYARD_TELEPORT(Type.TABLET, "S.Grave", "S.GRV", ItemID.TELETAB_SALVE),
	FENKENSTRAINS_CASTLE_TELEPORT(Type.TABLET, "Fenk", "FNK", ItemID.TELETAB_FENK),
	WEST_ARDOUGNE_TELEPORT(Type.TABLET, "W.Ardy", "W.ARD", ItemID.TELETAB_WESTARDY),
	HARMONY_ISLAND_TELEPORT(Type.TABLET, "H.Isle", "HRM", ItemID.TELETAB_HARMONY),
	CEMETERY_TELEPORT(Type.TABLET, "Cemet", "CEM", ItemID.TELETAB_CEMETERY),
	BARROWS_TELEPORT(Type.TABLET, "Barrow", "BAR", ItemID.TELETAB_BARROWS),
	APE_ATOLL_TELEPORT(Type.TABLET, "Atoll", "APE", ItemID.TELETAB_APE),
	BATTLEFRONT_TELEPORT(Type.TABLET, "B.Front", "BF", ItemID.TELETAB_BATTLEFRONT),

	MOONCLAN_TELEPORT(Type.TABLET, "Moon", "MOON", ItemID.LUNAR_TABLET_MOONCLAN_TELEPORT),
	OURANIA_TELEPORT(Type.TABLET, "Ourania", "ZMI", ItemID.LUNAR_TABLET_OURANIA_TELEPORT),
	WATERBIRTH_TELEPORT(Type.TABLET, "W.Birth", "WAT", ItemID.LUNAR_TABLET_WATERBIRTH_TELEPORT),
	BARBARIAN_TELEPORT(Type.TABLET, "Barb", "BARB", ItemID.LUNAR_TABLET_BARBARIAN_TELEPORT),
	KHAZARD_TELEPORT(Type.TABLET, "Khaz", "KHA", ItemID.LUNAR_TABLET_KHAZARD_TELEPORT),
	FISHING_GUILD_TELEPORT(Type.TABLET, "Fish G.", "FIS", ItemID.LUNAR_TABLET_FISHING_GUILD_TELEPORT),
	CATHERBY_TELEPORT(Type.TABLET, "Cathy", "CATH", ItemID.LUNAR_TABLET_CATHERBY_TELEPORT),
	ICE_PLATEAU(Type.TABLET, "Ice Pl.", "ICE", ItemID.LUNAR_TABLET_ICE_PLATEAU_TELEPORT),

	TARGET_TELEPORT(Type.TABLET, "Target", "TRG", ItemID.TABLET_TARGET),
	VOLCANIC_MINE_TELEPORT(Type.TABLET, "V.Mine", "VM", ItemID.FOSSIL_TABLET_VOLCANOTELEPORT),
	WILDERNESS_CRABS_TELEPORT(Type.TABLET, "W.Crab", "CRAB", ItemID.TABLET_WILDYCRABS),

	// Scrolls
	NARDAH_TELEPORT(Type.SCROLL, "Nardah", "NAR", ItemID.TELEPORTSCROLL_NARDAH),
	DIGSITE_TELEPORT(Type.SCROLL, "Digsite", "DIG", ItemID.TELEPORTSCROLL_DIGSITE),
	FELDIP_HILLS_TELEPORT(Type.SCROLL, "F.Hills", "F.H", ItemID.TELEPORTSCROLL_FELDIP),
	LUNAR_ISLE_TELEPORT(Type.SCROLL, "L.Isle", "L.I", ItemID.TELEPORTSCROLL_LUNARISLE),
	MORTTON_TELEPORT(Type.SCROLL, "Mort'ton", "MORT", ItemID.TELEPORTSCROLL_MORTTON),
	PEST_CONTROL_TELEPORT(Type.SCROLL, "P.Cont", "PEST", ItemID.TELEPORTSCROLL_PESTCONTROL),
	PISCATORIS_TELEPORT(Type.SCROLL, "Pisca", "PISC", ItemID.TELEPORTSCROLL_PISCATORIS),
	TAI_BWO_WANNAI_TELEPORT(Type.SCROLL, "TaiBwo", "TAI", ItemID.TELEPORTSCROLL_TAIBWO),
	IORWERTH_CAMP_TELEPORT(Type.SCROLL, "Iorwerth", "IOR", ItemID.TELEPORTSCROLL_ELF),
	MOS_LEHARMLESS_TELEPORT(Type.SCROLL, "M.LeHarm", "M.L", ItemID.TELEPORTSCROLL_MOSLES),
	LUMBERYARD_TELEPORT(Type.SCROLL, "Lumber", "LUMB", ItemID.TELEPORTSCROLL_LUMBERYARD),

	ZUL_ANDRA_TELEPORT(Type.SCROLL, "Zul-andra", "ZUL", ItemID.TELEPORTSCROLL_ZULANDRA),
	KEY_MASTER_TELEPORT(Type.SCROLL, "Key master", "KEY", ItemID.TELEPORTSCROLL_CERBERUS),
	REVENANT_CAVE_TELEPORT(Type.SCROLL, "Rev cave", "REV", ItemID.TELEPORTSCROLL_REVENANTS),
	WATSON_TELEPORT(Type.SCROLL, "Watson", "WATS", ItemID.TELEPORTSCROLL_WATSON),

	// Jewellery
	OPAL_JEWELLERY(Type.JEWELLERY, "Opal", "OP", ItemID.OPAL_RING, ItemID.OPAL_NECKLACE, ItemID.OPAL_BRACELET, ItemID.UNSTRUNG_OPAL_AMULET, ItemID.STRUNG_OPAL_AMULET),
	JADE_JEWELLERY(Type.JEWELLERY, "Jade", "J", ItemID.JADE_RING, ItemID.JADE_NECKLACE, ItemID.JADE_BRACELET, ItemID.UNSTRUNG_JADE_AMULET, ItemID.STRUNG_JADE_AMULET),
	TOPAZ_JEWELLERY(Type.JEWELLERY, "Topaz", "T", ItemID.TOPAZ_RING, ItemID.TOPAZ_NECKLACE, ItemID.TOPAZ_BRACELET, ItemID.UNSTRUNG_TOPAZ_AMULET, ItemID.STRUNG_TOPAZ_AMULET),
	GOLD_JEWELLERY(Type.JEWELLERY, "Gold", "GOL", ItemID.GOLD_RING, ItemID.GOLD_NECKLACE, ItemID.JEWL_GOLD_BRACELET_DUMMY, ItemID.UNSTRUNG_GOLD_AMULET, ItemID.STRUNG_GOLD_AMULET),
	SAPPHIRE_JEWELLERY(Type.JEWELLERY, "Sapphir", "S", ItemID.SAPPHIRE_RING, ItemID.SAPPHIRE_NECKLACE, ItemID.JEWL_SAPPHIRE_BRACELET_DUMMY, ItemID.UNSTRUNG_SAPPHIRE_AMULET, ItemID.STRUNG_SAPPHIRE_AMULET),
	EMERALD_JEWELLERY(Type.JEWELLERY, "Emerald", "E", ItemID.EMERALD_RING, ItemID.EMERALD_NECKLACE, ItemID.JEWL_EMERALD_BRACELET, ItemID.UNSTRUNG_EMERALD_AMULET, ItemID.STRUNG_EMERALD_AMULET),
	RUBY_JEWELLERY(Type.JEWELLERY, "Ruby", "R", ItemID.RUBY_RING, ItemID.RUBY_NECKLACE, ItemID.JEWL_RUBY_BRACELET, ItemID.UNSTRUNG_RUBY_AMULET, ItemID.STRUNG_RUBY_AMULET),
	DIAMOND_JEWELLERY(Type.JEWELLERY, "Diamon", "DI", ItemID.DIAMOND_RING, ItemID.DIAMOND_NECKLACE, ItemID.JEWL_DIAMOND_BRACELET, ItemID.UNSTRUNG_DIAMOND_AMULET, ItemID.STRUNG_DIAMOND_AMULET),
	DRAGONSTONE_JEWELLERY(Type.JEWELLERY, "Dragon", "DR", ItemID.DRAGONSTONE_RING, ItemID.DRAGONSTONE_NECKLACE, ItemID.JEWL_DRAGONSTONE_BRACELET, ItemID.UNSTRUNG_DRAGONSTONE_AMULET, ItemID.STRUNG_DRAGONSTONE_AMULET),
	ONYX_JEWELLERY(Type.JEWELLERY, "Onyx", "ON", ItemID.ONYX_RING, ItemID.ONYX_NECKLACE, ItemID.JEWL_ONYX_BRACELET, ItemID.UNSTRUNG_ONYX_AMULET, ItemID.STRUNG_ONYX_AMULET),
	ZENYTE_JEWELLERY(Type.JEWELLERY, "Zenyte", "Z", ItemID.ZENYTE_RING, ItemID.ZENYTE_NECKLACE, ItemID.ZENYTE_BRACELET_DUMMY, ItemID.UNSTRUNG_ZENYTE_AMULET, ItemID.ZENYTE_AMULET),

	// Enchanted Jewellery
	RING_OF_PURSUIT(Type.JEWELLERY_ENCHANTED, "Pursuit", "PUR", ItemID.RING_OF_PURSUIT),
	DODGY_NECKLACE(Type.JEWELLERY_ENCHANTED, "Dodgy", "DOD", ItemID.DODGY_NECKLACE),
	EXPEDITIOUS_BRACELET(Type.JEWELLERY_ENCHANTED, "Exped", "EXP", ItemID.EXPEDITIOUS_BRACELET),
	AMULET_OF_BOUNTY(Type.JEWELLERY_ENCHANTED, "Bounty", "BOU", ItemID.AMULET_OF_BOUNTY),

	RING_OF_RETURNING(Type.JEWELLERY_ENCHANTED, "Return", "RET", ItemID.RING_OF_RETURNING_1, ItemID.RING_OF_RETURNING_2, ItemID.RING_OF_RETURNING_3, ItemID.RING_OF_RETURNING_4, ItemID.RING_OF_RETURNING_5),
	NECKLACE_OF_PASSAGE(Type.JEWELLERY_ENCHANTED, "Passage", "PASS", ItemID.NECKLACE_OF_PASSAGE_1, ItemID.NECKLACE_OF_PASSAGE_2, ItemID.NECKLACE_OF_PASSAGE_3, ItemID.NECKLACE_OF_PASSAGE_4, ItemID.NECKLACE_OF_PASSAGE_5),
	FLAMTAER_BRACELET(Type.JEWELLERY_ENCHANTED, "Flamta", "FLA", ItemID.FLAMTAER_BRACELET),
	AMULET_OF_CHEMISTRY(Type.JEWELLERY_ENCHANTED, "Chem", "CH", ItemID.AMULET_OF_CHEMISTRY),

	EFARITAYS_AID(Type.JEWELLERY_ENCHANTED, "Efarit", "EFA", ItemID.VAMPYRE_RING),
	NECKLACE_OF_FAITH(Type.JEWELLERY_ENCHANTED, "Faith", "FAI", ItemID.NECKLACE_OF_FAITH),
	BRACELET_OF_SLAUGHTER(Type.JEWELLERY_ENCHANTED, "Slaught", "SLA", ItemID.BRACELET_OF_SLAUGHTER),
	BURNING_AMULET(Type.JEWELLERY_ENCHANTED, "Burning", "BUR", ItemID.BURNING_AMULET_1, ItemID.BURNING_AMULET_2, ItemID.BURNING_AMULET_3, ItemID.BURNING_AMULET_4, ItemID.BURNING_AMULET_5),

	RING_OF_RECOIL(Type.JEWELLERY_ENCHANTED, "Recoil", "REC", ItemID.RING_OF_RECOIL),
	GAMES_NECKLACE(Type.JEWELLERY_ENCHANTED, "Games", "GAME", ItemID.NECKLACE_OF_MINIGAMES_1, ItemID.NECKLACE_OF_MINIGAMES_2, ItemID.NECKLACE_OF_MINIGAMES_3, ItemID.NECKLACE_OF_MINIGAMES_4, ItemID.NECKLACE_OF_MINIGAMES_5, ItemID.NECKLACE_OF_MINIGAMES_6, ItemID.NECKLACE_OF_MINIGAMES_7, ItemID.NECKLACE_OF_MINIGAMES_8),
	BRACELET_OF_CLAY(Type.JEWELLERY_ENCHANTED, "Clay", "CLA", ItemID.JEWL_BRACELET_OF_CLAY),
	AMULET_OF_MAGIC(Type.JEWELLERY_ENCHANTED, "Magic", "MAG", ItemID.AMULET_OF_MAGIC),

	RING_OF_DUELING(Type.JEWELLERY_ENCHANTED, "Duel", "DU", ItemID.RING_OF_DUELING_1, ItemID.RING_OF_DUELING_2, ItemID.RING_OF_DUELING_3, ItemID.RING_OF_DUELING_4, ItemID.RING_OF_DUELING_5, ItemID.RING_OF_DUELING_6, ItemID.RING_OF_DUELING_7, ItemID.RING_OF_DUELING_8),
	BINDING_NECKLACE(Type.JEWELLERY_ENCHANTED, "Binding", "BIND", ItemID.MAGIC_EMERALD_NECKLACE),
	CASTLE_WARS_BRACELET(Type.JEWELLERY_ENCHANTED, "Castle", "CAS", ItemID.JEWL_CASTLEWARS_BRACELET, ItemID.JEWL_CASTLEWARS_BRACELET2, ItemID.JEWL_CASTLEWARS_BRACELET3),
	AMULET_OF_DEFENCE(Type.JEWELLERY_ENCHANTED, "Defence", "DEF", ItemID.AMULET_OF_DEFENCE),
	AMULET_OF_NATURE(Type.JEWELLERY_ENCHANTED, "Nature", "NAT", ItemID.NATURE_AMULET),

	RING_OF_FORGING(Type.JEWELLERY_ENCHANTED, "Forging", "FOR", ItemID.RING_OF_FORGING),
	DIGSITE_PENDANT(Type.JEWELLERY_ENCHANTED, "Digsite", "DIG", ItemID.NECKLACE_OF_DIGSITE_1, ItemID.NECKLACE_OF_DIGSITE_2, ItemID.NECKLACE_OF_DIGSITE_3, ItemID.NECKLACE_OF_DIGSITE_4, ItemID.NECKLACE_OF_DIGSITE_5),
	INOCULATION_BRACELET(Type.JEWELLERY_ENCHANTED, "Inocul", "INO", ItemID.JEWL_BRACELET_OF_INNOCULATION),
	AMULET_OF_STRENGTH(Type.JEWELLERY_ENCHANTED, "Strengt", "STR", ItemID.AMULET_OF_STRENGTH),

	RING_OF_LIFE(Type.JEWELLERY_ENCHANTED, "Life", "LI", ItemID.RING_OF_LIFE),
	PHOENIX_NECKLACE(Type.JEWELLERY_ENCHANTED, "Phoenix", "PHO", ItemID.JEWL_NECKLACE_OF_PHOENIX),
	ABYSSAL_BRACELET(Type.JEWELLERY_ENCHANTED, "Abyss", "ABY", ItemID.JEWL_RUNERUNNING_BRACELET_1, ItemID.JEWL_RUNERUNNING_BRACELET_2, ItemID.JEWL_RUNERUNNING_BRACELET_3, ItemID.JEWL_RUNERUNNING_BRACELET_4, ItemID.JEWL_RUNERUNNING_BRACELET_5),
	AMULET_OF_POWER(Type.JEWELLERY_ENCHANTED, "Power", "POW", ItemID.AMULET_OF_POWER),

	RING_OF_WEALTH(Type.JEWELLERY_ENCHANTED, "Wealth", "WE", ItemID.RING_OF_WEALTH, ItemID.RING_OF_WEALTH_1, ItemID.RING_OF_WEALTH_2, ItemID.RING_OF_WEALTH_3, ItemID.RING_OF_WEALTH_4, ItemID.RING_OF_WEALTH_5),
	SKILLS_NECKLACE(Type.JEWELLERY_ENCHANTED, "Skill", "SK", ItemID.JEWL_NECKLACE_OF_SKILLS, ItemID.JEWL_NECKLACE_OF_SKILLS_1, ItemID.JEWL_NECKLACE_OF_SKILLS_2, ItemID.JEWL_NECKLACE_OF_SKILLS_3, ItemID.JEWL_NECKLACE_OF_SKILLS_4, ItemID.JEWL_NECKLACE_OF_SKILLS_5, ItemID.JEWL_NECKLACE_OF_SKILLS_6),
	COMBAT_BRACELET(Type.JEWELLERY_ENCHANTED, "Combat", "COM", ItemID.JEWL_BRACELET_OF_COMBAT, ItemID.JEWL_BRACELET_OF_COMBAT_1, ItemID.JEWL_BRACELET_OF_COMBAT_2, ItemID.JEWL_BRACELET_OF_COMBAT_3, ItemID.JEWL_BRACELET_OF_COMBAT_4, ItemID.JEWL_BRACELET_OF_COMBAT_5, ItemID.JEWL_BRACELET_OF_COMBAT_6),
	AMULET_OF_GLORY(Type.JEWELLERY_ENCHANTED, "Glory", "GLO", ItemID.AMULET_OF_GLORY, ItemID.AMULET_OF_GLORY_1, ItemID.AMULET_OF_GLORY_2, ItemID.AMULET_OF_GLORY_3, ItemID.AMULET_OF_GLORY_4, ItemID.AMULET_OF_GLORY_5, ItemID.AMULET_OF_GLORY_6, ItemID.AMULET_OF_GLORY_INF),

	RING_OF_STONE(Type.JEWELLERY_ENCHANTED, "Stone", "STO", ItemID.ENCHANTED_ONYX_RING),
	BERSERKER_NECKLACE(Type.JEWELLERY_ENCHANTED, "Berserk", "BER", ItemID.JEWL_BESERKER_NECKLACE),
	REGEN_BRACELET(Type.JEWELLERY_ENCHANTED, "Regen", "REG", ItemID.JEWL_BRACELET_REGEN),
	AMULET_OF_FURY(Type.JEWELLERY_ENCHANTED, "Fury", "FU", ItemID.ENCHANTED_ONYX_AMULET),

	RING_OF_SUFFERING(Type.JEWELLERY_ENCHANTED, "Suffer", "SUF", ItemID.ZENYTE_RING_ENCHANTED),
	NECKLACE_OF_ANGUISH(Type.JEWELLERY_ENCHANTED, "Anguish", "ANG", ItemID.ZENYTE_NECKLACE_ENCHANTED),
	TORMENTED_BRACELET(Type.JEWELLERY_ENCHANTED, "Torment", "TOR", ItemID.ZENYTE_BRACELET_ENCHANTED),
	AMULET_OF_TORTURE(Type.JEWELLERY_ENCHANTED, "Torture", "TOR", ItemID.ZENYTE_AMULET_ENCHANTED),

	OCCULT_NECKLACE(Type.JEWELLERY_ENCHANTED, "Occult", "OCC", ItemID.OCCULT_NECKLACE),
	DRAGONBONE_NECKLACE(Type.JEWELLERY_ENCHANTED, "Dragon", "DRA", ItemID.DRAGONBONE_NECKLACE),
	SLAYER_RING(Type.JEWELLERY_ENCHANTED, "Slayer", "SLA", ItemID.SLAYER_RING_1, ItemID.SLAYER_RING_2, ItemID.SLAYER_RING_3, ItemID.SLAYER_RING_4, ItemID.SLAYER_RING_5, ItemID.SLAYER_RING_6, ItemID.SLAYER_RING_7, ItemID.SLAYER_RING_8, ItemID.SLAYER_RING_ETERNAL),

	// Wines
	JUG_OF_WATER(Type.WINES, "Water", "Wat", ItemID.JUG_WATER),
	UNFERMENTED_WINE(Type.WINES, "Unf.", "U", ItemID.JUG_UNFERMENTED_WINE),
	ZAMORAKS_UNFERMENTED_WINE(Type.WINES, "Unf. Z", "UZ", ItemID.JUG_UNFERMENTED_ZAMORAK_WINE),

	JUG_OF_WINE(Type.WINES, "Wine", "W", ItemID.JUG_WINE),
	WINE_OF_ZAMORAK(Type.WINES, "Zammy", "Z", ItemID.WINE_OF_ZAMORAK),
	JUG_OF_BAD_WINE(Type.WINES, "Bad", "Bad", ItemID.JUG_BAD_WINE),
	HALF_FULL_WINE_JUG(Type.WINES, "Half", "HF", ItemID.HALF_FULL_WINE_JUG),

	JUG_OF_BLESSED_WINE(Type.WINES, "Blessed", "B", ItemID.JUG_WINE_BLESSED),
	JUG_OF_SUNFIRE_WINE(Type.WINES, "Sunfire", "S", ItemID.JUG_SUNFIRE_WINE),
	JUG_OF_BLESSED_SUNFIRE_WINE(Type.WINES, "B. Sun", "BS", ItemID.JUG_SUNFIRE_WINE_BLESSED);

	final Type type;
	final String medName;
	final String shortName;
	final int[] itemIDs;

	ItemIdentification(Type type, String medName, String shortName, int... ids)
	{
		this.type = type;
		this.medName = medName;
		this.shortName = shortName;
		this.itemIDs = ids;
	}

	private static final Map<Integer, ItemIdentification> itemIdentifications;

	static
	{
		ImmutableMap.Builder<Integer, ItemIdentification> builder = new ImmutableMap.Builder<>();

		for (ItemIdentification i : values())
		{
			for (int id : i.itemIDs)
			{
				builder.put(id, i);
			}
		}

		itemIdentifications = builder.build();
	}

	static ItemIdentification get(int id)
	{
		return itemIdentifications.get(id);
	}

	@AllArgsConstructor
	enum Type
	{
		SEED_HERB(ItemIdentificationConfig::showHerbSeeds),
		SEED_BERRY(ItemIdentificationConfig::showBerrySeeds),
		SEED_ALLOTMENT(ItemIdentificationConfig::showAllotmentSeeds),
		SEED_SPECIAL(ItemIdentificationConfig::showSpecialSeeds),
		SEED_TREE(ItemIdentificationConfig::showTreeSeeds),
		SEED_FRUIT_TREE(ItemIdentificationConfig::showFruitTreeSeeds),
		SEED_FLOWER(ItemIdentificationConfig::showFlowerSeeds),
		HOPS_SEED(ItemIdentificationConfig::showHopsSeeds),
		SACK(ItemIdentificationConfig::showSacks),
		HERB(ItemIdentificationConfig::showHerbs),
		LOGS(ItemIdentificationConfig::showLogs),
		LOGS_PYRE(ItemIdentificationConfig::showPyreLogs),
		PLANK(ItemIdentificationConfig::showPlanks),
		SAPLING(ItemIdentificationConfig::showSaplings),
		COMPOST(ItemIdentificationConfig::showComposts),
		ORE(ItemIdentificationConfig::showOres),
		BAR(ItemIdentificationConfig::showBars),
		GEM(ItemIdentificationConfig::showGems),
		POTION(ItemIdentificationConfig::showPotions),
		BUTTERFLY_MOTH_JAR(ItemIdentificationConfig::showButterflyMothJars),
		IMPLING_JAR(ItemIdentificationConfig::showImplingJars),
		TABLET(ItemIdentificationConfig::showTablets),
		SCROLL(ItemIdentificationConfig::showTeleportScrolls),
		JEWELLERY(ItemIdentificationConfig::showJewellery),
		JEWELLERY_ENCHANTED(ItemIdentificationConfig::showEnchantedJewellery),
		WINES(ItemIdentificationConfig::showWines);

		final Predicate<ItemIdentificationConfig> enabled;
	}
}
