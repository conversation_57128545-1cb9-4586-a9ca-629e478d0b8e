/*
 * Copyright (c) 2018, <PERSON> <Seth<PERSON><EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.poh;

import com.google.common.collect.ImmutableMap;
import java.awt.image.BufferedImage;
import java.util.Map;
import lombok.Getter;
import net.runelite.api.gameval.ObjectID;
import net.runelite.client.util.ImageUtil;

public enum PohIcons
{
	EXITPORTAL("exitportal", ObjectID.POH_EXIT_PORTAL),
	VARROCK("varrock", ObjectID.POH_PORTAL_TEAK_VARROCK, ObjectID.POH_PORTAL_MAG_VARROCK, ObjectID.POH_PORTAL_MARBLE_VARROCK, ObjectID.POH_PORTAL_LEAGUE_5_VARROCK),
	FALADOR("falador", ObjectID.POH_PORTAL_TEAK_FALADOR, ObjectID.POH_PORTAL_MAG_FALADOR, ObjectID.POH_PORTAL_MARBLE_FALADOR, ObjectID.POH_PORTAL_LEAGUE_5_FALADOR),
	LUMBRIDGE("lumbridge", ObjectID.POH_PORTAL_TEAK_LUMBRIDGE, ObjectID.POH_PORTAL_MAG_LUMBRIDGE, ObjectID.POH_PORTAL_MARBLE_LUMBRIDGE, ObjectID.POH_PORTAL_LEAGUE_5_LUMBRIDGE),
	ARDOUGNE("ardougne", ObjectID.POH_PORTAL_TEAK_ARDOUGNE, ObjectID.POH_PORTAL_MAG_ARDOUGNE, ObjectID.POH_PORTAL_MARBLE_ARDOUGNE, ObjectID.POH_PORTAL_LEAGUE_5_ARDOUGNE),
	YANILLE("yanille", ObjectID.POH_PORTAL_TEAK_YANILLE, ObjectID.POH_PORTAL_MAG_YANILLE, ObjectID.POH_PORTAL_MARBLE_YANILLE, ObjectID.POH_PORTAL_LEAGUE_5_YANILLE),
	CAMELOT("camelot", ObjectID.POH_PORTAL_TEAK_CAMELOT, ObjectID.POH_PORTAL_MAG_CAMELOT, ObjectID.POH_PORTAL_MARBLE_CAMELOT, ObjectID.POH_PORTAL_LEAGUE_5_CAMELOT),
	LUNARISLE("lunarisle", ObjectID.POH_PORTAL_TEAK_LUNARISLE, ObjectID.POH_PORTAL_MAG_LUNARISLE, ObjectID.POH_PORTAL_MARBLE_LUNARISLE, ObjectID.POH_PORTAL_LEAGUE_5_LUNARISLE),
	WATERBIRTH("waterbirth", ObjectID.POH_PORTAL_TEAK_WATERBIRTH, ObjectID.POH_PORTAL_MAG_WATERBIRTH, ObjectID.POH_PORTAL_MARBLE_WATERBIRTH, ObjectID.POH_PORTAL_LEAGUE_5_WATERBIRTH),
	FISHINGGUILD("fishingguild", ObjectID.POH_PORTAL_TEAK_FISHINGGUILD, ObjectID.POH_PORTAL_MAG_FISHINGGUILD, ObjectID.POH_PORTAL_MARBLE_FISHINGGUILD, ObjectID.POH_PORTAL_LEAGUE_5_FISHINGGUILD),
	SENNTISTEN("senntisten", ObjectID.POH_PORTAL_TEAK_SENNTISTEN, ObjectID.POH_PORTAL_MAG_SENNTISTEN, ObjectID.POH_PORTAL_MARBLE_SENNTISTEN, ObjectID.POH_PORTAL_LEAGUE_5_SENNTISTEN),
	KHARYLL("kharyll", ObjectID.POH_PORTAL_TEAK_KHARYRLL, ObjectID.POH_PORTAL_MAG_KHARYRLL, ObjectID.POH_PORTAL_MARBLE_KHARYRLL, ObjectID.POH_PORTAL_LEAGUE_5_KHARYRLL),
	ANNAKARL("annakarl", ObjectID.POH_PORTAL_TEAK_ANNAKARL, ObjectID.POH_PORTAL_MAG_ANNAKARL, ObjectID.POH_PORTAL_MARBLE_ANNAKARL, ObjectID.POH_PORTAL_LEAGUE_5_ANNAKARL),
	KOUREND("kourend", ObjectID.POH_PORTAL_TEAK_KOUREND, ObjectID.POH_PORTAL_MAG_KOUREND, ObjectID.POH_PORTAL_MARBLE_KOUREND, ObjectID.POH_PORTAL_LEAGUE_5_KOUREND),
	MARIM("marim", ObjectID.POH_PORTAL_TEAK_MARIM, ObjectID.POH_PORTAL_MAG_MARIM, ObjectID.POH_PORTAL_MARBLE_MARIM, ObjectID.POH_PORTAL_LEAGUE_5_MARIM),
	TROLLSTRONGHOLD("trollstronghold", ObjectID.POH_PORTAL_TEAK_STRONGHOLD, ObjectID.POH_PORTAL_MAG_STRONGHOLD, ObjectID.POH_PORTAL_MARBLE_STRONGHOLD, ObjectID.POH_PORTAL_LEAGUE_5_STRONGHOLD),
	GHORROCK("ghorrock", ObjectID.POH_PORTAL_TEAK_GHORROCK, ObjectID.POH_PORTAL_MAG_GHORROCK, ObjectID.POH_PORTAL_MARBLE_GHORROCK, ObjectID.POH_PORTAL_LEAGUE_5_GHORROCK),
	CARRALLANGER("carrallanger", ObjectID.POH_PORTAL_TEAK_CARRALLANGAR, ObjectID.POH_PORTAL_MAG_CARRALLANGAR, ObjectID.POH_PORTAL_MARBLE_CARRALLANGAR, ObjectID.POH_PORTAL_LEAGUE_5_CARRALLANGAR),
	CATHERBY("catherby", ObjectID.POH_PORTAL_TEAK_CATHERBY, ObjectID.POH_PORTAL_MAG_CATHERBY, ObjectID.POH_PORTAL_MARBLE_CATHERBY, ObjectID.POH_PORTAL_LEAGUE_5_CATHERBY),
	WEISS("weiss", ObjectID.POH_PORTAL_TEAK_WEISS, ObjectID.POH_PORTAL_MAG_WEISS, ObjectID.POH_PORTAL_MARBLE_WEISS, ObjectID.POH_PORTAL_LEAGUE_5_WEISS),
	APEATOLLDUNGEON("apeatolldungeon", ObjectID.POH_PORTAL_TEAK_APE_ATOLL, ObjectID.POH_PORTAL_MAG_APE_ATOLL, ObjectID.POH_PORTAL_MARBLE_APE_ATOLL, ObjectID.POH_PORTAL_LEAGUE_5_APE_ATOLL),
	BARROWS("barrows", ObjectID.POH_PORTAL_TEAK_BARROWS, ObjectID.POH_PORTAL_MAG_BARROWS, ObjectID.POH_PORTAL_MARBLE_BARROWS, ObjectID.POH_PORTAL_LEAGUE_5_BARROWS),
	BATTLEFRONT("battlefront", ObjectID.POH_PORTAL_TEAK_BATTLEFRONT, ObjectID.POH_PORTAL_MAG_BATTLEFRONT, ObjectID.POH_PORTAL_MARBLE_BATTLEFRONT, ObjectID.POH_PORTAL_LEAGUE_5_BATTLEFRONT),
	CEMETERY("cemetery", ObjectID.POH_PORTAL_TEAK_CEMETERY, ObjectID.POH_PORTAL_MAG_CEMETERY, ObjectID.POH_PORTAL_MARBLE_CEMETERY, ObjectID.POH_PORTAL_LEAGUE_5_CEMETERY),
	DRAYNORMANOR("draynormanor", ObjectID.POH_PORTAL_TEAK_DRAYNOR_MANOR, ObjectID.POH_PORTAL_MAG_DRAYNOR_MANOR, ObjectID.POH_PORTAL_MARBLE_DRAYNOR_MANOR, ObjectID.POH_PORTAL_LEAGUE_5_DRAYNOR_MANOR),
	FENKENSTRAINSCASTLE("fenkenstrainscastle", ObjectID.POH_PORTAL_TEAK_FENKENSTRAIN, ObjectID.POH_PORTAL_MAG_FENKENSTRAIN, ObjectID.POH_PORTAL_MARBLE_FENKENSTRAIN, ObjectID.POH_PORTAL_LEAGUE_5_FENKENSTRAIN),
	HARMONYISLAND("harmonyisland", ObjectID.POH_PORTAL_TEAK_HARMONY_ISLAND, ObjectID.POH_PORTAL_MAG_HARMONY_ISLAND, ObjectID.POH_PORTAL_MARBLE_HARMONY_ISLAND, ObjectID.POH_PORTAL_LEAGUE_5_HARMONY_ISLAND),
	ARCEUUSLIBRARY("arceuuslibrary", ObjectID.POH_PORTAL_TEAK_ARCEUUS_LIBRARY, ObjectID.POH_PORTAL_MAG_ARCEUUS_LIBRARY, ObjectID.POH_PORTAL_MARBLE_ARCEUUS_LIBRARY, ObjectID.POH_PORTAL_LEAGUE_5_ARCEUUS_LIBRARY),
	MINDALTAR("mindaltar", ObjectID.POH_PORTAL_TEAK_MIND_ALTAR, ObjectID.POH_PORTAL_MAG_MIND_ALTAR, ObjectID.POH_PORTAL_MARBLE_MIND_ALTAR, ObjectID.POH_PORTAL_LEAGUE_5_MIND_ALTAR),
	SALVEGRAVEYARD("salvegraveyard", ObjectID.POH_PORTAL_TEAK_SALVE_GRAVEYARD, ObjectID.POH_PORTAL_MAG_SALVE_GRAVEYARD, ObjectID.POH_PORTAL_MARBLE_SALVE_GRAVEYARD, ObjectID.POH_PORTAL_LEAGUE_5_SALVE_GRAVEYARD),
	WESTARDOUGNE("westardougne", ObjectID.POH_PORTAL_TEAK_WEST_ARDOUGNE, ObjectID.POH_PORTAL_MAG_WEST_ARDOUGNE, ObjectID.POH_PORTAL_MARBLE_WEST_ARDOUGNE, ObjectID.POH_PORTAL_LEAGUE_5_WEST_ARDOUGNE),
	CIVITASILLAFORTIS("civitasillafortis",
		ObjectID.POH_PORTAL_TEAK_FORTIS, ObjectID.POH_PORTAL_MAG_FORTIS, ObjectID.POH_PORTAL_MARBLE_FORTIS, ObjectID.POH_PORTAL_LEAGUE_5_FORTIS
	),
	ALTAR("altar",
		ObjectID.POH_ALTAR_SARADOMIN_1, ObjectID.POH_ALTAR_ZAMORAK_1, ObjectID.POH_ALTAR_GUTHIX_1, ObjectID.POH_ALTAR_SARADOMIN_2, ObjectID.POH_ALTAR_ZAMORAK_2, ObjectID.POH_ALTAR_GUTHIX_2, ObjectID.POH_ALTAR_SARADOMIN_3, ObjectID.POH_ALTAR_ZAMORAK_3,
		ObjectID.POH_ALTAR_GUTHIX_3, ObjectID.POH_ALTAR_SARADOMIN_4, ObjectID.POH_ALTAR_ZAMORAK_4, ObjectID.POH_ALTAR_GUTHIX_4, ObjectID.POH_ALTAR_SARADOMIN_5, ObjectID.POH_ALTAR_ZAMORAK_5, ObjectID.POH_ALTAR_GUTHIX_5, ObjectID.POH_ALTAR_SARADOMIN_6,
		ObjectID.POH_ALTAR_GUTHIX_6, ObjectID.POH_ALTAR_SARADOMIN_7, ObjectID.POH_ALTAR_ZAMORAK_7, ObjectID.POH_ALTAR_GUTHIX_7
	),
	POOLS("pool",
		ObjectID.POH_POOL_RESTORATION, ObjectID.POH_POOL_REVITALISATION, ObjectID.POH_POOL_REJUVENATION, ObjectID.POH_POOL_RECOVERY, ObjectID.POH_POOL_REGENERATION,
		ObjectID.XMAS20_POH_POOL_RESTORATION, ObjectID.XMAS20_POH_POOL_REVITALISATION, ObjectID.XMAS20_POH_POOL_REJUVENATION, ObjectID.XMAS20_POH_POOL_RECOVERY, ObjectID.XMAS20_POH_POOL_REGENERATION
	),
	GLORY("glory", ObjectID.POH_TROPHY_AMULETOFGLORY_4),
	REPAIR("repair", ObjectID.POH_REPAIR_3),
	SPELLBOOKALTAR("spellbook", ObjectID.POH_ALTAR_ANCIENT, ObjectID.POH_ALTAR_LUNAR, ObjectID.ARCHEUS_ALTAR_DARK, ObjectID.POH_ALTAR_OCCULT),
	JEWELLERYBOX("jewellery", ObjectID.POH_JEWELLERY_BOX_1, ObjectID.POH_JEWELLERY_BOX_2, ObjectID.POH_JEWELLERY_BOX_3),
	MAGICTRAVEL("transportation", ObjectID.POH_SPIRIT_TREE, ObjectID.LEAGUE_5_POH_SPIRIT_TREE, ObjectID.LEAGUE_5_POH_SPIRIT_RING, ObjectID.POH_FAIRY_RING, ObjectID.POH_SPIRIT_RING, ObjectID.POH_WILDERNESS_OBELISK, ObjectID.XMAS20_POH_SPIRIT_TREE, ObjectID.XMAS20_POH_SPIRIT_RING),
	PORTALNEXUS("portalnexus", ObjectID.POH_NEXUS_PORTAL_1, ObjectID.POH_NEXUS_PORTAL_2, ObjectID.POH_NEXUS_PORTAL_3, ObjectID.POH_NEXUS_PORTAL_LEAGUE_5),
	XERICSTALISMAN("xericstalisman",
		ObjectID.POH_AMULET_XERIC_LOOKOUT, ObjectID.POH_AMULET_XERIC_GLADE, ObjectID.POH_AMULET_XERIC_INFERNO, ObjectID.POH_AMULET_XERIC_HEART, ObjectID.POH_AMULET_XERIC_HONOUR, ObjectID.POH_AMULET_XERIC
	),
	DIGSITEPENDANT("digsitependant",
		ObjectID.POH_AMULET_DIG_DIGSITE, ObjectID.POH_AMULET_DIG_FOSSIL, ObjectID.POH_AMULET_DIG_LITHKREN, ObjectID.POH_AMULET_DIGSITE
	),
	MYTHICALCAPE("mythicalcape", ObjectID.POH_TROPHY_MYTHICAL_CAPE, ObjectID.POH_MOUNTED_MYTHICALCAPE);

	private static final Map<Integer, PohIcons> minimapIcons;

	@Getter
	private final String imageResource;
	@Getter
	private final int[] Ids;

	private BufferedImage image;

	static
	{
		ImmutableMap.Builder<Integer, PohIcons> builder = new ImmutableMap.Builder<>();

		for (PohIcons icon : values())
		{
			for (Integer spotId : icon.getIds())
			{
				builder.put(spotId, icon);
			}
		}

		minimapIcons = builder.build();
	}

	PohIcons(String imageResource, int... ids)
	{
		this.imageResource = imageResource;
		this.Ids = ids;
	}

	public static PohIcons getIcon(int id)
	{
		return minimapIcons.get(id);
	}

	public BufferedImage getImage()
	{
		if (image != null)
		{
			return image;
		}

		image = ImageUtil.loadImageResource(getClass(), getImageResource() + ".png");

		return image;
	}
}
