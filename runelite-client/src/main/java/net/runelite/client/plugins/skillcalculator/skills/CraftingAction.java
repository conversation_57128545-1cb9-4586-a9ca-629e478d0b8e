/*
 * Copyright (c) 2021, <PERSON> <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.skillcalculator.skills;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.runelite.api.gameval.ItemID;
import net.runelite.client.game.ItemManager;

@AllArgsConstructor
@Getter
public enum CraftingAction implements ItemSkillAction
{
	BALL_OF_WOOL(ItemID.BALL_OF_WOOL, 1, 2.5f),
	POT(ItemID.POT_EMPTY, 1, 6.3f),
	UNFIRED_POT(ItemID.POT_UNFIRED, 1, 6.3f),
	OPAL_RING(ItemID.OPAL_RING, 1, 10),
	LEATHER_GLOVES(ItemID.LEATHER_GLOVES, 1, 13.8f),
	OPAL(ItemID.OPAL, 1, 15),
	BEER_GLASS(ItemID.BEER_GLASS, 1, 17.5f, true),
	MOLTEN_GLASS(ItemID.MOLTEN_GLASS, 1, 20),
	EMPTY_CANDLE_LANTERN(ItemID.CANDLE_LANTERN_EMPTY, 4, 19),
	BIRD_HOUSE(ItemID.BIRDHOUSE_NORMAL, 5, 15),
	GOLD_RING(ItemID.GOLD_RING, 5, 15),
	GOLD_NECKLACE(ItemID.GOLD_NECKLACE, 6, 20),
	PIE_DISH(ItemID.PIEDISH, 7, 10),
	UNFIRED_PIE_DISH(ItemID.PIEDISH_UNFIRED, 7, 15),
	LEATHER_BOOTS(ItemID.LEATHER_BOOTS, 7, 16.3f),
	GOLD_BRACELET(ItemID.JEWL_GOLD_BRACELET, 7, 25),
	BOWL(ItemID.BOWL_EMPTY, 8, 15),
	UNFIRED_BOWL(ItemID.BOWL_UNFIRED, 8, 18),
	GOLD_AMULET_U(ItemID.UNSTRUNG_GOLD_AMULET, 8, 30),
	COWL(ItemID.LEATHER_COWL, 9, 18.5f),
	BOW_STRING(ItemID.BOW_STRING, 10, 15),
	CROSSBOW_STRING(ItemID.XBOWS_CROSSBOW_STRING, 10, 15),
	LEATHER_VAMBRACES(ItemID.LEATHER_VAMBRACES, 11, 22),
	EMPTY_OIL_LAMP(ItemID.OIL_LAMP_EMPTY, 12, 25),
	JADE(ItemID.JADE, 13, 20),
	JADE_RING(ItemID.JADE_RING, 13, 32),
	LEATHER_BODY(ItemID.LEATHER_ARMOUR, 14, 25),
	OAK_BIRD_HOUSE(ItemID.BIRDHOUSE_OAK, 15, 20),
	RED_TOPAZ(ItemID.RED_TOPAZ, 16, 25),
	OPAL_NECKLACE(ItemID.OPAL_NECKLACE, 16, 35),
	TOPAZ_RING(ItemID.TOPAZ_RING, 16, 35),
	HOLY_SYMBOL(ItemID.BLESSEDSTAR, 16, 50),
	UNHOLY_SYMBOL(ItemID.BLESSEDSNAKE, 17, 50),
	LEATHER_CHAPS(ItemID.LEATHER_CHAPS, 18, 27),
	EMPTY_PLANT_POT(ItemID.PLANTPOT_EMPTY, 19, 17.5f),
	UNFIRED_PLANT_POT(ItemID.PLANTPOT_UNFIRED, 19, 20),
	MAGIC_STRING(ItemID.MAGIC_STRING, 19, 30),
	SAPPHIRE_RING(ItemID.SAPPHIRE_RING, 20, 40),
	SAPPHIRE(ItemID.SAPPHIRE, 20, 50),
	EMPTY_SACK(ItemID.SACK_EMPTY, 21, 38),
	OPAL_BRACELET(ItemID.OPAL_BRACELET, 22, 45),
	SAPPHIRE_NECKLACE(ItemID.SAPPHIRE_NECKLACE, 22, 55),
	TIARA(ItemID.TIARA, 23, 52.5f),
	SAPPHIRE_BRACELET(ItemID.JEWL_SAPPHIRE_BRACELET, 23, 60),
	SAPPHIRE_AMULET_U(ItemID.UNSTRUNG_SAPPHIRE_AMULET, 24, 65),
	POT_LID(ItemID.POTLID, 25, 20),
	UNFIRED_POT_LID(ItemID.POTLID_UNFIRED, 25, 20),
	WILLOW_BIRD_HOUSE(ItemID.BIRDHOUSE_WILLOW, 25, 25),
	JADE_NECKLACE(ItemID.JADE_NECKLACE, 25, 54),
	DRIFT_NET(ItemID.FOSSIL_DRIFT_NET, 26, 55),
	EMERALD_RING(ItemID.EMERALD_RING, 27, 55),
	OPAL_AMULET_U(ItemID.UNSTRUNG_OPAL_AMULET, 27, 55),
	EMERALD(ItemID.EMERALD, 27, 67.5f),
	HARDLEATHER_BODY(ItemID.HARDLEATHER_BODY, 28, 35),
	EMERALD_NECKLACE(ItemID.EMERALD_NECKLACE, 29, 60),
	JADE_BRACELET(ItemID.JADE_BRACELET, 29, 60),
	ROPE(ItemID.ROPE, 30, 25, true),
	EMERALD_BRACELET(ItemID.JEWL_EMERALD_BRACELET, 30, 65),
	EMERALD_AMULET_U(ItemID.UNSTRUNG_EMERALD_AMULET, 31, 70),
	SPIKY_VAMBRACES(ItemID.SPIKED_VAMBRACES, 32, 6),
	TOPAZ_NECKLACE(ItemID.TOPAZ_NECKLACE, 32, 70),
	VIAL(ItemID.VIAL_EMPTY, 33, 35, true),
	JADE_AMULET_U(ItemID.UNSTRUNG_JADE_AMULET, 34, 70),
	RUBY_RING(ItemID.RUBY_RING, 34, 70),
	RUBY(ItemID.RUBY, 34, 85),
	TEAK_BIRD_HOUSE(ItemID.BIRDHOUSE_TEAK, 35, 30),
	BROODOO_SHIELD(ItemID.BROODOO_POISONSHIELD, 35, 100),
	BASKET(ItemID.BASKET_EMPTY, 36, 56),
	COIF(ItemID.COIF, 38, 37, true),
	TOPAZ_BRACELET(ItemID.TOPAZ_BRACELET, 38, 75),
	RUBY_NECKLACE(ItemID.RUBY_NECKLACE, 40, 75),
	HARD_LEATHER_SHIELD(ItemID.LEATHER_SHIELD, 41, 70),
	GOLD_TIARA(ItemID.TIARA_GOLD, 42, 35),
	FISHBOWL(ItemID.FISHBOWL_WATER, 42, 42.5f),
	RUBY_BRACELET(ItemID.JEWL_RUBY_BRACELET, 42, 80),
	DIAMOND_RING(ItemID.DIAMOND_RING, 43, 85),
	DIAMOND(ItemID.DIAMOND, 43, 107.5f),
	SNAKESKIN_BOOTS(ItemID.SNAKESKIN_BOOTS, 45, 30),
	MAPLE_BIRD_HOUSE(ItemID.BIRDHOUSE_MAPLE, 45, 35),
	TOPAZ_AMULET_U(ItemID.UNSTRUNG_TOPAZ_AMULET, 45, 80),
	UNPOWERED_ORB(ItemID.STAFFORB, 46, 52.5f),
	SNAKESKIN_VAMBRACES(ItemID.SNAKESKIN_VAMBRACES, 47, 35),
	SNAKESKIN_BANDANA(ItemID.SNAKESKIN_BANDANA, 48, 45),
	LANTERN_LENS(ItemID.BULLSEYE_LANTERN_LENS, 49, 55),
	MAHOGANY_BIRD_HOUSE(ItemID.BIRDHOUSE_MAHOGANY, 50, 40),
	RUBY_AMULET_U(ItemID.UNSTRUNG_RUBY_AMULET, 50, 85),
	SNAKESKIN_CHAPS(ItemID.SNAKESKIN_CHAPS, 51, 50),
	SNAKESKIN_BODY(ItemID.SNAKESKIN_BODY, 53, 55),
	WATER_BATTLESTAFF(ItemID.WATER_BATTLESTAFF, 54, 100),
	DRAGONSTONE_RING(ItemID.DRAGONSTONE_RING, 55, 100),
	DRAGONSTONE(ItemID.DRAGONSTONE, 55, 137.5f),
	DIAMOND_NECKLACE(ItemID.DIAMOND_NECKLACE, 56, 90),
	SNAKESKIN_SHIELD(ItemID.SNAKESKIN_SHIELD, 56, 100),
	GREEN_DHIDE_VAMB(ItemID.DRAGON_VAMBRACES, 57, 62, true),
	DIAMOND_BRACELET(ItemID.JEWL_DIAMOND_BRACELET, 58, 95),
	EARTH_BATTLESTAFF(ItemID.EARTH_BATTLESTAFF, 58, 112.5f),
	YEW_BIRD_HOUSE(ItemID.BIRDHOUSE_YEW, 60, 45),
	GREEN_DHIDE_CHAPS(ItemID.DRAGONHIDE_CHAPS, 60, 124, true),
	GREEN_DHIDE_SHIELD(ItemID.GREEN_DHIDE_SHIELD, 62, 124),
	FIRE_BATTLESTAFF(ItemID.FIRE_BATTLESTAFF, 62, 125),
	GREEN_DHIDE_BODY(ItemID.DRAGONHIDE_BODY, 63, 186, true),
	BLUE_DHIDE_VAMB(ItemID.BLUE_DRAGON_VAMBRACES, 66, 70),
	AIR_BATTLESTAFF(ItemID.AIR_BATTLESTAFF, 66, 137.5f),
	ONYX_RING(ItemID.ONYX_RING, 67, 115),
	ONYX(ItemID.ONYX, 67, 167.5f),
	BLUE_DHIDE_CHAPS(ItemID.BLUE_DRAGONHIDE_CHAPS, 68, 140),
	BLUE_DHIDE_SHIELD(ItemID.BLUE_DHIDE_SHIELD, 69, 140),
	DIAMOND_AMULET_U(ItemID.UNSTRUNG_DIAMOND_AMULET, 70, 100),
	BLUE_DHIDE_BODY(ItemID.BLUE_DRAGONHIDE_BODY, 71, 210),
	DRAGONSTONE_NECKLACE(ItemID.DRAGONSTONE_NECKLACE, 72, 105),
	RED_DHIDE_VAMB(ItemID.RED_DRAGON_VAMBRACES, 73, 78),
	DRAGONSTONE_BRACELET(ItemID.JEWL_DRAGONSTONE_BRACELET, 74, 110),
	MAGIC_BIRD_HOUSE(ItemID.BIRDHOUSE_MAGIC, 75, 50),
	RED_DHIDE_CHAPS(ItemID.RED_DRAGONHIDE_CHAPS, 75, 156),
	RED_DHIDE_SHIELD(ItemID.RED_DHIDE_SHIELD, 76, 156),
	RED_DHIDE_BODY(ItemID.RED_DRAGONHIDE_BODY, 77, 234),
	BLACK_DHIDE_VAMB(ItemID.BLACK_DRAGON_VAMBRACES, 79, 86),
	DRAGONSTONE_AMULET_U(ItemID.UNSTRUNG_DRAGONSTONE_AMULET, 80, 150),
	ONYX_NECKLACE(ItemID.ONYX_NECKLACE, 82, 120),
	BLACK_DHIDE_CHAPS(ItemID.BLACK_DRAGONHIDE_CHAPS, 82, 172),
	AMETHYST_BOLT_TIPS(ItemID.XBOWS_BOLT_TIPS_AMETHYST, 83, 4),
	BLACK_DHIDE_SHIELD(ItemID.BLACK_DHIDE_SHIELD, 83, 172),
	ONYX_BRACELET(ItemID.JEWL_ONYX_BRACELET, 84, 125),
	BLACK_DHIDE_BODY(ItemID.BLACK_DRAGONHIDE_BODY, 84, 258),
	AMETHYST_ARROWTIPS(ItemID.AMETHYST_ARROWHEADS, 85, 4),
	AMETHYST_JAVELIN_HEADS(ItemID.AMETHYST_JAVELIN_HEAD, 87, 12),
	LIGHT_ORB(ItemID.DORGESH_LIGHT_BULB, 87, 70),
	AMETHYST_DART_TIP(ItemID.AMETHYST_DART_TIP, 89, 7.5f),
	ZENYTE_RING(ItemID.ZENYTE_RING, 89, 150),
	ZENYTE(ItemID.ZENYTE, 89, 200),
	REDWOOD_BIRD_HOUSE(ItemID.BIRDHOUSE_REDWOOD, 90, 55),
	ONYX_AMULET_U(ItemID.UNSTRUNG_ONYX_AMULET, 90, 165),
	ZENYTE_NECKLACE(ItemID.ZENYTE_NECKLACE, 92, 165),
	ZENYTE_BRACELET(ItemID.ZENYTE_BRACELET, 95, 180),
	ZENYTE_AMULET_U(ItemID.UNSTRUNG_ZENYTE_AMULET, 98, 200),
	;

	private final int itemId;
	private final int level;
	private final float xp;
	private final boolean isMembersOverride;

	CraftingAction(int itemId, int level, float xp)
	{
		this(itemId, level, xp, false);
	}

	@Override
	public boolean isMembers(final ItemManager itemManager)
	{
		return isMembersOverride() || ItemSkillAction.super.isMembers(itemManager);
	}
}
