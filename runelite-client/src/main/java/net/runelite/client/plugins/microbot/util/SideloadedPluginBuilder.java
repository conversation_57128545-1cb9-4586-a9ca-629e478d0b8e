package net.runelite.client.plugins.microbot.util;

import java.io.*;
import java.nio.file.*;
import java.util.jar.*;
import java.util.zip.ZipEntry;

/**
 * Utility class to dynamically build JAR files for all sideloaded plugins
 */
public class SideloadedPluginBuilder {
    
    public static void main(String[] args) {
        if (args.length != 3) {
            System.err.println("Usage: SideloadedPluginBuilder <outputDirectory> <targetDirectory> <copyToDirectory>");
            System.exit(1);
        }
        
        String outputDirectory = args[0];
        String targetDirectory = args[1];
        String copyToDirectory = args[2];
        
        try {
            buildSideloadedPlugins(outputDirectory, targetDirectory, copyToDirectory);
        } catch (Exception e) {
            System.err.println("Error building sideloaded plugins: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    public static void buildSideloadedPlugins(String outputDirectory, String targetDirectory, String copyToDirectory) throws IOException {
        Path sideloadedDir = Paths.get(outputDirectory, "net", "runelite", "client", "plugins", "microbot", "sideloaded");
        Path targetDir = Paths.get(targetDirectory);
        Path copyToDir = Paths.get(copyToDirectory);
        
        if (!Files.exists(sideloadedDir)) {
            System.out.println("Sideloaded plugins directory does not exist: " + sideloadedDir);
            return;
        }
        
        // Create copy destination directory if it doesn't exist
        if (!Files.exists(copyToDir)) {
            Files.createDirectories(copyToDir);
        }
        
        // Find all subdirectories in sideloaded
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(sideloadedDir, Files::isDirectory)) {
            for (Path pluginDir : stream) {
                String pluginName = pluginDir.getFileName().toString();
                String jarName = "sideloaded-" + pluginName + ".jar";
                Path jarPath = targetDir.resolve(jarName);
                
                System.out.println("Building sideloaded plugin JAR: " + jarName);
                
                // Create JAR file
                try (JarOutputStream jarOut = new JarOutputStream(new FileOutputStream(jarPath.toFile()))) {
                    // Add manifest
                    Manifest manifest = new Manifest();
                    manifest.getMainAttributes().put(Attributes.Name.MANIFEST_VERSION, "1.0");
                    manifest.getMainAttributes().put(Attributes.Name.CLASS_PATH, ".");
                    
                    ZipEntry manifestEntry = new ZipEntry("META-INF/MANIFEST.MF");
                    jarOut.putNextEntry(manifestEntry);
                    manifest.write(jarOut);
                    jarOut.closeEntry();
                    
                    // Add all class files from this plugin directory
                    Path pluginClassDir = Paths.get(outputDirectory, "net", "runelite", "client", "plugins", "microbot", "sideloaded", pluginName);
                    if (Files.exists(pluginClassDir)) {
                        addDirectoryToJar(jarOut, pluginClassDir, "net/runelite/client/plugins/microbot/sideloaded/" + pluginName + "/");
                    }
                }
                
                // Copy to primary destination directory (x11-setup)
                Path destPath = copyToDir.resolve(jarName);
                Files.copy(jarPath, destPath, StandardCopyOption.REPLACE_EXISTING);
                System.out.println("Copied " + jarName + " to " + destPath);

                // Copy to all rscache directories matching pattern C:\rscache\*\microbot-plugins
                copyToRsCacheDirectories(jarPath, jarName);
            }
        }
    }
    
    private static void addDirectoryToJar(JarOutputStream jarOut, Path dir, String basePath) throws IOException {
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(dir)) {
            for (Path entry : stream) {
                if (Files.isDirectory(entry)) {
                    addDirectoryToJar(jarOut, entry, basePath + entry.getFileName().toString() + "/");
                } else if (Files.isRegularFile(entry) && entry.toString().endsWith(".class")) {
                    String entryName = basePath + entry.getFileName().toString();
                    ZipEntry zipEntry = new ZipEntry(entryName);
                    jarOut.putNextEntry(zipEntry);
                    Files.copy(entry, jarOut);
                    jarOut.closeEntry();
                }
            }
        }
    }

    private static void copyToRsCacheDirectories(Path jarPath, String jarName) {
        try {
            Path rsCacheRoot = Paths.get("C:", "rscache");
            if (!Files.exists(rsCacheRoot)) {
                System.out.println("rscache directory does not exist: " + rsCacheRoot);
                return;
            }

            // Find all subdirectories in C:\rscache\
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(rsCacheRoot, Files::isDirectory)) {
                for (Path userDir : stream) {
                    Path microbotPluginsDir = userDir.resolve("microbot-plugins");

                    // Create microbot-plugins directory if it doesn't exist
                    if (!Files.exists(microbotPluginsDir)) {
                        Files.createDirectories(microbotPluginsDir);
                        System.out.println("Created directory: " + microbotPluginsDir);
                    }

                    // Copy the JAR file
                    Path destPath = microbotPluginsDir.resolve(jarName);
                    Files.copy(jarPath, destPath, StandardCopyOption.REPLACE_EXISTING);
                    System.out.println("Copied " + jarName + " to " + destPath);
                }
            }
        } catch (IOException e) {
            System.err.println("Error copying to rscache directories: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
