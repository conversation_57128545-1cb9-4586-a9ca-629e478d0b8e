/*
 * Copyright (c) 2017, <PERSON> <Seth<PERSON><EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.itemcharges;

import com.google.common.collect.ImmutableMap;
import java.util.Map;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import net.runelite.api.gameval.ItemID;
import static net.runelite.client.plugins.itemcharges.ItemChargeType.POTION;
import static net.runelite.client.plugins.itemcharges.ItemChargeType.*;

@AllArgsConstructor
@Getter
enum ItemWithCharge
{
	ABRACE1(ABYSSAL_BRACELET, ItemID.JEWL_RUNERUNNING_BRACELET_1, 1),
	ABRACE2(ABYSSAL_BRACELET, ItemID.JEWL_RUNERUNNING_BRACELET_2, 2),
	ABRACE3(ABYSSAL_BRACELET, ItemID.JEWL_RUNERUNNING_BRACELET_3, 3),
	ABRACE4(ABYSSAL_BRACELET, ItemID.JEWL_RUNERUNNING_BRACELET_4, 4),
	ABRACE5(ABYSSAL_BRACELET, ItemID.JEWL_RUNERUNNING_BRACELET_5, 5),
	ABSORPTION1(POTION, ItemID.NZONE1DOSEABSORPTIONPOTION, 1),
	ABSORPTION2(POTION, ItemID.NZONE2DOSEABSORPTIONPOTION, 2),
	ABSORPTION3(POTION, ItemID.NZONE3DOSEABSORPTIONPOTION, 3),
	ABSORPTION4(POTION, ItemID.NZONE4DOSEABSORPTIONPOTION, 4),
	AGILITY1(POTION, ItemID._1DOSE1AGILITY, 1),
	AGILITY2(POTION, ItemID._2DOSE1AGILITY, 2),
	AGILITY3(POTION, ItemID._3DOSE1AGILITY, 3),
	AGILITY4(POTION, ItemID._4DOSE1AGILITY, 4),
	AGILITY_MIX_1(POTION, ItemID.BRUTAL_1DOSE1AGILITY, 1),
	AGILITY_MIX_2(POTION, ItemID.BRUTAL_2DOSE1AGILITY, 2),
	ANTI1(POTION, ItemID._1DOSEANTIPOISON, 1),
	ANTI2(POTION, ItemID._2DOSEANTIPOISON, 2),
	ANTI3(POTION, ItemID._3DOSEANTIPOISON, 3),
	ANTI4(POTION, ItemID._4DOSEANTIPOISON, 4),
	ANTI_MIX1(POTION, ItemID.BRUTAL_1DOSEANTIPOISON, 1),
	ANTI_MIX2(POTION, ItemID.BRUTAL_2DOSEANTIPOISON, 2),
	ANTIDOTE_P1(POTION, ItemID.ANTIDOTE_1, 1),
	ANTIDOTE_P2(POTION, ItemID.ANTIDOTE_2, 2),
	ANTIDOTE_P3(POTION, ItemID.ANTIDOTE_3, 3),
	ANTIDOTE_P4(POTION, ItemID.ANTIDOTE_4, 4),
	ANTIDOTE_P_MIX1(POTION, ItemID.BRUTAL_ANTIDOTE_1, 1),
	ANTIDOTE_P_MIX2(POTION, ItemID.BRUTAL_ANTIDOTE_2, 2),
	ANTIDOTE_PP1(POTION, ItemID.ANTIDOTE__1, 1),
	ANTIDOTE_PP2(POTION, ItemID.ANTIDOTE__2, 2),
	ANTIDOTE_PP3(POTION, ItemID.ANTIDOTE__3, 3),
	ANTIDOTE_PP4(POTION, ItemID.ANTIDOTE__4, 4),
	ANCIENT_BR1(POTION, ItemID._1DOSEANCIENTBREW, 1),
	ANCIENT_BR2(POTION, ItemID._2DOSEANCIENTBREW, 2),
	ANCIENT_BR3(POTION, ItemID._3DOSEANCIENTBREW, 3),
	ANCIENT_BR4(POTION, ItemID._4DOSEANCIENTBREW, 4),
	ANCIENT_MIX_1(POTION, ItemID.BRUTAL_1DOSEANCIENTBREW, 1),
	ANCIENT_MIX_2(POTION, ItemID.BRUTAL_2DOSEANCIENTBREW, 2),
	ANTIFIRE1(POTION, ItemID._1DOSE1ANTIDRAGON, 1),
	ANTIFIRE2(POTION, ItemID._2DOSE1ANTIDRAGON, 2),
	ANTIFIRE3(POTION, ItemID._3DOSE1ANTIDRAGON, 3),
	ANTIFIRE4(POTION, ItemID._4DOSE1ANTIDRAGON, 4),
	ANTIFIRE_MIX_1(POTION, ItemID.BRUTAL_1DOSE1ANTIDRAGON, 1),
	ANTIFIRE_MIX_2(POTION, ItemID.BRUTAL_2DOSE1ANTIDRAGON, 2),
	ANTIVEN1(POTION, ItemID.ANTIVENOM1, 1),
	ANTIVEN2(POTION, ItemID.ANTIVENOM2, 2),
	ANTIVEN3(POTION, ItemID.ANTIVENOM3, 3),
	ANTIVEN4(POTION, ItemID.ANTIVENOM4, 4),
	ANTIVENOM_P1(POTION, ItemID.ANTIVENOM_1, 1),
	ANTIVENOM_P2(POTION, ItemID.ANTIVENOM_2, 2),
	ANTIVENOM_P3(POTION, ItemID.ANTIVENOM_3, 3),
	ANTIVENOM_P4(POTION, ItemID.ANTIVENOM_4, 4),
	ATTACK1(POTION, ItemID._1DOSE1ATTACK, 1),
	ATTACK2(POTION, ItemID._2DOSE1ATTACK, 2),
	ATTACK3(POTION, ItemID._3DOSE1ATTACK, 3),
	ATTACK4(POTION, ItemID._4DOSE1ATTACK, 4),
	ATTACK_MIX_1(POTION, ItemID.BRUTAL_1DOSE1ATTACK, 1),
	ATTACK_MIX_2(POTION, ItemID.BRUTAL_2DOSE1ATTACK, 2),
	BASKET_APPLES1(FRUIT_BASKET, ItemID.BASKET_APPLE_1, 1),
	BASKET_APPLES2(FRUIT_BASKET, ItemID.BASKET_APPLE_2, 2),
	BASKET_APPLES3(FRUIT_BASKET, ItemID.BASKET_APPLE_3, 3),
	BASKET_APPLES4(FRUIT_BASKET, ItemID.BASKET_APPLE_4, 4),
	BASKET_APPLES5(FRUIT_BASKET, ItemID.BASKET_APPLE_5, 5),
	BASKET_BANANAS1(FRUIT_BASKET, ItemID.BASKET_BANANA_1, 1),
	BASKET_BANANAS2(FRUIT_BASKET, ItemID.BASKET_BANANA_2, 2),
	BASKET_BANANAS3(FRUIT_BASKET, ItemID.BASKET_BANANA_3, 3),
	BASKET_BANANAS4(FRUIT_BASKET, ItemID.BASKET_BANANA_4, 4),
	BASKET_BANANAS5(FRUIT_BASKET, ItemID.BASKET_BANANA_5, 5),
	BASKET_ORANGES1(FRUIT_BASKET, ItemID.BASKET_ORANGE_1, 1),
	BASKET_ORANGES2(FRUIT_BASKET, ItemID.BASKET_ORANGE_2, 2),
	BASKET_ORANGES3(FRUIT_BASKET, ItemID.BASKET_ORANGE_3, 3),
	BASKET_ORANGES4(FRUIT_BASKET, ItemID.BASKET_ORANGE_4, 4),
	BASKET_ORANGES5(FRUIT_BASKET, ItemID.BASKET_ORANGE_5, 5),
	BASKET_STRAWBERRIES1(FRUIT_BASKET, ItemID.BASKET_STRAWBERRY_1, 1),
	BASKET_STRAWBERRIES2(FRUIT_BASKET, ItemID.BASKET_STRAWBERRY_2, 2),
	BASKET_STRAWBERRIES3(FRUIT_BASKET, ItemID.BASKET_STRAWBERRY_3, 3),
	BASKET_STRAWBERRIES4(FRUIT_BASKET, ItemID.BASKET_STRAWBERRY_4, 4),
	BASKET_STRAWBERRIES5(FRUIT_BASKET, ItemID.BASKET_STRAWBERRY_5, 5),
	BASKET_TOMATOES1(FRUIT_BASKET, ItemID.BASKET_TOMATO_1, 1),
	BASKET_TOMATOES2(FRUIT_BASKET, ItemID.BASKET_TOMATO_2, 2),
	BASKET_TOMATOES3(FRUIT_BASKET, ItemID.BASKET_TOMATO_3, 3),
	BASKET_TOMATOES4(FRUIT_BASKET, ItemID.BASKET_TOMATO_4, 4),
	BASKET_TOMATOES5(FRUIT_BASKET, ItemID.BASKET_TOMATO_5, 5),
	BASTION1(POTION, ItemID._1DOSEBASTION, 1),
	BASTION2(POTION, ItemID._2DOSEBASTION, 2),
	BASTION3(POTION, ItemID._3DOSEBASTION, 3),
	BASTION4(POTION, ItemID._4DOSEBASTION, 4),
	BATTLEMAGE1(POTION, ItemID._1DOSEBATTLEMAGE, 1),
	BATTLEMAGE2(POTION, ItemID._2DOSEBATTLEMAGE, 2),
	BATTLEMAGE3(POTION, ItemID._3DOSEBATTLEMAGE, 3),
	BATTLEMAGE4(POTION, ItemID._4DOSEBATTLEMAGE, 4),
	BELLOWS0(BELLOWS, ItemID.EMPTY_OGRE_BELLOWS, 0),
	BELLOWS1(BELLOWS, ItemID.FILLED_OGRE_BELLOW1, 1),
	BELLOWS2(BELLOWS, ItemID.FILLED_OGRE_BELLOW2, 2),
	BELLOWS3(BELLOWS, ItemID.FILLED_OGRE_BELLOW3, 3),
	BLACK_WARLOCK_MIX1(POTION, ItemID.HUNTER_MIX_WARLOCK_1DOSE, 1),
	BLACK_WARLOCK_MIX2(POTION, ItemID.HUNTER_MIX_WARLOCK_2DOSE, 2),
	BLIGHTED_SUPER_REST1(POTION, ItemID.BLIGHTED_1DOSE2RESTORE, 1),
	BLIGHTED_SUPER_REST2(POTION, ItemID.BLIGHTED_2DOSE2RESTORE, 2),
	BLIGHTED_SUPER_REST3(POTION, ItemID.BLIGHTED_3DOSE2RESTORE, 3),
	BLIGHTED_SUPER_REST4(POTION, ItemID.BLIGHTED_4DOSE2RESTORE, 4),
	BURNING1(TELEPORT, ItemID.BURNING_AMULET_1, 1),
	BURNING2(TELEPORT, ItemID.BURNING_AMULET_2, 2),
	BURNING3(TELEPORT, ItemID.BURNING_AMULET_3, 3),
	BURNING4(TELEPORT, ItemID.BURNING_AMULET_4, 4),
	BURNING5(TELEPORT, ItemID.BURNING_AMULET_5, 5),
	CBRACE1(TELEPORT, ItemID.JEWL_BRACELET_OF_COMBAT_1, 1),
	CBRACE2(TELEPORT, ItemID.JEWL_BRACELET_OF_COMBAT_2, 2),
	CBRACE3(TELEPORT, ItemID.JEWL_BRACELET_OF_COMBAT_3, 3),
	CBRACE4(TELEPORT, ItemID.JEWL_BRACELET_OF_COMBAT_4, 4),
	CBRACE5(TELEPORT, ItemID.JEWL_BRACELET_OF_COMBAT_5, 5),
	CBRACE6(TELEPORT, ItemID.JEWL_BRACELET_OF_COMBAT_6, 6),
	COMBAT1(POTION, ItemID._1DOSECOMBAT, 1),
	COMBAT2(POTION, ItemID._2DOSECOMBAT, 2),
	COMBAT3(POTION, ItemID._3DOSECOMBAT, 3),
	COMBAT4(POTION, ItemID._4DOSECOMBAT, 4),
	COMBAT_MIX_1(POTION, ItemID.BRUTAL_1DOSECOMBAT, 1),
	COMBAT_MIX_2(POTION, ItemID.BRUTAL_2DOSECOMBAT, 2),
	COMPOST1(POTION, ItemID.SUPERCOMPOST_POTION_1, 1),
	COMPOST2(POTION, ItemID.SUPERCOMPOST_POTION_2, 2),
	COMPOST3(POTION, ItemID.SUPERCOMPOST_POTION_3, 3),
	COMPOST4(POTION, ItemID.SUPERCOMPOST_POTION_4, 4),
	DEFENCE1(POTION, ItemID._1DOSE1DEFENSE, 1),
	DEFENCE2(POTION, ItemID._2DOSE1DEFENSE, 2),
	DEFENCE3(POTION, ItemID._3DOSE1DEFENSE, 3),
	DEFENCE4(POTION, ItemID._4DOSE1DEFENSE, 4),
	DEFENCE_MIX_1(POTION, ItemID.BRUTAL_1DOSE1DEFENSE, 1),
	DEFENCE_MIX_2(POTION, ItemID.BRUTAL_2DOSE1DEFENSE, 2),
	DIGSITE1(TELEPORT, ItemID.NECKLACE_OF_DIGSITE_1, 1),
	DIGSITE2(TELEPORT, ItemID.NECKLACE_OF_DIGSITE_2, 2),
	DIGSITE3(TELEPORT, ItemID.NECKLACE_OF_DIGSITE_3, 3),
	DIGSITE4(TELEPORT, ItemID.NECKLACE_OF_DIGSITE_4, 4),
	DIGSITE5(TELEPORT, ItemID.NECKLACE_OF_DIGSITE_5, 5),
	DIVINE_BASTION1(POTION, ItemID._1DOSEDIVINEBASTION, 1),
	DIVINE_BASTION2(POTION, ItemID._2DOSEDIVINEBASTION, 2),
	DIVINE_BASTION3(POTION, ItemID._3DOSEDIVINEBASTION, 3),
	DIVINE_BASTION4(POTION, ItemID._4DOSEDIVINEBASTION, 4),
	DIVINE_BATTLEMAGE1(POTION, ItemID._1DOSEDIVINEBATTLEMAGE, 1),
	DIVINE_BATTLEMAGE2(POTION, ItemID._2DOSEDIVINEBATTLEMAGE, 2),
	DIVINE_BATTLEMAGE3(POTION, ItemID._3DOSEDIVINEBATTLEMAGE, 3),
	DIVINE_BATTLEMAGE4(POTION, ItemID._4DOSEDIVINEBATTLEMAGE, 4),
	DIVINE_MAGIC1(POTION, ItemID._1DOSEDIVINEMAGIC, 1),
	DIVINE_MAGIC2(POTION, ItemID._2DOSEDIVINEMAGIC, 2),
	DIVINE_MAGIC3(POTION, ItemID._3DOSEDIVINEMAGIC, 3),
	DIVINE_MAGIC4(POTION, ItemID._4DOSEDIVINEMAGIC, 4),
	DIVINE_RANGING1(POTION, ItemID._1DOSEDIVINERANGE, 1),
	DIVINE_RANGING2(POTION, ItemID._2DOSEDIVINERANGE, 2),
	DIVINE_RANGING3(POTION, ItemID._3DOSEDIVINERANGE, 3),
	DIVINE_RANGING4(POTION, ItemID._4DOSEDIVINERANGE, 4),
	DIVINE_SUPER_ATTACK1(POTION, ItemID._1DOSEDIVINEATTACK, 1),
	DIVINE_SUPER_ATTACK2(POTION, ItemID._2DOSEDIVINEATTACK, 2),
	DIVINE_SUPER_ATTACK3(POTION, ItemID._3DOSEDIVINEATTACK, 3),
	DIVINE_SUPER_ATTACK4(POTION, ItemID._4DOSEDIVINEATTACK, 4),
	DIVINE_SUPER_COMBAT1(POTION, ItemID._1DOSEDIVINECOMBAT, 1),
	DIVINE_SUPER_COMBAT2(POTION, ItemID._2DOSEDIVINECOMBAT, 2),
	DIVINE_SUPER_COMBAT3(POTION, ItemID._3DOSEDIVINECOMBAT, 3),
	DIVINE_SUPER_COMBAT4(POTION, ItemID._4DOSEDIVINECOMBAT, 4),
	DIVINE_SUPER_DEFENCE1(POTION, ItemID._1DOSEDIVINEDEFENCE, 1),
	DIVINE_SUPER_DEFENCE2(POTION, ItemID._2DOSEDIVINEDEFENCE, 2),
	DIVINE_SUPER_DEFENCE3(POTION, ItemID._3DOSEDIVINEDEFENCE, 3),
	DIVINE_SUPER_DEFENCE4(POTION, ItemID._4DOSEDIVINEDEFENCE, 4),
	DIVINE_SUPER_STRENGTH1(POTION, ItemID._1DOSEDIVINESTRENGTH, 1),
	DIVINE_SUPER_STRENGTH2(POTION, ItemID._2DOSEDIVINESTRENGTH, 2),
	DIVINE_SUPER_STRENGTH3(POTION, ItemID._3DOSEDIVINESTRENGTH, 3),
	DIVINE_SUPER_STRENGTH4(POTION, ItemID._4DOSEDIVINESTRENGTH, 4),
	EGNIOL1(POTION, ItemID.GAUNTLET_POTION_1, 1),
	EGNIOL2(POTION, ItemID.GAUNTLET_POTION_2, 2),
	EGNIOL3(POTION, ItemID.GAUNTLET_POTION_3, 3),
	EGNIOL4(POTION, ItemID.GAUNTLET_POTION_4, 4),
	ELYRE1(TELEPORT, ItemID.MAGIC_STRUNG_LYRE, 1),
	ELYRE2(TELEPORT, ItemID.MAGIC_STRUNG_LYRE_2, 2),
	ELYRE3(TELEPORT, ItemID.MAGIC_STRUNG_LYRE_3, 3),
	ELYRE4(TELEPORT, ItemID.MAGIC_STRUNG_LYRE_4, 4),
	ELYRE5(TELEPORT, ItemID.MAGIC_STRUNG_LYRE_5, 5),
	ENERGY1(POTION, ItemID._1DOSE1ENERGY, 1),
	ENERGY2(POTION, ItemID._2DOSE1ENERGY, 2),
	ENERGY3(POTION, ItemID._3DOSE1ENERGY, 3),
	ENERGY4(POTION, ItemID._4DOSE1ENERGY, 4),
	ENERGY_MIX_1(POTION, ItemID.BRUTAL_1DOSE1ENERGY, 1),
	ENERGY_MIX_2(POTION, ItemID.BRUTAL_2DOSE1ENERGY, 2),
	EXTENDED_ANTIFI1(POTION, ItemID._1DOSE2ANTIDRAGON, 1),
	EXTENDED_ANTIFI2(POTION, ItemID._2DOSE2ANTIDRAGON, 2),
	EXTENDED_ANTIFI3(POTION, ItemID._3DOSE2ANTIDRAGON, 3),
	EXTENDED_ANTIFI4(POTION, ItemID._4DOSE2ANTIDRAGON, 4),
	EXTENDED_ANTIFIRE_MIX_1(POTION, ItemID.BRUTAL_1DOSE2ANTIDRAGON, 1),
	EXTENDED_ANTIFIRE_MIX_2(POTION, ItemID.BRUTAL_2DOSE2ANTIDRAGON, 2),
	EXTENDED_SUPER_ANTI1(POTION, ItemID._1DOSE4ANTIDRAGON, 1),
	EXTENDED_SUPER_ANTI2(POTION, ItemID._2DOSE4ANTIDRAGON, 2),
	EXTENDED_SUPER_ANTI3(POTION, ItemID._3DOSE4ANTIDRAGON, 3),
	EXTENDED_SUPER_ANTI4(POTION, ItemID._4DOSE4ANTIDRAGON, 4),
	EXTENDED_SUPER_ANTIFIRE_MIX_1(POTION, ItemID.BRUTAL_1DOSE4ANTIDRAGON, 1),
	EXTENDED_SUPER_ANTIFIRE_MIX_2(POTION, ItemID.BRUTAL_2DOSE4ANTIDRAGON, 2),
	FISHING1(POTION, ItemID._1DOSEFISHERSPOTION, 1),
	FISHING2(POTION, ItemID._2DOSEFISHERSPOTION, 2),
	FISHING3(POTION, ItemID._3DOSEFISHERSPOTION, 3),
	FISHING4(POTION, ItemID._4DOSEFISHERSPOTION, 4),
	FISHING_MIX_1(POTION, ItemID.BRUTAL_1DOSEFISHERSPOTION, 1),
	FISHING_MIX_2(POTION, ItemID.BRUTAL_2DOSEFISHERSPOTION, 2),
	FUNGICIDE0(FUNGICIDE_SPRAY, ItemID.SLAYER_SPRAY_PUMP_0, 0),
	FUNGICIDE1(FUNGICIDE_SPRAY, ItemID.SLAYER_SPRAY_PUMP_1, 1),
	FUNGICIDE2(FUNGICIDE_SPRAY, ItemID.SLAYER_SPRAY_PUMP_2, 2),
	FUNGICIDE3(FUNGICIDE_SPRAY, ItemID.SLAYER_SPRAY_PUMP_3, 3),
	FUNGICIDE4(FUNGICIDE_SPRAY, ItemID.SLAYER_SPRAY_PUMP_4, 4),
	FUNGICIDE5(FUNGICIDE_SPRAY, ItemID.SLAYER_SPRAY_PUMP_5, 5),
	FUNGICIDE6(FUNGICIDE_SPRAY, ItemID.SLAYER_SPRAY_PUMP_6, 6),
	FUNGICIDE7(FUNGICIDE_SPRAY, ItemID.SLAYER_SPRAY_PUMP_7, 7),
	FUNGICIDE8(FUNGICIDE_SPRAY, ItemID.SLAYER_SPRAY_PUMP_8, 8),
	FUNGICIDE9(FUNGICIDE_SPRAY, ItemID.SLAYER_SPRAY_PUMP_9, 9),
	FUNGICIDE10(FUNGICIDE_SPRAY, ItemID.SLAYER_SPRAY_PUMP_10, 10),
	GAMES1(TELEPORT, ItemID.NECKLACE_OF_MINIGAMES_1, 1),
	GAMES2(TELEPORT, ItemID.NECKLACE_OF_MINIGAMES_2, 2),
	GAMES3(TELEPORT, ItemID.NECKLACE_OF_MINIGAMES_3, 3),
	GAMES4(TELEPORT, ItemID.NECKLACE_OF_MINIGAMES_4, 4),
	GAMES5(TELEPORT, ItemID.NECKLACE_OF_MINIGAMES_5, 5),
	GAMES6(TELEPORT, ItemID.NECKLACE_OF_MINIGAMES_6, 6),
	GAMES7(TELEPORT, ItemID.NECKLACE_OF_MINIGAMES_7, 7),
	GAMES8(TELEPORT, ItemID.NECKLACE_OF_MINIGAMES_8, 8),
	GLORY1(TELEPORT, ItemID.AMULET_OF_GLORY_1, 1),
	GLORY2(TELEPORT, ItemID.AMULET_OF_GLORY_2, 2),
	GLORY3(TELEPORT, ItemID.AMULET_OF_GLORY_3, 3),
	GLORY4(TELEPORT, ItemID.AMULET_OF_GLORY_4, 4),
	GLORY5(TELEPORT, ItemID.AMULET_OF_GLORY_5, 5),
	GLORY6(TELEPORT, ItemID.AMULET_OF_GLORY_6, 6),
	GLORYT1(TELEPORT, ItemID.TRAIL_AMULET_OF_GLORY_1, 1),
	GLORYT2(TELEPORT, ItemID.TRAIL_AMULET_OF_GLORY_2, 2),
	GLORYT3(TELEPORT, ItemID.TRAIL_AMULET_OF_GLORY_3, 3),
	GLORYT4(TELEPORT, ItemID.TRAIL_AMULET_OF_GLORY_4, 4),
	GLORYT5(TELEPORT, ItemID.TRAIL_AMULET_OF_GLORY_5, 5),
	GLORYT6(TELEPORT, ItemID.TRAIL_AMULET_OF_GLORY_6, 6),
	GREST1(GUTHIX_REST, ItemID.CUP_GUTHIX_REST_1, 1),
	GREST2(GUTHIX_REST, ItemID.CUP_GUTHIX_REST_2, 2),
	GREST3(GUTHIX_REST, ItemID.CUP_GUTHIX_REST_3, 3),
	GREST4(GUTHIX_REST, ItemID.CUP_GUTHIX_REST_4, 4),
	GUTHIX_BAL1(POTION, ItemID.BURGH_GUTHIX_BALANCE_1, 1),
	GUTHIX_BAL2(POTION, ItemID.BURGH_GUTHIX_BALANCE_2, 2),
	GUTHIX_BAL3(POTION, ItemID.BURGH_GUTHIX_BALANCE_3, 3),
	GUTHIX_BAL4(POTION, ItemID.BURGH_GUTHIX_BALANCE_4, 4),
	HUNTER1(POTION, ItemID._1DOSEHUNTING, 1),
	HUNTER2(POTION, ItemID._2DOSEHUNTING, 2),
	HUNTER3(POTION, ItemID._3DOSEHUNTING, 3),
	HUNTER4(POTION, ItemID._4DOSEHUNTING, 4),
	HUNTING_MIX_1(POTION, ItemID.BRUTAL_1DOSE1HUNTING, 1),
	HUNTING_MIX_2(POTION, ItemID.BRUTAL_2DOSE1HUNTING, 2),
	IMP_IN_A_BOX1(IMPBOX, ItemID.MAGIC_IMP_BOX_HALF, 1),
	IMP_IN_A_BOX2(IMPBOX, ItemID.MAGIC_IMP_BOX_FULL, 2),
	MAGIC1(POTION, ItemID._1DOSE1MAGIC, 1),
	MAGIC2(POTION, ItemID._2DOSE1MAGIC, 2),
	MAGIC3(POTION, ItemID._3DOSE1MAGIC, 3),
	MAGIC4(POTION, ItemID._4DOSE1MAGIC, 4),
	MAGIC_MIX_1(POTION, ItemID.BRUTAL_1DOSE1MAGIC, 1),
	MAGIC_MIX_2(POTION, ItemID.BRUTAL_2DOSE1MAGIC, 2),
	MAGIC_ESS1(POTION, ItemID._1DOSEMAGICESS, 1),
	MAGIC_ESS2(POTION, ItemID._2DOSEMAGICESS, 2),
	MAGIC_ESS3(POTION, ItemID._3DOSEMAGICESS, 3),
	MAGIC_ESS4(POTION, ItemID._4DOSEMAGICESS, 4),
	MAGIC_ESSENCE_MIX_1(POTION, ItemID.BRUTAL_1DOSEMAGICESS, 1),
	MAGIC_ESSENCE_MIX_2(POTION, ItemID.BRUTAL_2DOSEMAGICESS, 2),
	MOONLIGHT1(POTION, ItemID._1DOSEMOONLIGHTPOTION, 1),
	MOONLIGHT2(POTION, ItemID._2DOSEMOONLIGHTPOTION, 2),
	MOONLIGHT3(POTION, ItemID._3DOSEMOONLIGHTPOTION, 3),
	MOONLIGHT4(POTION, ItemID._4DOSEMOONLIGHTPOTION, 4),
	MOONLIGHT_MOTH_MIX1(POTION, ItemID.HUNTER_MIX_MOONMOTH_1DOSE, 1),
	MOONLIGHT_MOTH_MIX2(POTION, ItemID.HUNTER_MIX_MOONMOTH_2DOSE, 2),
	OVERLOAD1(POTION, ItemID.NZONE1DOSEOVERLOADPOTION, 1),
	OVERLOAD2(POTION, ItemID.NZONE2DOSEOVERLOADPOTION, 2),
	OVERLOAD3(POTION, ItemID.NZONE3DOSEOVERLOADPOTION, 3),
	OVERLOAD4(POTION, ItemID.NZONE4DOSEOVERLOADPOTION, 4),
	PASSAGE1(TELEPORT, ItemID.NECKLACE_OF_PASSAGE_1, 1),
	PASSAGE2(TELEPORT, ItemID.NECKLACE_OF_PASSAGE_2, 2),
	PASSAGE3(TELEPORT, ItemID.NECKLACE_OF_PASSAGE_3, 3),
	PASSAGE4(TELEPORT, ItemID.NECKLACE_OF_PASSAGE_4, 4),
	PASSAGE5(TELEPORT, ItemID.NECKLACE_OF_PASSAGE_5, 5),
	PRAYER1(POTION, ItemID._1DOSEPRAYERRESTORE, 1),
	PRAYER2(POTION, ItemID._2DOSEPRAYERRESTORE, 2),
	PRAYER3(POTION, ItemID._3DOSEPRAYERRESTORE, 3),
	PRAYER4(POTION, ItemID._4DOSEPRAYERRESTORE, 4),
	PRAYER_MIX_1(POTION, ItemID.BRUTAL_1DOSEPRAYERRESTORE, 1),
	PRAYER_MIX_2(POTION, ItemID.BRUTAL_2DOSEPRAYERRESTORE, 2),
	RANGING1(POTION, ItemID._1DOSERANGERSPOTION, 1),
	RANGING2(POTION, ItemID._2DOSERANGERSPOTION, 2),
	RANGING3(POTION, ItemID._3DOSERANGERSPOTION, 3),
	RANGING4(POTION, ItemID._4DOSERANGERSPOTION, 4),
	RANGING_MIX_1(POTION, ItemID.BRUTAL_1DOSERANGERSPOTION, 1),
	RANGING_MIX_2(POTION, ItemID.BRUTAL_2DOSERANGERSPOTION, 2),
	RELICYMS1(POTION, ItemID.RELICYMS_BALM1, 1),
	RELICYMS2(POTION, ItemID.RELICYMS_BALM2, 2),
	RELICYMS3(POTION, ItemID.RELICYMS_BALM3, 3),
	RELICYMS4(POTION, ItemID.RELICYMS_BALM4, 4),
	RELICYMS_MIX_1(POTION, ItemID.BRUTAL_RELICYMS_BALM1, 1),
	RELICYMS_MIX_2(POTION, ItemID.BRUTAL_RELICYMS_BALM2, 2),
	RESTORE1(POTION, ItemID._1DOSESTATRESTORE, 1),
	RESTORE2(POTION, ItemID._2DOSESTATRESTORE, 2),
	RESTORE3(POTION, ItemID._3DOSESTATRESTORE, 3),
	RESTORE4(POTION, ItemID._4DOSESTATRESTORE, 4),
	RESTORE_MIX_1(POTION, ItemID.BRUTAL_1DOSESTATRESTORE, 1),
	RESTORE_MIX_2(POTION, ItemID.BRUTAL_2DOSESTATRESTORE, 2),
	RETURNING1(TELEPORT, ItemID.RING_OF_RETURNING_1, 1),
	RETURNING2(TELEPORT, ItemID.RING_OF_RETURNING_2, 2),
	RETURNING3(TELEPORT, ItemID.RING_OF_RETURNING_3, 3),
	RETURNING4(TELEPORT, ItemID.RING_OF_RETURNING_4, 4),
	RETURNING5(TELEPORT, ItemID.RING_OF_RETURNING_5, 5),
	ROD1(TELEPORT, ItemID.RING_OF_DUELING_1, 1),
	ROD2(TELEPORT, ItemID.RING_OF_DUELING_2, 2),
	ROD3(TELEPORT, ItemID.RING_OF_DUELING_3, 3),
	ROD4(TELEPORT, ItemID.RING_OF_DUELING_4, 4),
	ROD5(TELEPORT, ItemID.RING_OF_DUELING_5, 5),
	ROD6(TELEPORT, ItemID.RING_OF_DUELING_6, 6),
	ROD7(TELEPORT, ItemID.RING_OF_DUELING_7, 7),
	ROD8(TELEPORT, ItemID.RING_OF_DUELING_8, 8),
	ROS1(TELEPORT, ItemID.SLAYER_RING_1, 1),
	ROS2(TELEPORT, ItemID.SLAYER_RING_2, 2),
	ROS3(TELEPORT, ItemID.SLAYER_RING_3, 3),
	ROS4(TELEPORT, ItemID.SLAYER_RING_4, 4),
	ROS5(TELEPORT, ItemID.SLAYER_RING_5, 5),
	ROS6(TELEPORT, ItemID.SLAYER_RING_6, 6),
	ROS7(TELEPORT, ItemID.SLAYER_RING_7, 7),
	ROS8(TELEPORT, ItemID.SLAYER_RING_8, 8),
	ROW1(TELEPORT, ItemID.RING_OF_WEALTH_1, 1),
	ROW2(TELEPORT, ItemID.RING_OF_WEALTH_2, 2),
	ROW3(TELEPORT, ItemID.RING_OF_WEALTH_3, 3),
	ROW4(TELEPORT, ItemID.RING_OF_WEALTH_4, 4),
	ROW5(TELEPORT, ItemID.RING_OF_WEALTH_5, 5),
	ROWI1(TELEPORT, ItemID.RING_OF_WEALTH_I1, 1),
	ROWI2(TELEPORT, ItemID.RING_OF_WEALTH_I2, 2),
	ROWI3(TELEPORT, ItemID.RING_OF_WEALTH_I3, 3),
	ROWI4(TELEPORT, ItemID.RING_OF_WEALTH_I4, 4),
	ROWI5(TELEPORT, ItemID.RING_OF_WEALTH_I5, 5),
	RUBY_HARVEST_MIX1(POTION, ItemID.HUNTER_MIX_RUBY_1DOSE, 1),
	RUBY_HARVEST_MIX2(POTION, ItemID.HUNTER_MIX_RUBY_2DOSE, 2),
	SACK_CABBAGES1(SACK, ItemID.SACK_CABBAGE_1, 1),
	SACK_CABBAGES2(SACK, ItemID.SACK_CABBAGE_2, 2),
	SACK_CABBAGES3(SACK, ItemID.SACK_CABBAGE_3, 3),
	SACK_CABBAGES4(SACK, ItemID.SACK_CABBAGE_4, 4),
	SACK_CABBAGES5(SACK, ItemID.SACK_CABBAGE_5, 5),
	SACK_CABBAGES6(SACK, ItemID.SACK_CABBAGE_6, 6),
	SACK_CABBAGES7(SACK, ItemID.SACK_CABBAGE_7, 7),
	SACK_CABBAGES8(SACK, ItemID.SACK_CABBAGE_8, 8),
	SACK_CABBAGES9(SACK, ItemID.SACK_CABBAGE_9, 9),
	SACK_CABBAGES10(SACK, ItemID.SACK_CABBAGE_10, 10),
	SACK_ONIONS1(SACK, ItemID.SACK_ONION_1, 1),
	SACK_ONIONS2(SACK, ItemID.SACK_ONION_2, 2),
	SACK_ONIONS3(SACK, ItemID.SACK_ONION_3, 3),
	SACK_ONIONS4(SACK, ItemID.SACK_ONION_4, 4),
	SACK_ONIONS5(SACK, ItemID.SACK_ONION_5, 5),
	SACK_ONIONS6(SACK, ItemID.SACK_ONION_6, 6),
	SACK_ONIONS7(SACK, ItemID.SACK_ONION_7, 7),
	SACK_ONIONS8(SACK, ItemID.SACK_ONION_8, 8),
	SACK_ONIONS9(SACK, ItemID.SACK_ONION_9, 9),
	SACK_ONIONS10(SACK, ItemID.SACK_ONION_10, 10),
	SACK_POTATOES1(SACK, ItemID.SACK_POTATO_1, 1),
	SACK_POTATOES2(SACK, ItemID.SACK_POTATO_2, 2),
	SACK_POTATOES3(SACK, ItemID.SACK_POTATO_3, 3),
	SACK_POTATOES4(SACK, ItemID.SACK_POTATO_4, 4),
	SACK_POTATOES5(SACK, ItemID.SACK_POTATO_5, 5),
	SACK_POTATOES6(SACK, ItemID.SACK_POTATO_6, 6),
	SACK_POTATOES7(SACK, ItemID.SACK_POTATO_7, 7),
	SACK_POTATOES8(SACK, ItemID.SACK_POTATO_8, 8),
	SACK_POTATOES9(SACK, ItemID.SACK_POTATO_9, 9),
	SACK_POTATOES10(SACK, ItemID.SACK_POTATO_10, 10),
	SANFEW1(POTION, ItemID.SANFEW_SALVE_1_DOSE, 1),
	SANFEW2(POTION, ItemID.SANFEW_SALVE_2_DOSE, 2),
	SANFEW3(POTION, ItemID.SANFEW_SALVE_3_DOSE, 3),
	SANFEW4(POTION, ItemID.SANFEW_SALVE_4_DOSE, 4),
	SAPPHIRE_GLACIALIS_MIX1(POTION, ItemID.HUNTER_MIX_GLACIALIS_1DOSE, 1),
	SAPPHIRE_GLACIALIS_MIX2(POTION, ItemID.HUNTER_MIX_GLACIALIS_2DOSE, 2),
	SARADOMIN_BR1(POTION, ItemID._1DOSEPOTIONOFSARADOMIN, 1),
	SARADOMIN_BR2(POTION, ItemID._2DOSEPOTIONOFSARADOMIN, 2),
	SARADOMIN_BR3(POTION, ItemID._3DOSEPOTIONOFSARADOMIN, 3),
	SARADOMIN_BR4(POTION, ItemID._4DOSEPOTIONOFSARADOMIN, 4),
	SERUM_2071(POTION, ItemID.MORT_SERUM1, 1),
	SERUM_2072(POTION, ItemID.MORT_SERUM2, 2),
	SERUM_2073(POTION, ItemID.MORT_SERUM3, 3),
	SERUM_2074(POTION, ItemID.MORT_SERUM4, 4),
	SERUM_2081(POTION, ItemID.MORT_SERUM_PERM1, 1),
	SERUM_2082(POTION, ItemID.MORT_SERUM_PERM2, 2),
	SERUM_2083(POTION, ItemID.MORT_SERUM_PERM3, 3),
	SERUM_2084(POTION, ItemID.MORT_SERUM_PERM4, 4),
	SKILLS1(TELEPORT, ItemID.JEWL_NECKLACE_OF_SKILLS_1, 1),
	SKILLS2(TELEPORT, ItemID.JEWL_NECKLACE_OF_SKILLS_2, 2),
	SKILLS3(TELEPORT, ItemID.JEWL_NECKLACE_OF_SKILLS_3, 3),
	SKILLS4(TELEPORT, ItemID.JEWL_NECKLACE_OF_SKILLS_4, 4),
	SKILLS5(TELEPORT, ItemID.JEWL_NECKLACE_OF_SKILLS_5, 5),
	SKILLS6(TELEPORT, ItemID.JEWL_NECKLACE_OF_SKILLS_6, 6),
	SNOWY_KNIGHT_MIX1(POTION, ItemID.HUNTER_MIX_SNOWY_1DOSE, 1),
	SNOWY_KNIGHT_MIX2(POTION, ItemID.HUNTER_MIX_SNOWY_2DOSE, 2),
	STAMINA1(POTION, ItemID._1DOSESTAMINA, 1),
	STAMINA2(POTION, ItemID._2DOSESTAMINA, 2),
	STAMINA3(POTION, ItemID._3DOSESTAMINA, 3),
	STAMINA4(POTION, ItemID._4DOSESTAMINA, 4),
	STAMINA_MIX_1(POTION, ItemID.BRUTAL_1DOSESTAMINA, 1),
	STAMINA_MIX_2(POTION, ItemID.BRUTAL_2DOSESTAMINA, 2),
	STRENGTH1(POTION, ItemID._1DOSE1STRENGTH, 1),
	STRENGTH2(POTION, ItemID._2DOSE1STRENGTH, 2),
	STRENGTH3(POTION, ItemID._3DOSE1STRENGTH, 3),
	STRENGTH4(POTION, ItemID.STRENGTH4, 4),
	STRENGTH_MIX_1(POTION, ItemID.BRUTAL_1DOSE1STRENGTH, 1),
	STRENGTH_MIX_2(POTION, ItemID.BRUTAL_2DOSE1STRENGTH, 2),
	SUNLIGHT_MOTH_MIX1(POTION, ItemID.HUNTER_MIX_SUNMOTH_1DOSE, 1),
	SUNLIGHT_MOTH_MIX2(POTION, ItemID.HUNTER_MIX_SUNMOTH_2DOSE, 2),
	SUPERANTI1(POTION, ItemID._1DOSE2ANTIPOISON, 1),
	SUPERANTI2(POTION, ItemID._2DOSE2ANTIPOISON, 2),
	SUPERANTI3(POTION, ItemID._3DOSE2ANTIPOISON, 3),
	SUPERANTI4(POTION, ItemID._4DOSE2ANTIPOISON, 4),
	ANTIPOISON_SUPERMIX_1(POTION, ItemID.BRUTAL_1DOSE2ANTIPOISON, 1),
	ANTIPOISON_SUPERMIX_2(POTION, ItemID.BRUTAL_2DOSE2ANTIPOISON, 2),
	SUPER_ANTIFIRE1(POTION, ItemID._1DOSE3ANTIDRAGON, 1),
	SUPER_ANTIFIRE2(POTION, ItemID._2DOSE3ANTIDRAGON, 2),
	SUPER_ANTIFIRE3(POTION, ItemID._3DOSE3ANTIDRAGON, 3),
	SUPER_ANTIFIRE4(POTION, ItemID._4DOSE3ANTIDRAGON, 4),
	SUPER_ANTIFIRE_MIX_1(POTION, ItemID.BRUTAL_1DOSE3ANTIDRAGON, 1),
	SUPER_ANTIFIRE_MIX_2(POTION, ItemID.BRUTAL_2DOSE3ANTIDRAGON, 2),
	SUPER_ATT1(POTION, ItemID._1DOSE2ATTACK, 1),
	SUPER_ATT2(POTION, ItemID._2DOSE2ATTACK, 2),
	SUPER_ATT3(POTION, ItemID._3DOSE2ATTACK, 3),
	SUPER_ATT4(POTION, ItemID._4DOSE2ATTACK, 4),
	SUPER_ATTACK_MIX1(POTION, ItemID.BRUTAL_1DOSE2ATTACK, 1),
	SUPER_ATTACK_MIX2(POTION, ItemID.BRUTAL_2DOSE2ATTACK, 2),
	SUPER_COMB1(POTION, ItemID._1DOSE2COMBAT, 1),
	SUPER_COMB2(POTION, ItemID._2DOSE2COMBAT, 2),
	SUPER_COMB3(POTION, ItemID._3DOSE2COMBAT, 3),
	SUPER_COMB4(POTION, ItemID._4DOSE2COMBAT, 4),
	SUPER_DEF1(POTION, ItemID._1DOSE2DEFENSE, 1),
	SUPER_DEF2(POTION, ItemID._2DOSE2DEFENSE, 2),
	SUPER_DEF3(POTION, ItemID._3DOSE2DEFENSE, 3),
	SUPER_DEF4(POTION, ItemID._4DOSE2DEFENSE, 4),
	SUPER_DEF_MIX_1(POTION, ItemID.BRUTAL_1DOSE2DEFENSE, 1),
	SUPER_DEF_MIX_2(POTION, ItemID.BRUTAL_2DOSE2DEFENSE, 2),
	SUPER_ENERG1(POTION, ItemID._1DOSE2ENERGY, 1),
	SUPER_ENERG2(POTION, ItemID._2DOSE2ENERGY, 2),
	SUPER_ENERG3(POTION, ItemID._3DOSE2ENERGY, 3),
	SUPER_ENERG4(POTION, ItemID._4DOSE2ENERGY, 4),
	SUPER_ENERGY_MIX_1(POTION, ItemID.BRUTAL_1DOSE2ENERGY, 1),
	SUPER_ENERGY_MIX_2(POTION, ItemID.BRUTAL_2DOSE2ENERGY, 2),
	SUPER_MAG1(POTION, ItemID.NZONE1DOSE2MAGICPOTION, 1),
	SUPER_MAG2(POTION, ItemID.NZONE2DOSE2MAGICPOTION, 2),
	SUPER_MAG3(POTION, ItemID.NZONE3DOSE2MAGICPOTION, 3),
	SUPER_MAG4(POTION, ItemID.NZONE4DOSE2MAGICPOTION, 4),
	SUPER_RANG1(POTION, ItemID.NZONE1DOSE2RANGERSPOTION, 1),
	SUPER_RANG2(POTION, ItemID.NZONE2DOSE2RANGERSPOTION, 2),
	SUPER_RANG3(POTION, ItemID.NZONE3DOSE2RANGERSPOTION, 3),
	SUPER_RANG4(POTION, ItemID.NZONE4DOSE2RANGERSPOTION, 4),
	SUPER_REST1(POTION, ItemID._1DOSE2RESTORE, 1),
	SUPER_REST2(POTION, ItemID._2DOSE2RESTORE, 2),
	SUPER_REST3(POTION, ItemID._3DOSE2RESTORE, 3),
	SUPER_REST4(POTION, ItemID._4DOSE2RESTORE, 4),
	SUPER_RESTORE_MIX_1(POTION, ItemID.BRUTAL_1DOSE2RESTORE, 1),
	SUPER_RESTORE_MIX_2(POTION, ItemID.BRUTAL_2DOSE2RESTORE, 2),
	SUPER_STR1(POTION, ItemID._1DOSE2STRENGTH, 1),
	SUPER_STR2(POTION, ItemID._2DOSE2STRENGTH, 2),
	SUPER_STR3(POTION, ItemID._3DOSE2STRENGTH, 3),
	SUPER_STR4(POTION, ItemID._4DOSE2STRENGTH, 4),
	SUPER_STR_MIX_1(POTION, ItemID.BRUTAL_1DOSE2STRENGTH, 1),
	SUPER_STR_MIX_2(POTION, ItemID.BRUTAL_2DOSE2STRENGTH, 2),
	SURGE1(POTION, ItemID._1DOSESURGE, 1),
	SURGE2(POTION, ItemID._2DOSESURGE, 2),
	SURGE3(POTION, ItemID._3DOSESURGE, 3),
	SURGE4(POTION, ItemID._4DOSESURGE, 4),
	TCRYSTAL1(TELEPORT, ItemID.MOURNING_TELEPORT_CRYSTAL_1, 1),
	TCRYSTAL2(TELEPORT, ItemID.MOURNING_TELEPORT_CRYSTAL_2, 2),
	TCRYSTAL3(TELEPORT, ItemID.MOURNING_TELEPORT_CRYSTAL_3, 3),
	TCRYSTAL4(TELEPORT, ItemID.MOURNING_TELEPORT_CRYSTAL_4, 4),
	TCRYSTAL5(TELEPORT, ItemID.MOURNING_TELEPORT_CRYSTAL_5, 5),
	WCAN0(WATERCAN, ItemID.WATERING_CAN_0, 0),
	WCAN1(WATERCAN, ItemID.WATERING_CAN_1, 1),
	WCAN2(WATERCAN, ItemID.WATERING_CAN_2, 2),
	WCAN3(WATERCAN, ItemID.WATERING_CAN_3, 3),
	WCAN4(WATERCAN, ItemID.WATERING_CAN_4, 4),
	WCAN5(WATERCAN, ItemID.WATERING_CAN_5, 5),
	WCAN6(WATERCAN, ItemID.WATERING_CAN_6, 6),
	WCAN7(WATERCAN, ItemID.WATERING_CAN_7, 7),
	WCAN8(WATERCAN, ItemID.WATERING_CAN_8, 8),
	WSKIN0(WATERSKIN, ItemID.WATER_SKIN0, 0),
	WSKIN1(WATERSKIN, ItemID.WATER_SKIN1, 1),
	WSKIN2(WATERSKIN, ItemID.WATER_SKIN2, 2),
	WSKIN3(WATERSKIN, ItemID.WATER_SKIN3, 3),
	WSKIN4(WATERSKIN, ItemID.WATER_SKIN4, 4),
	ZAMORAK_BR1(POTION, ItemID._1DOSEPOTIONOFZAMORAK, 1),
	ZAMORAK_BR2(POTION, ItemID._2DOSEPOTIONOFZAMORAK, 2),
	ZAMORAK_BR3(POTION, ItemID._3DOSEPOTIONOFZAMORAK, 3),
	ZAMORAK_BR4(POTION, ItemID._4DOSEPOTIONOFZAMORAK, 4),
	ZAMORAK_MIX_1(POTION, ItemID.BRUTAL_1DOSEPOTIONOFZAMORAK, 1),
	ZAMORAK_MIX_2(POTION, ItemID.BRUTAL_2DOSEPOTIONOFZAMORAK, 2),
	ELDER_MIN1(POTION, ItemID.RAIDS_VIAL_ELDER_WEAK_1, 1),
	ELDER_MIN2(POTION, ItemID.RAIDS_VIAL_ELDER_WEAK_2, 2),
	ELDER_MIN3(POTION, ItemID.RAIDS_VIAL_ELDER_WEAK_3, 3),
	ELDER_MIN4(POTION, ItemID.RAIDS_VIAL_ELDER_WEAK_4, 4),
	ELDER1(POTION, ItemID.RAIDS_VIAL_ELDER_1, 1),
	ELDER2(POTION, ItemID.RAIDS_VIAL_ELDER_2, 2),
	ELDER3(POTION, ItemID.RAIDS_VIAL_ELDER_3, 3),
	ELDER4(POTION, ItemID.RAIDS_VIAL_ELDER_4, 4),
	ELDER_MAX1(POTION, ItemID.RAIDS_VIAL_ELDER_STRONG_1, 1),
	ELDER_MAX2(POTION, ItemID.RAIDS_VIAL_ELDER_STRONG_2, 2),
	ELDER_MAX3(POTION, ItemID.RAIDS_VIAL_ELDER_STRONG_3, 3),
	ELDER_MAX4(POTION, ItemID.RAIDS_VIAL_ELDER_STRONG_4, 4),
	KODAI_MIN1(POTION, ItemID.RAIDS_VIAL_KODAI_WEAK_1, 1),
	KODAI_MIN2(POTION, ItemID.RAIDS_VIAL_KODAI_WEAK_2, 2),
	KODAI_MIN3(POTION, ItemID.RAIDS_VIAL_KODAI_WEAK_3, 3),
	KODAI_MIN4(POTION, ItemID.RAIDS_VIAL_KODAI_WEAK_4, 4),
	KODAI1(POTION, ItemID.RAIDS_VIAL_KODAI_1, 1),
	KODAI2(POTION, ItemID.RAIDS_VIAL_KODAI_2, 2),
	KODAI3(POTION, ItemID.RAIDS_VIAL_KODAI_3, 3),
	KODAI4(POTION, ItemID.RAIDS_VIAL_KODAI_4, 4),
	KODAI_MAX1(POTION, ItemID.RAIDS_VIAL_KODAI_STRONG_1, 1),
	KODAI_MAX2(POTION, ItemID.RAIDS_VIAL_KODAI_STRONG_2, 2),
	KODAI_MAX3(POTION, ItemID.RAIDS_VIAL_KODAI_STRONG_3, 3),
	KODAI_MAX4(POTION, ItemID.RAIDS_VIAL_KODAI_STRONG_4, 4),
	TWISTED_MIN1(POTION, ItemID.RAIDS_VIAL_TWISTED_WEAK_1, 1),
	TWISTED_MIN2(POTION, ItemID.RAIDS_VIAL_TWISTED_WEAK_2, 2),
	TWISTED_MIN3(POTION, ItemID.RAIDS_VIAL_TWISTED_WEAK_3, 3),
	TWISTED_MIN4(POTION, ItemID.RAIDS_VIAL_TWISTED_WEAK_4, 4),
	TWISTED1(POTION, ItemID.RAIDS_VIAL_TWISTED_1, 1),
	TWISTED2(POTION, ItemID.RAIDS_VIAL_TWISTED_2, 2),
	TWISTED3(POTION, ItemID.RAIDS_VIAL_TWISTED_3, 3),
	TWISTED4(POTION, ItemID.RAIDS_VIAL_TWISTED_4, 4),
	TWISTED_MAX1(POTION, ItemID.RAIDS_VIAL_TWISTED_STRONG_1, 1),
	TWISTED_MAX2(POTION, ItemID.RAIDS_VIAL_TWISTED_STRONG_2, 2),
	TWISTED_MAX3(POTION, ItemID.RAIDS_VIAL_TWISTED_STRONG_3, 3),
	TWISTED_MAX4(POTION, ItemID.RAIDS_VIAL_TWISTED_STRONG_4, 4),
	REVITALISATION_MIN1(POTION, ItemID.RAIDS_VIAL_REVITALISATION_WEAK_1, 1),
	REVITALISATION_MIN2(POTION, ItemID.RAIDS_VIAL_REVITALISATION_WEAK_2, 2),
	REVITALISATION_MIN3(POTION, ItemID.RAIDS_VIAL_REVITALISATION_WEAK_3, 3),
	REVITALISATION_MIN4(POTION, ItemID.RAIDS_VIAL_REVITALISATION_WEAK_4, 4),
	REVITALISATION1(POTION, ItemID.RAIDS_VIAL_REVITALISATION_1, 1),
	REVITALISATION2(POTION, ItemID.RAIDS_VIAL_REVITALISATION_2, 2),
	REVITALISATION3(POTION, ItemID.RAIDS_VIAL_REVITALISATION_3, 3),
	REVITALISATION4(POTION, ItemID.RAIDS_VIAL_REVITALISATION_4, 4),
	REVITALISATION_MAX1(POTION, ItemID.RAIDS_VIAL_REVITALISATION_STRONG_1, 1),
	REVITALISATION_MAX2(POTION, ItemID.RAIDS_VIAL_REVITALISATION_STRONG_2, 2),
	REVITALISATION_MAX3(POTION, ItemID.RAIDS_VIAL_REVITALISATION_STRONG_3, 3),
	REVITALISATION_MAX4(POTION, ItemID.RAIDS_VIAL_REVITALISATION_STRONG_4, 4),
	XERICS_AID_MIN1(POTION, ItemID.RAIDS_VIAL_XERICAID_WEAK_1, 1),
	XERICS_AID_MIN2(POTION, ItemID.RAIDS_VIAL_XERICAID_WEAK_2, 2),
	XERICS_AID_MIN3(POTION, ItemID.RAIDS_VIAL_XERICAID_WEAK_3, 3),
	XERICS_AID_MIN4(POTION, ItemID.RAIDS_VIAL_XERICAID_WEAK_4, 4),
	XERICS_AID1(POTION, ItemID.RAIDS_VIAL_XERICAID_1, 1),
	XERICS_AID2(POTION, ItemID.RAIDS_VIAL_XERICAID_2, 2),
	XERICS_AID3(POTION, ItemID.RAIDS_VIAL_XERICAID_3, 3),
	XERICS_AID4(POTION, ItemID.RAIDS_VIAL_XERICAID_4, 4),
	XERICS_AID_MAX1(POTION, ItemID.RAIDS_VIAL_XERICAID_STRONG_1, 1),
	XERICS_AID_MAX2(POTION, ItemID.RAIDS_VIAL_XERICAID_STRONG_2, 2),
	XERICS_AID_MAX3(POTION, ItemID.RAIDS_VIAL_XERICAID_STRONG_3, 3),
	XERICS_AID_MAX4(POTION, ItemID.RAIDS_VIAL_XERICAID_STRONG_4, 4),
	PRAYER_ENHANCE_MIN1(POTION, ItemID.RAIDS_VIAL_PRAYER_WEAK_1, 1),
	PRAYER_ENHANCE_MIN2(POTION, ItemID.RAIDS_VIAL_PRAYER_WEAK_2, 2),
	PRAYER_ENHANCE_MIN3(POTION, ItemID.RAIDS_VIAL_PRAYER_WEAK_3, 3),
	PRAYER_ENHANCE_MIN4(POTION, ItemID.RAIDS_VIAL_PRAYER_WEAK_4, 4),
	PRAYER_ENHANCE1(POTION, ItemID.RAIDS_VIAL_PRAYER_1, 1),
	PRAYER_ENHANCE2(POTION, ItemID.RAIDS_VIAL_PRAYER_2, 2),
	PRAYER_ENHANCE3(POTION, ItemID.RAIDS_VIAL_PRAYER_3, 3),
	PRAYER_ENHANCE4(POTION, ItemID.RAIDS_VIAL_PRAYER_4, 4),
	PRAYER_ENHANCE_MAX1(POTION, ItemID.RAIDS_VIAL_PRAYER_STRONG_1, 1),
	PRAYER_ENHANCE_MAX2(POTION, ItemID.RAIDS_VIAL_PRAYER_STRONG_2, 2),
	PRAYER_ENHANCE_MAX3(POTION, ItemID.RAIDS_VIAL_PRAYER_STRONG_3, 3),
	PRAYER_ENHANCE_MAX4(POTION, ItemID.RAIDS_VIAL_PRAYER_STRONG_4, 4),
	COX_OVERLOAD_MIN1(POTION, ItemID.RAIDS_VIAL_OVERLOAD_WEAK_1, 1),
	COX_OVERLOAD_MIN2(POTION, ItemID.RAIDS_VIAL_OVERLOAD_WEAK_2, 2),
	COX_OVERLOAD_MIN3(POTION, ItemID.RAIDS_VIAL_OVERLOAD_WEAK_3, 3),
	COX_OVERLOAD_MIN4(POTION, ItemID.RAIDS_VIAL_OVERLOAD_WEAK_4, 4),
	COX_OVERLOAD1(POTION, ItemID.RAIDS_VIAL_OVERLOAD_1, 1),
	COX_OVERLOAD2(POTION, ItemID.RAIDS_VIAL_OVERLOAD_2, 2),
	COX_OVERLOAD3(POTION, ItemID.RAIDS_VIAL_OVERLOAD_3, 3),
	COX_OVERLOAD4(POTION, ItemID.RAIDS_VIAL_OVERLOAD_4, 4),
	COX_OVERLOAD_MAX1(POTION, ItemID.RAIDS_VIAL_OVERLOAD_STRONG_1, 1),
	COX_OVERLOAD_MAX2(POTION, ItemID.RAIDS_VIAL_OVERLOAD_STRONG_2, 2),
	COX_OVERLOAD_MAX3(POTION, ItemID.RAIDS_VIAL_OVERLOAD_STRONG_3, 3),
	COX_OVERLOAD_MAX4(POTION, ItemID.RAIDS_VIAL_OVERLOAD_STRONG_4, 4),
	NECTAR1(POTION, ItemID.TOA_SUPPLY_HEAL_1, 1),
	NECTAR2(POTION, ItemID.TOA_SUPPLY_HEAL_2, 2),
	NECTAR3(POTION, ItemID.TOA_SUPPLY_HEAL_3, 3),
	NECTAR4(POTION, ItemID.TOA_SUPPLY_HEAL_4, 4),
	TEARS_OF_ELIDINIS1(POTION, ItemID.TOA_SUPPLY_PRAYER_1, 1),
	TEARS_OF_ELIDINIS2(POTION, ItemID.TOA_SUPPLY_PRAYER_2, 2),
	TEARS_OF_ELIDINIS3(POTION, ItemID.TOA_SUPPLY_PRAYER_3, 3),
	TEARS_OF_ELIDINIS4(POTION, ItemID.TOA_SUPPLY_PRAYER_4, 4),
	LIQUID_ADRENALINE1(POTION, ItemID.TOA_SUPPLY_ENERGY_1, 1),
	LIQUID_ADRENALINE2(POTION, ItemID.TOA_SUPPLY_ENERGY_2, 2),
	AMBROSIA1(POTION, ItemID.TOA_SUPPLY_PANICHEAL_1, 1),
	AMBROSI2(POTION, ItemID.TOA_SUPPLY_PANICHEAL_2, 2),
	BLESSED_CRYSTAL_SCARAB1(POTION, ItemID.TOA_SUPPLY_PRAYER_OVERTIME_1, 1),
	BLESSED_CRYSTAL_SCARAB2(POTION, ItemID.TOA_SUPPLY_PRAYER_OVERTIME_2, 2),
	SILK_DRESSING1(POTION, ItemID.TOA_SUPPLY_HEAL_OVERTIME_1, 1),
	SILK_DRESSING2(POTION, ItemID.TOA_SUPPLY_HEAL_OVERTIME_2, 2),
	SMELLING_SALTS1(POTION, ItemID.TOA_SUPPLY_STATS_1, 1),
	SMELLING_SALTS2(POTION, ItemID.TOA_SUPPLY_STATS_2, 2),
	FORGOTTEN_BR1(POTION, ItemID._1DOSEFORGOTTENBREW, 1),
	FORGOTTEN_BR2(POTION, ItemID._2DOSEFORGOTTENBREW, 2),
	FORGOTTEN_BR3(POTION, ItemID._3DOSEFORGOTTENBREW, 3),
	FORGOTTEN_BR4(POTION, ItemID._4DOSEFORGOTTENBREW, 4),
	MENAPHITE_REM1(POTION, ItemID._1DOSESTATRENEWAL, 1),
	MENAPHITE_REM2(POTION, ItemID._2DOSESTATRENEWAL, 2),
	MENAPHITE_REM3(POTION, ItemID._3DOSESTATRENEWAL, 3),
	MENAPHITE_REM4(POTION, ItemID._4DOSESTATRENEWAL, 4),
	EXTENDED_ANTI_VENOM1(POTION, ItemID.EXTENDED_ANTIVENOM_1, 1),
	EXTENDED_ANTI_VENOM2(POTION, ItemID.EXTENDED_ANTIVENOM_2, 2),
	EXTENDED_ANTI_VENOM3(POTION, ItemID.EXTENDED_ANTIVENOM_3, 3),
	EXTENDED_ANTI_VENOM4(POTION, ItemID.EXTENDED_ANTIVENOM_4, 4),
	GOADING1(POTION, ItemID._1DOSEGOADING, 1),
	GOADING2(POTION, ItemID._2DOSEGOADING, 2),
	GOADING3(POTION, ItemID._3DOSEGOADING, 3),
	GOADING4(POTION, ItemID._4DOSEGOADING, 4),
	PRAYER_REGENERATION1(POTION, ItemID._1DOSE1PRAYER_REGENERATION, 1),
	PRAYER_REGENERATION2(POTION, ItemID._2DOSE1PRAYER_REGENERATION, 2),
	PRAYER_REGENERATION3(POTION, ItemID._3DOSE1PRAYER_REGENERATION, 3),
	PRAYER_REGENERATION4(POTION, ItemID._4DOSE1PRAYER_REGENERATION, 4),
	;

	private final ItemChargeType type;
	private final int id;
	private final int charges;

	private static final Map<Integer, ItemWithCharge> ID_MAP;

	static
	{
		ImmutableMap.Builder<Integer, ItemWithCharge> builder = new ImmutableMap.Builder<>();

		for (ItemWithCharge itemCharge : values())
		{
			builder.put(itemCharge.getId(), itemCharge);
		}

		ID_MAP = builder.build();
	}

	@Nullable
	static ItemWithCharge findItem(int itemId)
	{
		return ID_MAP.get(itemId);
	}

}
