/*
 * Copyright (c) 2018 Abex
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF ME<PERSON><PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.cluescrolls.clues.emote;

import lombok.Getter;
import net.runelite.api.coords.WorldPoint;
import net.runelite.api.gameval.ObjectID;

@Getter
public enum STASHUnit
{
	NEAR_A_SHED_IN_LUMBRIDGE_SWAMP(ObjectID.HH_EASY001, new WorldPoint(3201, 3171, 0)),
	ON_THE_BRIDGE_TO_THE_MISTHALIN_WIZARDS_TOWER(ObjectID.HH_EASY002, new WorldPoint(3115, 3194, 0)),
	DRAYNOR_VILLAGE_MARKET(ObjectID.HH_EASY003, new WorldPoint(3083, 3254, 0)),
	LIMESTONE_MINE(ObjectID.HH_EASY004, new WorldPoint(3373, 3498, 0)),
	OUTSIDE_THE_LEGENDS_GUILD_GATES(ObjectID.HH_EASY005, new WorldPoint(2735, 3350, 0)),
	MUDSKIPPER_POINT(ObjectID.HH_EASY006, new WorldPoint(2988, 3111, 0)),
	NEAR_THE_ENTRANA_FERRY_IN_PORT_SARIM(ObjectID.HH_EASY007, new WorldPoint(3050, 3237, 0)),
	AL_KHARID_SCORPION_MINE(ObjectID.HH_EASY008, new WorldPoint(3303, 3289, 0)),
	DRAYNOR_MANOR_BY_THE_FOUNTAIN(ObjectID.HH_EASY009, new WorldPoint(3089, 3331, 0)),
	WHEAT_FIELD_NEAR_THE_LUMBRIDGE_WINDMILL(ObjectID.HH_EASY010, new WorldPoint(3163, 3297, 0)),
	CROSSROADS_NORTH_OF_DRAYNOR_VILLAGE(ObjectID.HH_EASY011, new WorldPoint(3111, 3289, 0)),
	RIMMINGTON_MINE(ObjectID.HH_EASY012, new WorldPoint(2976, 3239, 0)),
	VARROCK_PALACE_LIBRARY(ObjectID.HH_EASY013, new WorldPoint(3214, 3490, 0)),
	UPSTAIRS_IN_THE_ARDOUGNE_WINDMILL(ObjectID.HH_EASY014, new WorldPoint(2635, 3386, 2)),
	OUTSIDE_THE_FALADOR_PARTY_ROOM(ObjectID.HH_EASY015, new WorldPoint(3043, 3371, 0)),
	TAVERLEY_STONE_CIRCLE(ObjectID.HH_EASY016, new WorldPoint(2924, 3477, 0)),
	CATHERBY_BEEHIVE_FIELD(ObjectID.HH_EASY018, new WorldPoint(2764, 3438, 0)),
	NEAR_THE_PARROTS_IN_ARDOUGNE_ZOO(ObjectID.HH_EASY019, new WorldPoint(2608, 3284, 0)),
	ROAD_JUNCTION_NORTH_OF_RIMMINGTON(ObjectID.HH_EASY020, new WorldPoint(2981, 3278, 0)),
	OUTSIDE_THE_FISHING_GUILD(ObjectID.HH_EASY021, new WorldPoint(2608, 3393, 0)),
	OUTSIDE_KEEP_LE_FAYE(ObjectID.HH_EASY022, new WorldPoint(2756, 3399, 0)),
	ROAD_JUNCTION_SOUTH_OF_SINCLAIR_MANSION(ObjectID.HH_EASY024, new WorldPoint(2735, 3534, 0)),
	OUTSIDE_THE_DIGSITE_EXAM_CENTRE(ObjectID.HH_EASY025, new WorldPoint(3353, 3343, 0)),
	NEAR_THE_SAWMILL_OPERATORS_BOOTH(ObjectID.HH_EASY026, new WorldPoint(3298, 3490, 0)),
	EMIRS_ARENA_TICKET_OFFICE(ObjectID.HH_EASY027, new WorldPoint(3316, 3242, 0)),
	OUTSIDE_VARROCK_PALACE_COURTYARD(ObjectID.HH_EASY_EXP1, new WorldPoint(3211, 3456, 0)),
	NEAR_HERQUINS_SHOP_IN_FALADOR(ObjectID.HH_EASY_EXP2, new WorldPoint(2941, 3339, 0)),
	SOUTH_OF_THE_GRAND_EXCHANGE(ObjectID.HH_EASY_EXP3, new WorldPoint(3159, 3464, 0)),
	AUBURYS_SHOP_IN_VARROCK(ObjectID.HH_EASY_EXP4, new WorldPoint(3252, 3404, 0)),
	CENTRE_OF_CANIFIS(ObjectID.HH_MEDIUM001, new WorldPoint(3491, 3489, 0)),
	MAUSOLEUM_OFF_THE_MORYTANIA_COAST(ObjectID.HH_MEDIUM002, new WorldPoint(3500, 3575, 0)),
	EAST_OF_THE_BARBARIAN_VILLAGE_BRIDGE(ObjectID.HH_MEDIUM003, new WorldPoint(3110, 3422, 0)),
	SOUTH_OF_THE_SHRINE_IN_TAI_BWO_WANNAI_VILLAGE(ObjectID.HH_MEDIUM004, new WorldPoint(2802, 3081, 0)),
	CASTLE_WARS_BANK(ObjectID.HH_MEDIUM005, new WorldPoint(2444, 3093, 0)),
	BARBARIAN_OUTPOST_OBSTACLE_COURSE(ObjectID.HH_MEDIUM006, new WorldPoint(2541, 3550, 0)),
	GNOME_STRONGHOLD_BALANCING_ROPE(ObjectID.HH_MEDIUM007, new WorldPoint(2473, 3418, 2)),
	OUTSIDE_YANILLE_BANK(ObjectID.HH_MEDIUM008, new WorldPoint(2603, 3091, 0)),
	OBSERVATORY(ObjectID.HH_MEDIUM009, new WorldPoint(2439, 3166, 0)),
	OGRE_CAGE_IN_KING_LATHAS_TRAINING_CAMP(ObjectID.HH_MEDIUM010, new WorldPoint(2533, 3377, 0)),
	DIGSITE(ObjectID.HH_MEDIUM011, new WorldPoint(3370, 3420, 0)),
	HICKTONS_ARCHERY_EMPORIUM(ObjectID.HH_MEDIUM012, new WorldPoint(2825, 3441, 0)),
	SHANTAY_PASS(ObjectID.HH_MEDIUM013, new WorldPoint(3308, 3125, 0)),
	LUMBRIDGE_SWAMP_CAVES(ObjectID.HH_MEDIUM_EXP1, new WorldPoint(3222, 9584, 0), new WorldPoint(3167, 9570, 0)),
	OUTSIDE_CATHERBY_BANK(ObjectID.HH_MEDIUM_EXP2, new WorldPoint(2807, 3437, 0)),
	OUTSIDE_THE_SEERS_VILLAGE_COURTHOUSE(ObjectID.HH_MEDIUM_EXP3, new WorldPoint(2731, 3475, 0)),
	OUTSIDE_HARRYS_FISHING_SHOP_IN_CATHERBY(ObjectID.HH_MEDIUM_EXP4, new WorldPoint(2837, 3436, 0)),
	TZHAAR_WEAPONS_STORE(ObjectID.HH_MEDIUM_EXP5, new WorldPoint(2479, 5146, 0)),
	NORTH_OF_EVIL_DAVES_HOUSE_IN_EDGEVILLE(ObjectID.HH_MEDIUM_EXP6, new WorldPoint(3077, 3503, 0)),
	WEST_OF_THE_SHAYZIEN_COMBAT_RING(ObjectID.HH_MEDIUM_EXP7, new WorldPoint(1541, 3631, 0)),
	ENTRANCE_OF_THE_ARCEUUS_LIBRARY(ObjectID.HH_MEDIUM_EXP8, new WorldPoint(1642, 3809, 0)),
	OUTSIDE_DRAYNOR_VILLAGE_JAIL(ObjectID.HH_MEDIUM_EXP9, new WorldPoint(3130, 3250, 0)),
	EAST_OF_THE_LEVEL_19_WILDERNESS_OBELISK(ObjectID.HH_HARD001, new WorldPoint(3243, 3662, 0)),
	FISHING_GUILD_BANK(ObjectID.HH_HARD002, new WorldPoint(2593, 3409, 0)),
	TOP_FLOOR_OF_THE_LIGHTHOUSE(ObjectID.HH_HARD003, new WorldPoint(2512, 3640, 2)),
	OUTSIDE_THE_GREAT_PYRAMID_OF_SOPHANEM(ObjectID.HH_HARD005, new WorldPoint(3291, 2780, 0)),
	NOTERAZZOS_SHOP_IN_THE_WILDERNESS(ObjectID.HH_HARD006, new WorldPoint(3027, 3699, 0)),
	WEST_SIDE_OF_THE_KARAMJA_BANANA_PLANTATION(ObjectID.HH_HARD007, new WorldPoint(2909, 3169, 0)),
	MOUNTAIN_CAMP_GOAT_ENCLOSURE(ObjectID.HH_HARD008, new WorldPoint(2810, 3677, 0)),
	GNOME_GLIDER_ON_WHITE_WOLF_MOUNTAIN(ObjectID.HH_HARD009, new WorldPoint(2849, 3496, 0)),
	SHILO_VILLAGE_BANK(ObjectID.HH_HARD010, new WorldPoint(2853, 2952, 0)),
	INSIDE_THE_DIGSITE_EXAM_CENTRE(ObjectID.HH_HARD_EXP1, new WorldPoint(3356, 3333, 0)),
	NORTHEAST_CORNER_OF_THE_KHARAZI_JUNGLE(ObjectID.HH_HARD_EXP2, new WorldPoint(2952, 2932, 0)),
	VOLCANO_IN_THE_NORTHEASTERN_WILDERNESS(ObjectID.HH_HARD_EXP3, new WorldPoint(3368, 3930, 0)),
	IN_THE_MIDDLE_OF_JIGGIG(ObjectID.HH_HARD_EXP4, new WorldPoint(2478, 3048, 0)),
	AGILITY_PYRAMID(ObjectID.HH_HARD_EXP5, new WorldPoint(3357, 2830, 0)),
	HOSIDIUS_MESS(ObjectID.HH_HARD_EXP6, new WorldPoint(1646, 3632, 0)),
	CHAPEL_IN_WEST_ARDOUGNE(ObjectID.HH_ELITE_EXP1, new WorldPoint(2527, 3294, 0)),
	NEAR_A_RUNITE_ROCK_IN_THE_FREMENNIK_ISLES(ObjectID.HH_ELITE_EXP2, new WorldPoint(2374, 3847, 0)),
	NEAR_A_LADDER_IN_THE_WILDERNESS_LAVA_MAZE(ObjectID.HH_ELITE_EXP3, new WorldPoint(3069, 3862, 0)),
	ENTRANCE_OF_THE_CAVE_OF_DAMIS(ObjectID.HH_ELITE_EXP4, new WorldPoint(2629, 5070, 0)),
	WARRIORS_GUILD_BANK(ObjectID.HH_ELITE_EXP5, new WorldPoint(2844, 3537, 0)),
	SOUTHEAST_CORNER_OF_THE_MONASTERY(ObjectID.HH_ELITE_EXP6, new WorldPoint(3056, 3482, 0)),
	SOUTHEAST_CORNER_OF_THE_FISHING_PLATFORM(ObjectID.HH_ELITE_EXP7, new WorldPoint(2787, 3277, 0)),
	OUTSIDE_THE_SLAYER_TOWER_GARGOYLE_ROOM(ObjectID.HH_ELITE_EXP8, new WorldPoint(3423, 3534, 2)),
	ON_TOP_OF_TROLLHEIM_MOUNTAIN(ObjectID.HH_ELITE_EXP9, new WorldPoint(2886, 3676, 0)),
	FOUNTAIN_OF_HEROES(ObjectID.HH_ELITE_EXP10, new WorldPoint(2916, 9891, 0)),
	ENTRANCE_OF_THE_CAVERN_UNDER_THE_WHIRLPOOL(ObjectID.HH_ELITE_EXP11, new WorldPoint(1764, 5367, 1), new WorldPoint(1636, 5367, 1)),
	HALFWAY_DOWN_TROLLWEISS_MOUNTAIN(ObjectID.HH_ELITE_EXP12, new WorldPoint(2782, 3787, 0)),
	SHAYZIEN_WAR_TENT(ObjectID.HH_ELITE_EXP13, new WorldPoint(1488, 3637, 0)),
	OUTSIDE_THE_LEGENDS_GUILD_DOOR(ObjectID.HH_ELITE_EXP14, new WorldPoint(2727, 3371, 0)),
	NEAR_THE_GEM_STALL_IN_ARDOUGNE_MARKET(ObjectID.HH_ELITE_EXP15, new WorldPoint(2672, 3302, 0)),
	OUTSIDE_THE_BAR_BY_THE_FIGHT_ARENA(ObjectID.HH_ELITE_EXP16, new WorldPoint(2571, 3150, 0)),
	SOUTHEAST_CORNER_OF_LAVA_DRAGON_ISLE(ObjectID.HH_MASTER001, new WorldPoint(3228, 3830, 0)),
	NEAR_THE_PIER_IN_ZULANDRA(ObjectID.HH_MASTER002, new WorldPoint(2203, 3059, 0)),
	BARROWS_CHEST(ObjectID.HH_MASTER003, new WorldPoint(3547, 9690, 0)),
	WELL_OF_VOYAGE(ObjectID.HH_MASTER004, new WorldPoint(2006, 4709, 1)),
	NORTHERN_WALL_OF_CASTLE_DRAKAN(ObjectID.HH_MASTER005, new WorldPoint(3563, 3379, 0)),
	_7TH_CHAMBER_OF_JALSAVRAH(ObjectID.HH_MASTER006, new WorldPoint(1951, 4431, 0)),
	SOUL_ALTAR(ObjectID.HH_MASTER007, new WorldPoint(1810, 3855, 0)),
	WARRIORS_GUILD_BANK_29047(ObjectID.HH_MASTER008, new WorldPoint(2845, 3545, 0)),
	ENTRANA_CHAPEL(ObjectID.HH_MASTER009, new WorldPoint(2851, 3355, 0)),
	TZHAAR_GEM_STORE(ObjectID.HH_MASTER010, new WorldPoint(2466, 5150, 0)),
	TENT_IN_LORD_IORWERTHS_ENCAMPMENT(ObjectID.HH_MASTER011, new WorldPoint(2198, 3257, 0)),
	OUTSIDE_MUDKNUCKLES_HUT(ObjectID.HH_MASTER012, new WorldPoint(2959, 3502, 0)),
	CENTRE_OF_THE_CATACOMBS_OF_KOUREND(ObjectID.HH_MASTER013, new WorldPoint(1661, 10045, 0)),
	KING_BLACK_DRAGONS_LAIR(ObjectID.HH_MASTER014, new WorldPoint(2286, 4680, 0)),
	OUTSIDE_KRIL_TSUTSAROTHS_ROOM(ObjectID.HH_MASTER015, new WorldPoint(2931, 5337, 2)),
	BY_THE_BEAR_CAGE_IN_VARROCK_PALACE_GARDENS(ObjectID.HH_MASTER016, new WorldPoint(3232, 3494, 0)),
	OUTSIDE_THE_WILDERNESS_AXE_HUT(ObjectID.HH_MASTER017, new WorldPoint(3186, 3958, 0)),
	TOP_FLOOR_OF_THE_YANILLE_WATCHTOWER(ObjectID.HH_MASTER018, new WorldPoint(2930, 4718, 2)),
	DEATH_ALTAR(ObjectID.HH_MASTER019, new WorldPoint(2210, 4842, 0)),
	BEHIND_MISS_SCHISM_IN_DRAYNOR_VILLAGE(ObjectID.HH_MASTER020, new WorldPoint(3095, 3254, 0)),
	NORTHWESTERN_CORNER_OF_THE_ENCHANTED_VALLEY(ObjectID.HH_MASTER021, new WorldPoint(3022, 4517, 0)),
	NORTH_OF_MOUNT_KARUULM(ObjectID.HH_MEDIUM_EXP10, new WorldPoint(1308, 3840, 0)),
	GYPSY_TENT_ENTRANCE(ObjectID.HH_BEGINNER001, new WorldPoint(3206, 3422, 0)),
	FINE_CLOTHES_ENTRANCE(ObjectID.HH_BEGINNER002, new WorldPoint(3209, 3416, 0)),
	BOB_AXES_ENTRANCE(ObjectID.HH_BEGINNER003, new WorldPoint(3233, 3200, 0)),
	CRYSTALLINE_MAPLE_TREES(ObjectID.HH_MASTER022, new WorldPoint(2213, 3427, 0)),
	CHARCOAL_BURNERS(ObjectID.HH_ELITE_EXP17, new WorldPoint(1712, 3470, 0)),
	FORTIS_GRAND_MUSEUM(ObjectID.HH_EASY_VM01, new WorldPoint(1723, 3153, 0)),
	TEMPLE_SOUTHEAST_OF_THE_BAZAAR(ObjectID.HH_ELITE_VM01, new WorldPoint(1702, 3079, 0)),
	CAM_TORUM_ENTRANCE(ObjectID.HH_MASTER_VM01, new WorldPoint(1428, 3118, 0)),
	WESTERN_SALVAGER_OVERLOOK(ObjectID.HH_MASTER_VM02, new WorldPoint(1614, 3296, 0)),
	TWILIGHT_TEMPLE_MINE(ObjectID.HH_MEDIUM_EXP11, new WorldPoint(1668, 3287, 0)),
	ORTUS_MEETS_PROUDSPIRE(ObjectID.HH_MEDIUM_EXP12, new WorldPoint(1629, 3239, 0)),
	OUTSIDE_TWILIGHT_TEMPLE(ObjectID.HH_HARD_EXP7, new WorldPoint(1693, 3243, 0)),
	;

	private final int objectId;
	private final WorldPoint[] worldPoints;

	STASHUnit(int objectId, WorldPoint... worldPoints)
	{
		this.objectId = objectId;
		this.worldPoints = worldPoints;
	}
}
