/*
 * Copyright (c) 2020, <PERSON> <https://github.com/J<PERSON><PERSON><PERSON><PERSON>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.mining;

import com.google.common.collect.ImmutableSet;
import java.util.Set;
import net.runelite.api.gameval.AnimationID;

public class MiningAnimation
{
	public static final Set<Integer> MINING_ANIMATIONS = ImmutableSet.of(
		AnimationID.HUMAN_MINING_BRONZE_PICKAXE,
		AnimationID.HUMAN_MINING_BRONZE_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_BRONZE_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_BRONZE,

		AnimationID.HUMAN_MINING_IRON_PICKAXE,
		AnimationID.HUMAN_MINING_IRON_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_IRON_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_IRON,

		AnimationID.HUMAN_MINING_STEEL_PICKAXE,
		AnimationID.HUMAN_MINING_STEEL_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_STEEL_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_STEEL,

		AnimationID.HUMAN_MINING_BLACK_PICKAXE,
		AnimationID.HUMAN_MINING_BLACK_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_BLACK_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_BLACK,

		AnimationID.HUMAN_MINING_MITHRIL_PICKAXE,
		AnimationID.HUMAN_MINING_MITHRIL_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_MITHRIL_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_MITHRIL,

		AnimationID.HUMAN_MINING_ADAMANT_PICKAXE,
		AnimationID.HUMAN_MINING_ADAMANT_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_ADAMANT_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_ADAMANT,

		AnimationID.HUMAN_MINING_RUNE_PICKAXE,
		AnimationID.HUMAN_MINING_RUNE_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_RUNE_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_RUNE,

		AnimationID.HUMAN_MINING_GILDED_PICKAXE,
		AnimationID.HUMAN_MINING_GILDED_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_GILDED_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_GILDED,

		AnimationID.HUMAN_MINING_DRAGON_PICKAXE,
		AnimationID.HUMAN_MINING_DRAGON_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_DRAGON_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_DRAGON,

		AnimationID.HUMAN_MINING_ZALCANO_PICKAXE,
		AnimationID.HUMAN_MINING_ZALCANO_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_ZALCANO_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_ZALCANO,

		AnimationID.HUMAN_MINING_TRAILBLAZER_PICKAXE_NO_INFERNAL,
		AnimationID.HUMAN_MINING_TRAILBLAZER_PICKAXE_NO_INFERNAL_WALL,
		AnimationID.HUMAN_MINING_TRAILBLAZER_PICKAXE_NO_INFERNAL_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_TRAILBLAZER_NO_INFERNAL,

		AnimationID.HUMAN_MINING_TRAILBLAZER_RELOADED_PICKAXE_NO_INFERNAL,
		AnimationID.HUMAN_MINING_TRAILBLAZER_RELOADED_PICKAXE_NO_INFERNAL_WALL,
		AnimationID.HUMAN_MINING_TRAILBLAZER_RELOADED_PICKAXE_NO_INFERNAL_NOREACHFORWARD,

		AnimationID.HUMAN_MINING_DRAGON_PICKAXE_PRETTY,
		AnimationID.HUMAN_MINING_DRAGON_PICKAXE_PRETTY_WALL,
		AnimationID.HUMAN_MINING_DRAGON_PICKAXE_PRETTY_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_PRETTY,

		AnimationID.HUMAN_MINING_INFERNAL_PICKAXE,
		AnimationID.HUMAN_MINING_INFERNAL_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_INFERNAL_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_INFERNAL,

		AnimationID.HUMAN_MINING_TRAILBLAZER_PICKAXE,
		AnimationID.HUMAN_MINING_TRAILBLAZER_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_TRAILBLAZER_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_TRAILBLAZER,

		AnimationID.HUMAN_MINING_TRAILBLAZER_RELOADED_PICKAXE,
		AnimationID.HUMAN_MINING_TRAILBLAZER_RELOADED_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_TRAILBLAZER_RELOADED_PICKAXE_NOREACHFORWARD,

		AnimationID.HUMAN_MINING_3A_PICKAXE,
		AnimationID.HUMAN_MINING_3A_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_3A_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_3A,

		AnimationID.HUMAN_MINING_CRYSTAL_PICKAXE,
		AnimationID.HUMAN_MINING_CRYSTAL_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_CRYSTAL_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_CRYSTAL,

		AnimationID.HUMAN_MINING_LEAGUE_TRAILBLAZER_PICKAXE,
		AnimationID.HUMAN_MINING_LEAGUE_TRAILBLAZER_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_LEAGUE_TRAILBLAZER_PICKAXE_NOREACHFORWARD,
		AnimationID.PICKAXE_POWER_SWING_LEAGUE_TRAILBLAZER
	);

	static final Set<Integer> WAll_ANIMATIONS = ImmutableSet.of(
		AnimationID.HUMAN_MINING_3A_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_ADAMANT_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_BLACK_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_BRONZE_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_CRYSTAL_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_DRAGON_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_ZALCANO_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_TRAILBLAZER_PICKAXE_NO_INFERNAL_WALL,
		AnimationID.HUMAN_MINING_DRAGON_PICKAXE_PRETTY_WALL,
		AnimationID.HUMAN_MINING_GILDED_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_INFERNAL_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_TRAILBLAZER_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_TRAILBLAZER_RELOADED_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_IRON_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_MITHRIL_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_RUNE_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_STEEL_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_LEAGUE_TRAILBLAZER_PICKAXE_WALL,
		AnimationID.HUMAN_MINING_TRAILBLAZER_RELOADED_PICKAXE_NO_INFERNAL_WALL
	);
}
