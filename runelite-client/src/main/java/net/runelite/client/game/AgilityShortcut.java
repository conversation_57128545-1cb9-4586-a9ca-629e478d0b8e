/*
 * Copyright (c) 2018, SomeoneWithAnInternetConnection
 * Copyright (c) 2019, <PERSON><PERSON><PERSON><PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.game;

import lombok.Getter;
import net.runelite.api.Client;
import net.runelite.api.Quest;
import net.runelite.api.QuestState;
import net.runelite.api.TileObject;
import net.runelite.api.coords.WorldPoint;
import net.runelite.api.gameval.ObjectID;

@Getter
public enum AgilityShortcut
{
	GENERIC_SHORTCUT(1, "Shortcut", null,
			// Trollheim
		ObjectID.TROLL_CLIMBINGROCKS_TOP, ObjectID.TROLL_CLIMBINGROCKS_BOTTOM,
			// Fremennik Slayer Cave
		ObjectID.KURASK_STEPS,
			// Fossil Island
		ObjectID.FOSSIL_TALL_LADDER_WEST, ObjectID.FOSSIL_TALL_LADDER_WEST_TOP, ObjectID.FOSSIL_TALL_LADDER_EAST, ObjectID.FOSSIL_TALL_LADDER_EAST_TOP, ObjectID.FOSSIL_JUMPSHROOM,
			// Brimhaven dungeon
		ObjectID.BRIMHAVEN_SLAYER_DRAGON_TUNNEL,
			// Lumbridge
		ObjectID.QIP_SHEEP_SHEARER_FULLSTYLE,
			// Gu'Tanoth Bridge
		ObjectID.TANOTHJUMP1, ObjectID.TANOTHJUMP2,
			// Lumbridge Swamp Caves
		ObjectID.SWAMP_CAVE_STEPPINGSTONE_A, ObjectID.SWAMP_CAVE_STEPPINGSTONE_B, ObjectID.TOG_CLIMBING_ROCKS_UP,
			// Morytania Pirate Ship
		ObjectID.AHOY_ROCK_INVISIBLE,
			// Lumber Yard
		ObjectID.GERTRUDEFENCE,
			// McGrubor's Wood
		ObjectID.MCGRUBORLOOSERAILING,
			// Underwater Area Fossil Island
		ObjectID.FOSSIL_UNDERWATER_DEEPWATER_AGIHOLE, ObjectID.FOSSIL_UNDERWATER_CAVE_OBSTACLE3, ObjectID.FOSSIL_GALLEON_SIDE_LVL1_OBSTACLE, ObjectID.FOSSIL_GALLEON_SIDELVL1_INTERIOR_OBSTACLE, ObjectID.FOSSIL_UNDERWATER_CAVE_OBSTACLE2, ObjectID.FOSSIL_UNDERWATER_CAVE_OBSTACLE1, ObjectID.FOSSIL_UNDERWATER_DEEPWATER_HOLE, ObjectID.FOSSIL_UNDERWATER_DRIFTNET_CURTAIN,
			// Tree Gnome Village
		ObjectID.TREEGNOMELOOSERAILING,
			// Burgh de Rott
		ObjectID.BURGH_AGILITY_SHORTCUT_FENCE,
			// Taverley
		ObjectID.FULLSTYLE,
			// Asgarnian Ice Dungeon
		ObjectID.WYVERN_STEPS,
			// Fossil Island Wyvern Cave
		ObjectID.FOSSIL_WYVERN_TASK_STAIRS_1B,
			// Trollweiss Mountain Cave
		ObjectID.HUNTING_POLAR_CLIMBING_ROCKS, ObjectID.HUNTING_POLAR_CLIMBING_ROCKS_INACTIVE,
			// Witchaven Dungeon
		ObjectID.SLUG2_CAVE_WALL_SHORTCUT,
			// Nature Grotto bridge
		ObjectID.DRUIDJUMP_LOC,
			// Rocky Ledges Viyeldi cave
		ObjectID.ROCKY_LEDGE, ObjectID.ROCKY_LEDGE1, ObjectID.ROCKY_LEDGE2, ObjectID.VIYCAVES_CLIMBROCK1, ObjectID.VIYCAVES_CLIMBROCK2, ObjectID.VIYCAVES_CLIMBROCK3,
			// Villa Lucens backstage theatre
		ObjectID.DOTI_AGILITY_CHALLENGE_A, ObjectID.DOTI_AGILITY_CHALLENGE_B, ObjectID.DOTI_AGILITY_CHALLENGE_C),
	WEISS_BROKEN_FENCE(1, "Shortcut", null, /* base id */ ObjectID.MY2ARM_TOWN_FENCE_BROKEN_SHORTCUT_MULTI)
	{
		@Override
		public boolean matches(Client client, TileObject object)
		{
			assert object.getId() == ObjectID.MY2ARM_TOWN_FENCE_BROKEN_SHORTCUT_MULTI;
			int multilocId = client.getObjectDefinition(object.getId())
				.getImpostor()
				.getId();
			return multilocId == ObjectID.MY2ARM_TOWN_FENCE_BROKEN_SHORTCUT;
		}
	},
	BRIMHAVEN_DUNGEON_MEDIUM_PIPE_RETURN(1, "Pipe Squeeze", null, new WorldPoint(2698, 9491, 0), ObjectID.KARAM_DUNGEON_PIPE),
	BRIMHAVEN_DUNGEON_PIPE_RETURN(1, "Pipe Squeeze", null, new WorldPoint(2655, 9573, 0), ObjectID.KARAM_DUNGEON_PIPE2),
	BRIMHAVEN_DUNGEON_STEPPING_STONES_RETURN(1, "Pipe Squeeze", null, ObjectID.KARAM_DUNGEON_STONE2),
	BRIMHAVEN_DUNGEON_LOG_BALANCE_RETURN(1, "Log Balance", null, ObjectID.KARAM_DUNGEON_BAMBOO_LOGBALANCE3),
	AGILITY_PYRAMID_ROCKS_WEST(1, "Rocks", null, ObjectID.NTK_AGILITY_CLIMBING_ROCKS_1),
	CAIRN_ISLE_CLIMBING_ROCKS(1, "Rocks", null, ObjectID.ZQHANDHOLDS),
	KARAMJA_GLIDER_LOG(1, "Log Balance", new WorldPoint(2906, 3050, 0), ObjectID.ZQ_LOGBALANCE),
	FALADOR_CRUMBLING_WALL(5, "Crumbling Wall", new WorldPoint(2936, 3357, 0), ObjectID.FAI_FALADOR_CASTLE_CRUMBLE_MID),
	YANILLE_CLIMBING_ROCKS(5, "Climbing rocks", null, ObjectID.WATCHSHORTCUT),
	RIVER_LUM_GRAPPLE_WEST(8, "Grapple Broken Raft", new WorldPoint(3245, 3179, 0), ObjectID.XBOWS_RAFT_BR),
	RIVER_LUM_GRAPPLE_EAST(8, "Grapple Broken Raft", new WorldPoint(3258, 3179, 0), ObjectID.XBOWS_RAFT_BR),
	CORSAIR_COVE_ROCKS(10, "Rocks", new WorldPoint(2545, 2871, 0), ObjectID.DS2_CORSAIR_COVE_SHORTCUT),
	KARAMJA_MOSS_GIANT_SWING(10, "Rope", null, ObjectID.TREE_ROPESWING1, ObjectID.TREE_ROPESWING2),
	FALADOR_GRAPPLE_WALL(11, "Grapple Wall", new WorldPoint(3031, 3391, 0), ObjectID.XBOWS_FAI_FALADOR_CASTLE_WALLS_HILLSKEW, ObjectID.XBOWS_FAI_FALADOR_CASTLE_ARCHES_HILLSKEW),
	BRIMHAVEN_DUNGEON_STEPPING_STONES(12, "Stepping Stones", null, ObjectID.KARAM_DUNGEON_STONE1),
	VARROCK_SOUTH_FENCE(13, "Fence", new WorldPoint(3239, 3334, 0), ObjectID.LUMBRIDGE_SC_FENCEJUMP),
	GOBLIN_VILLAGE_WALL(14, "Wall", new WorldPoint(2925, 3523, 0), ObjectID.BURTHORPE_DIARY_SHORTCUT),
	CORSAIR_COVE_DUNGEON_PILLAR(15, "Pillar Jump", new WorldPoint(1980, 8996, 0), ObjectID.DS2_OGRE_CORSAIR_DUNGEON_SHORTCUT),
	EDGEVILLE_DUNGEON_MONKEYBARS(15, "Monkey Bars", null, ObjectID.MONKEYBARS_END1),
	TROLLHEIM_ROCKS(15, "Rocks", null, new WorldPoint(2838, 3614, 0), ObjectID.TROLL_CLIMBINGROCKS), // No fixed world map location, but rocks near death plateau have a requirement of 15
	YANILLE_UNDERWALL_TUNNEL(16, "Underwall Tunnel", new WorldPoint(2574, 3109, 0), ObjectID.YANILLE_CASTLEHOLE_SC, ObjectID.YANILLE_CASTLEWALL_SC),
	KOUREND_CATACOMBS_SOUTH_WEST_CRACK_NORTH(17, "Crack", new WorldPoint(1647, 10008, 0), ObjectID.ZEAH_CATA_CRACK),
	KOUREND_CATACOMBS_SOUTH_WEST_CRACK_SOUTH(17, "Crack", new WorldPoint(1645, 10001, 0), ObjectID.ZEAH_CATA_CRACK),
	CRABCLAW_CAVES_CREVICE(18, "Crevice", new WorldPoint(1710, 9822, 0), ObjectID.HOSIDIUSQUEST_CRACKIN, ObjectID.HOSIDIUSQUEST_CRACKOUT),
	CRABCLAW_CAVES_ROCKS(18, "Rocks", new WorldPoint(1687, 9802, 0), ObjectID.HOSIDIUSQUEST_ROCK),
	CRABCLAW_CAVES_STEPPING_STONES(18, "Stepping Stones", new WorldPoint(1704, 9800, 0), ObjectID.HOSIDIUSQUEST_STONE),
	YANILLE_WATCHTOWER_TRELLIS(18, "Trellis", null, ObjectID.QIP_WATCHTOWER_TRELLIS_BASE),
	COAL_TRUCKS_LOG_BALANCE(20, "Log Balance", new WorldPoint(2598, 3475, 0), ObjectID.MINE_LOG_BALANCE1),
	GRAND_EXCHANGE_UNDERWALL_TUNNEL(21, "Underwall Tunnel", new WorldPoint(3139, 3515, 0), ObjectID.VARROCK_SC_TUNNEL_WEST, ObjectID.VARROCK_SC_TUNNEL_EAST),
	BRIMHAVEN_DUNGEON_PIPE(22, "Pipe Squeeze", new WorldPoint(2654, 9569, 0), ObjectID.KARAM_DUNGEON_PIPE2),
	OBSERVATORY_SCALE_CLIFF(23, "Grapple Rocks", new WorldPoint(2447, 3155, 0), ObjectID.XBOWS_ROPE_DIAGONAL_OBS, ObjectID.XBOWS_ROCK_HILLTOP_OBS),
	NEMUS_RETREAT_WALL_SOUTH_EAST(24, "Broken Wall", new WorldPoint(1386, 3302, 0), ObjectID.AV_LOWWALL_CLIMB_1),
	NEMUS_RETREAT_WALL_SOUTH_WEST(24, "Broken Wall", new WorldPoint(1368, 3295, 0), ObjectID.AV_LOWWALL_CLIMB_1),
	NEMUS_RETREAT_WALL_EAST(24, "Broken Wall", new WorldPoint(1389, 3309, 0), ObjectID.AV_LOWWALL_CLIMB_2),
	EAGLES_PEAK_ROCK_CLIMB(25, "Rock Climb", new WorldPoint(2320, 3499, 0), ObjectID.EP_CLIMBING_ROCKS01),
	FALADOR_UNDERWALL_TUNNEL(26, "Underwall Tunnel", new WorldPoint(2947, 3313, 0), ObjectID.FALADOR_SC_CASTLEWALL_SOUTH, ObjectID.FALADOR_SC_CASTLEWALL_NORTH),
	KOUREND_CATACOMBS_STONES_NORTH(28, "Stones", new WorldPoint(1613, 10071, 0), ObjectID.ZEAH_CATA_STEPSTONE),
	KOUREND_CATACOMBS_STONES_SOUTH(28, "Stones", new WorldPoint(1609, 10060, 0), ObjectID.ZEAH_CATA_STEPSTONE),
	MOUNT_KARUULM_LOWER(29, "Rocks", new WorldPoint(1324, 3782, 0), ObjectID.MOUNT_KARUULM_SHORTCUT_ROCKS_LOW),
	CORSAIR_COVE_RESOURCE_ROCKS(30, "Rocks", new WorldPoint(2486, 2898, 0), ObjectID.DS2_CORSAIR_SHORTCUT_BOTTOM, ObjectID.DS2_CORSAIR_SHORTCUT_TOP),
	SOUTHEAST_KARAJMA_STEPPING_STONES(30, "Stepping Stones", new WorldPoint(2924, 2946, 0), ObjectID.ZQROCKJUMP1, ObjectID.ZQROCKJUMP2, ObjectID.ZQROCKJUMP3),
	BRIMHAVEN_DUNGEON_LOG_BALANCE(30, "Log Balance", null, ObjectID.KARAM_DUNGEON_BAMBOO_LOGBALANCE1),
	AGILITY_PYRAMID_ROCKS_EAST(30, "Rocks", null, ObjectID.NTK_AGILITY_CLIMBING_ROCKS_2),
	DRAYNOR_MANOR_STEPPING_STONES(31, "Stepping Stones", new WorldPoint(3150, 3362, 0), ObjectID.LUMBRIDGE_SC_STEPSTONE),
	CATHERBY_CLIFFSIDE_GRAPPLE(32, "Grapple Rock", new WorldPoint(2868, 3429, 0), ObjectID.XBOWS_ROCK_HILLTOP_BASIC),
	CAIRN_ISLE_ROCKS(32, "Rocks", null, ObjectID.ZQCLIMBINGROCKS),
	SHILO_VILLAGE_STEPPING_STONES( 32, "Stepping Stones", new WorldPoint(2863, 2974, 0), ObjectID.SHILO_RIVER_STEPPINGSTONE),
	ARDOUGNE_LOG_BALANCE(33, "Log Balance", new WorldPoint(2602, 3336, 0), ObjectID.ARDOUGNE_LOG_BALANCE_LEFT_SC, ObjectID.ARDOUGNE_LOG_BALANCE_MID_SC, ObjectID.ARDOUGNE_LOG_BALANCE_RIGHT_SC),
	NEMUS_RETREAT_TUNNEL(33, "Tunnel", new WorldPoint(1367, 3325, 0), ObjectID.AV_TUNNEL_1),
	BRIMHAVEN_DUNGEON_MEDIUM_PIPE(34, "Pipe Squeeze", null, new WorldPoint(2698, 9501, 0), ObjectID.KARAM_DUNGEON_PIPE),
	KOUREND_CATACOMBS_NORTH_EAST_CRACK_NORTH(34, "Crack", new WorldPoint(1715, 10057, 0), ObjectID.ZEAH_CATA_CRACK),
	KOUREND_CATACOMBS_NORTH_EAST_CRACK_SOUTH(34, "Crack", new WorldPoint(1705, 10077, 0), ObjectID.ZEAH_CATA_CRACK),
	VARROCK_CASTLE_GARDEN_TRELLIS(35, "Trellis", new WorldPoint(3227, 3471, 0), ObjectID.GARDEN_TRELLIS_CONCAVE_SHORTCUT)
	{
		@Override
		public boolean matches(Client client, TileObject object)
		{
			return Quest.GARDEN_OF_TRANQUILLITY.getState(client) == QuestState.FINISHED;
		}
	},
	CATHERBY_OBELISK_GRAPPLE(36, "Grapple Rock", null, ObjectID.XBOWS_BEACH_TO_ISLAND_TREE_BASIC),
	NEMUS_RETREAT_STEPPING_STONES(36, "Stepping Stones", new WorldPoint(1395, 3309, 0), ObjectID.AV_STEPSTONE_1),
	GNOME_STRONGHOLD_ROCKS(37, "Rocks", new WorldPoint(2485, 3515, 0), ObjectID.GNOME_STRONGHOLD_SC_ROCK_TOP, ObjectID.GNOME_STRONGHOLD_SC_ROCK_BOTTOM),
	AL_KHARID_MINING_PITCLIFF_SCRAMBLE(38, "Rocks", new WorldPoint(3305, 3315, 0), ObjectID.ALKHARID_MINE_SC_TOP, ObjectID.ALKHARID_MINE_SC_BOTTOM),
	YANILLE_WALL_GRAPPLE(39, "Grapple Wall", new WorldPoint(2552, 3072, 0), ObjectID.XBOWS_YANILLE_CASTLEWALL),
	NEITIZNOT_BRIDGE_REPAIR(0, "Bridge Repair - Quest", new WorldPoint(2315, 3828, 0), ObjectID.FRISB_BRIDGE_1_S, ObjectID.FRISB_BRIDGE_1_N),
	NEITIZNOT_BRIDGE_SOUTHEAST(0, "Rope Bridge", null, ObjectID.FRISB_BRIDGE_2_S, ObjectID.FRISB_BRIDGE_2_N),
	NEITIZNOT_BRIDGE_NORTHWEST(0, "Rope Bridge", null, ObjectID.FRISB_BRIDGE_3_S, ObjectID.FRISB_BRIDGE_3_N),
	NEITIZNOT_BRIDGE_NORTH(0, "Rope Bridge", null, ObjectID.FRISB_BRIDGE_4_S, ObjectID.FRISB_BRIDGE_4_N),
	NEITIZNOT_BRIDGE_NORTHEAST(40, "Broken Rope bridge", null, ObjectID.FRISB_BRIDGE_5_S, ObjectID.FRISB_BRIDGE_5_N),
	KOUREND_LAKE_JUMP_EAST(40, "Stepping Stones", new WorldPoint(1612, 3570, 0), ObjectID.ZEAH_LAKE_SHORTCUT_HOSIDIUS, ObjectID.ZEAH_LAKE_SHORTCUT_SHAYZIEN),
	KOUREND_LAKE_JUMP_WEST(40, "Stepping Stones", new WorldPoint(1604, 3572, 0), ObjectID.ZEAH_LAKE_SHORTCUT_HOSIDIUS, ObjectID.ZEAH_LAKE_SHORTCUT_SHAYZIEN),
	TLATI_RAINFORST_BALANCE(40, "Log Balance", new WorldPoint(1283, 3144, 0), ObjectID.TLATI_NORTH_RIVER_LOG_BALANCE_1),
	YANILLE_DUNGEON_BALANCE(40, "Balancing Ledge", null, ObjectID.BALANCING_LEDGE3),
	AUBURNVALE_ROCK_SCRAMBLE(41, "Rocks", new WorldPoint(1393, 3322, 0), ObjectID.AV_SCRAMBLE_1),
	TROLLHEIM_EASY_CLIFF_SCRAMBLE(41, "Rocks", new WorldPoint(2869, 3670, 0), ObjectID.ASC_TROLL_MOUNTAIN_CLIMBROCK_1),
	DWARVEN_MINE_NARROW_CREVICE(42, "Narrow Crevice", new WorldPoint(3034, 9806, 0), ObjectID.DWARF_MINES_SC_WALL_CRACK),
	DRAYNOR_UNDERWALL_TUNNEL(42, "Underwall Tunnel", new WorldPoint(3068, 3261, 0), ObjectID.DRAYNOR_DIARY_UNDER_WALL_W, ObjectID.DRAYNOR_DIARY_UNDER_WALL_E),
	TROLLHEIM_MEDIUM_CLIFF_SCRAMBLE_NORTH(43, "Rocks", new WorldPoint(2886, 3684, 0), ObjectID.TROLL_MOUNTAIN_SHORTCUT_CLIMBINGROCKS1, ObjectID.TROLL_MOUNTAIN_SHORTCUT_CLIMBINGROCKS2, ObjectID.ASC_TROLL_MOUNTAIN_CLIMBROCK_2),
	TROLLHEIM_MEDIUM_CLIFF_SCRAMBLE_SOUTH(43, "Rocks", new WorldPoint(2876, 3666, 0), ObjectID.TROLL_MOUNTAIN_SHORTCUT_CLIMBINGROCKS1, ObjectID.TROLL_MOUNTAIN_SHORTCUT_CLIMBINGROCKS2, ObjectID.ASC_TROLL_MOUNTAIN_CLIMBROCK_2),
	TLATI_RAINFORST_CLIFF_SCRAMBLE(43, "Rocks", new WorldPoint(1271, 3001, 0), ObjectID.TLATI_TREE_AREA_SHORTCUT_BOTTOM, ObjectID.TLATI_TREE_AREA_SHORTCUT_TOP),
	SLAYER_DUNGEON_CHASM_JUMP(43, "Spiked Blades", new WorldPoint(2770, 10003, 0), ObjectID.SLAYER_DUNGEON_FLOOR_SPIKES_SC),
	TROLLHEIM_ADVANCED_CLIFF_SCRAMBLE(44, "Rocks", new WorldPoint(2907, 3686, 0), ObjectID.ASC_TROLL_MOUNTAIN_CLIMBROCK_3, ObjectID.TROLL_CLIMBINGROCKS),
	AUBURN_VALLEY_LOG_BALANCE_SOUTH(45, "Log Balance", new WorldPoint(1401, 3287, 0), ObjectID.AV_BALANCE_1),
	AUBURN_VALLEY_LOG_BALANCE_NORTH(45, "Log Balance", new WorldPoint(1453, 3332, 0), ObjectID.AV_BALANCE_2),
	PROUDSPIRE_LOWER_ROCKS(45, "Rocks", new WorldPoint(1588, 3260, 0), ObjectID.PROUDSPIRE_CLIMBING_ROCKS_DARK01_OP),
	KOUREND_RIVER_STEPPING_STONES(45, "Stepping Stones", new WorldPoint(1720, 3551, 0), ObjectID.ZEAH_SALTPETRE_SHORTCUT),
	TIRANNWN_LOG_BALANCE(45, "Log Balance", null, ObjectID.REGICIDE_LOGBALANCE3_START, ObjectID.REGICIDE_LOGBALANCE1_START, ObjectID.REGICIDE_LOGBALANCE2, ObjectID.REGICIDE_LOGBALANCE1, ObjectID.REGICIDE_LOGBALANCE2_START),
	COSMIC_ALTAR_MEDIUM_WALKWAY(46, "Narrow Walkway", new WorldPoint(2399, 4403, 0), ObjectID.FAIRY_SC_JUTTINGWALL),
	DEEP_WILDERNESS_DUNGEON_CREVICE_NORTH(46, "Narrow Crevice", new WorldPoint(3047, 10335, 0), ObjectID.WILDERNESS_DEEP_CREVICE),
	DEEP_WILDERNESS_DUNGEON_CREVICE_SOUTH(46, "Narrow Crevice", new WorldPoint(3045, 10327, 0), ObjectID.WILDERNESS_DEEP_CREVICE),
	TONALI_CAVERN_STEPPING_STONE(46, "Stepping Stones", null, ObjectID.VMQ4_CRYPT_STEPSTONE),
	TONALI_CAVERN_LOG_BALANCE(46, "Log Balance", null, ObjectID.VMQ4_CRYPT_LOG_BALANCE_1),
	TROLLHEIM_HARD_CLIFF_SCRAMBLE(47, "Rocks", new WorldPoint(2902, 3680, 0), ObjectID.ASC_TROLL_MOUNTAIN_CLIMBROCK_4),
	RALOS_RISE_ROCK_CLIMB(47, "Rocks", new WorldPoint(1458, 3129, 0), ObjectID.RALOS_RISE_SC),
	FREMENNIK_LOG_BALANCE(48, "Log Balance", new WorldPoint(2721, 3591, 0), ObjectID.SLAYER_RIVER_SC_LOGBALANCE_1, ObjectID.SLAYER_RIVER_SC_LOGBALANCE_2, ObjectID.SLAYER_RIVER_SC_LOGBALANCE_3),
	YANILLE_DUNGEON_PIPE_SQUEEZE(49, "Pipe Squeeze", null, ObjectID.OBSTICAL_PIPE4),
	ARCEUUS_ESSENCE_MINE_BOULDER(49, "Boulder", new WorldPoint(1774, 3888, 0), ObjectID.ARCHEUUS_RUNESTONE_SHORTCUT_BOULDER),
	MORYTANIA_STEPPING_STONE(50, "Stepping Stone", new WorldPoint(3418, 3326, 0), ObjectID.FAIRY_ISLAND_NATURE_GROTTO_SHORTCUT),
	SHAMAN_CAVES_JAGGED_WALL(50, "Jagged wall", null, ObjectID.CRUMBLED_WALL),
	VARROCK_SEWERS_PIPE_SQUEEZE(51, "Pipe Squeeze", new WorldPoint(3152, 9905, 0), ObjectID.VARROCK_DUNGEON_PIPE_SC),
	ARCEUUS_ESSENCE_MINE_EAST_SCRAMBLE(52, "Rock Climb", new WorldPoint(1770, 3851, 0), ObjectID.ARCHEUUS_RUNESTONE_SHORTCUT_MIDGREY_TOP, ObjectID.ARCHEUUS_RUNESTONE_SHORTCUT_MIDGREY_BOTTOM),
	KARAMJA_VOLCANO_GRAPPLE_NORTH(53, "Grapple Rock", new WorldPoint(2873, 3143, 0), ObjectID.XBOWS_JUNGLETREE_KARAMJA_BASIC),
	KARAMJA_VOLCANO_GRAPPLE_SOUTH(53, "Grapple Rock", new WorldPoint(2874, 3128, 0), ObjectID.XBOWS_JUNGLETREE_KARAMJA_BASIC),
	ALDARIN_ROCKS(54, "Rocks", new WorldPoint(1340, 2916, 0), ObjectID.ALDARIN_CLIFF_SHORTCUT_TOP, ObjectID.ALDARIN_CLIFF_SHORTCUT_BOTTOM),
	MOTHERLODE_MINE_WALL_EAST(54, "Wall", new WorldPoint(3124, 9703, 0), ObjectID.MOTHERLODE_SHORTCUT),
	MOTHERLODE_MINE_WALL_WEST(54, "Wall", new WorldPoint(3118, 9702, 0), ObjectID.MOTHERLODE_SHORTCUT),
	MISCELLANIA_DOCK_STEPPING_STONE(55, "Stepping Stone", new WorldPoint(2572, 3862, 0), ObjectID.MISC_DIARY_STEPPINGSTONE),
	TEMPLE_OF_EYE_RUBBLE(56, "Rubble", null, ObjectID.GOTR_AGILITY_SHORTCUT_TOP, ObjectID.GOTR_AGILITY_SHORTCUT_BOTTOM),
	BRIMHAVEN_DUNGEON_EAST_STEPPING_STONES_NORTH(56, "Stepping Stones", new WorldPoint(2685, 9547, 0), ObjectID.KARAMJA_DUNGEON_STEPPING_STONE_END),
	BRIMHAVEN_DUNGEON_EAST_STEPPING_STONES_SOUTH(56, "Stepping Stones", new WorldPoint(2693, 9529, 0), ObjectID.KARAMJA_DUNGEON_STEPPING_STONE_END),
	ISAFDAR_FOREST_OBSTACLES(56, "Trap", null, ObjectID.REGICIDE_CROSS_OVER2, ObjectID.REGICIDE_CROSS_OVER3, ObjectID.REGICIDE_CROSS_OVER2_TYRAS_CAMP, ObjectID.REGICIDE_CROSS_OVER1_TYRAS_CAMP, ObjectID.REGICIDE_CROSS_OVER1, ObjectID.REGICIDE_PITFALL_CORNER, ObjectID.REGICIDE_PITFALL_MID, ObjectID.REGICIDE_PITFALL_SIDE, ObjectID.REGICIDE_TRAP_WOODSPRING, ObjectID.REGICIDE_ROCK1_TRAP, ObjectID.REGICIDE_TRAP_TRIPWIRE),
	RELEKKA_EAST_FENCE(57, "Fence", new WorldPoint(2688, 3697, 0), ObjectID.VIKING_PIKE_DEFENCE_BROKEN),
	YANILLE_DUNGEON_MONKEY_BARS(57, "Monkey Bars", null, ObjectID.MONKEYBARS_END2),
	PHASMATYS_ECTOPOOL_SHORTCUT(58, "Weathered Wall", null , ObjectID.ECTOPOOL_SC_WALLCLIMB, ObjectID.ECTOPOOL_SC_RAILDOWN),
	ELVEN_OVERPASS_CLIFF_SCRAMBLE(59, "Rocks", new WorldPoint(2345, 3300, 0), ObjectID.ELVES_OVERPASS_SC_ROCKS_TOP, ObjectID.ELVES_OVERPASS_SC_ROCKS_BOTTOM),
	ELVEN_OVERPASS_CLIFF_SCRAMBLE_PRIFDDINAS(59, "Rocks", new WorldPoint(3369, 6052, 0), ObjectID.ELVES_OVERPASS_SC_ROCKS_TOP, ObjectID.ELVES_OVERPASS_SC_ROCKS_BOTTOM),
	ASGARNIA_ICE_DUNGEON_TUNNEL_EAST(60, "Tunnel", new WorldPoint(2989, 9547, 0), ObjectID.CAVEWALL_SHORTCUT_ROYAL_TITANS_EAST),
	ASGARNIA_ICE_DUNGEON_TUNNEL_WEST(60, "Tunnel", new WorldPoint(2968, 9549, 0), ObjectID.CAVEWALL_SHORTCUT_ROYAL_TITANS_WEST),
	WILDERNESS_GWD_CLIMB_EAST(60, "Rocks", new WorldPoint(2943, 3770, 0), ObjectID.GODWARS_CLIMBING_ROCKS_UP, ObjectID.GODWARS_CLIMBING_ROCKS_DOWN, ObjectID.GODWARS_CLIMBING_ROCKS_INACTIVE, ObjectID.GODWARS_CLIMBING_ROCKS_PLAIN_UP, ObjectID.GODWARS_CLIMBING_ROCKS_PLAIN_DOWN, ObjectID.GODWARS_CLIMBING_ROCKS_INACTIVE_PLAIN),
	WILDERNESS_GWD_CLIMB_WEST(60, "Rocks", new WorldPoint(2928, 3760, 0), ObjectID.GODWARS_CLIMBING_ROCKS_UP, ObjectID.GODWARS_CLIMBING_ROCKS_DOWN, ObjectID.GODWARS_CLIMBING_ROCKS_INACTIVE, ObjectID.GODWARS_CLIMBING_ROCKS_PLAIN_UP, ObjectID.GODWARS_CLIMBING_ROCKS_PLAIN_DOWN, ObjectID.GODWARS_CLIMBING_ROCKS_INACTIVE_PLAIN),
	MOS_LEHARMLESS_STEPPING_STONE(60, "Stepping Stone", new WorldPoint(3710, 2970, 0), ObjectID.MOS_LES_STEPPING_STONE),
	WINTERTODT_GAP(60, "Gap", new WorldPoint(1629, 4023, 0), ObjectID.WINT_PILLAR_JUMP),
	UNGAEL_ICE(60, "Ice Chunks", new WorldPoint(2262, 4044, 0), ObjectID.UNGAEL_CRATER_EXIT, ObjectID.UNGAEL_CRATER_SHORTCUT, ObjectID.UNGAEL_CRATER_SHORTCUT_TOP, ObjectID.UNGAEL_CRATER_SHORTCUT_BOTTOM, ObjectID.UNGAEL_CRATER_ENTRANCE_INSTANCE, ObjectID.UNGAEL_CRATER_EXIT_INSTANCE, ObjectID.UNGAEL_CRATER_ENTRANCE, ObjectID.UNGAEL_CRATER_ENTRANCE_INSTANCE_LEAVE, ObjectID.UNGAEL_CRATER_EXIT_INSTANCE_LEAVE),
	GWD_LITTLE_CRACK(60, "Little Crack", new WorldPoint(2900, 3712, 0), ObjectID.GODWARS_LITTLE_HOLE),
	SLAYER_TOWER_MEDIUM_CHAIN_FIRST(61, "Spikey Chain (Floor 1)", new WorldPoint(3421, 3550, 0), ObjectID.SLAYERTOWER_SC_CHAINBOTTOM),
	SLAYER_TOWER_MEDIUM_CHAIN_SECOND(61, "Spikey Chain (Floor 2)", new WorldPoint(3420, 3551, 0), ObjectID.SLAYERTOWER_SC_CHAINTOP),
	SLAYER_DUNGEON_CREVICE(61, "Narrow Crevice", new WorldPoint(2729, 10008, 0), ObjectID.SLAYER_DUNGEON_2_SC_WALL_CRACK),
	MOUNT_KARUULM_UPPER(62, "Rocks", new WorldPoint(1322, 3791, 0), ObjectID.MOUNT_KARUULM_SHORTCUT_ROCKS),
	NECROPOLIS_STEPPING_STONE_NORTH(62, "Stepping Stone", new WorldPoint(3293, 2706, 0), ObjectID.NECROPOLIS_STEPPING_STONE_2),
	NECROPOLIS_STEPPING_STONES_SOUTH(62, "Stepping Stones", new WorldPoint(3291, 2700, 0), ObjectID.NECROPOLIS_STEPPING_STONE_1),
	TAVERLEY_DUNGEON_RAILING(63, "Loose Railing", new WorldPoint(2935, 9811, 0), ObjectID.DEEPDUNGEONLOOSERAILING),
	FORTHOS_DUNGEON_SPIKED_BLADES(63, "Spiked Blades", new WorldPoint(1819, 9946, 0), ObjectID.HOSDUN_AGILITY_SHORTCUT),
	DARKMEYER_WALL(63, "Wall (Long rope)", new WorldPoint(3669, 3375, 0), ObjectID.DARKM_OUTER_WALL_3H_SHORTCUT, ObjectID.DARKM_OUTER_WALL_2H_SHORTCUT),
	TROLLHEIM_WILDERNESS_ROCKS_EAST(64, "Rocks", new WorldPoint(2945, 3678, 0), ObjectID.TROLLHEIM_WILDY_CLIMB_ROCKS),
	TROLLHEIM_WILDERNESS_ROCKS_WEST(64, "Rocks", new WorldPoint(2917, 3672, 0), ObjectID.TROLLHEIM_WILDY_CLIMB_ROCKS),
	FOSSIL_ISLAND_VOLCANO(64, "Rope", new WorldPoint(3780, 3822, 0), ObjectID.FOSSIL_VOLCANO_AGILITY_ROPE_BOTTOM, ObjectID.FOSSIL_VOLCANO_AGILITY_ROPE_TOP),
	MORYTANIA_TEMPLE(65, "Loose Railing", new WorldPoint(3422, 3476, 0), ObjectID.MORYTANIA_CLIMBINGROCKS_SC_TOP, ObjectID.MORYTANIA_CLIMBINGROCKS_SC_BOTTOM, ObjectID.MORYTANIA_RAILING_SC_FENCE_1, ObjectID.MORYTANIA_RAILING_SC_FENCE_2),
	REVENANT_CAVES_GREEN_DRAGONS(65, "Jump", new WorldPoint(3220, 10086, 0), ObjectID.WILD_CAVE_AGILITY_JUMP),
	COSMIC_ALTAR_ADVANCED_WALKWAY(66, "Narrow Walkway", new WorldPoint(2408, 4401, 0), ObjectID.FAIRY_SC_JUTTINGWALL),
	LUMBRIDGE_DESERT_STEPPING_STONE(66, "Stepping Stone", new WorldPoint(3210, 3135, 0), ObjectID.LUMBRIDGE_DIARY_DESERT_SHORTCUT),
	TAVERLEY_WALL_CLIMBING_ROCKS(66, "Climbing rocks", new WorldPoint(2945, 3439, 0), ObjectID.TAVELRYSHORTCUT),
	HEROES_GUILD_TUNNEL_EAST(67, "Crevice", new WorldPoint(2898, 9901, 0), ObjectID.HEROES_GUILD_SHORTCUT_TO_FOUNTAIN, ObjectID.HEROES_GUILD_SHORTCUT_FROM_FOUNTAIN),
	HEROES_GUILD_TUNNEL_WEST(67, "Crevice", new WorldPoint(2913, 9895, 0), ObjectID.HEROES_GUILD_SHORTCUT_TO_FOUNTAIN, ObjectID.HEROES_GUILD_SHORTCUT_FROM_FOUNTAIN),
	YANILLE_DUNGEON_RUBBLE_CLIMB(67, "Pile of Rubble", null, ObjectID.CLIMBINGCAVEROCKS1, ObjectID.CLIMBINGCAVEROCKS2),
	ELVEN_OVERPASS_MEDIUM_CLIFF(68, "Rocks", new WorldPoint(2337, 3288, 0), ObjectID.ELVES_OVERPASS_SC_ROCKS_TOP, ObjectID.ELVES_OVERPASS_SC_ROCKS_BOTTOM),
	ICE_MOUNTAIN_WESTERN_SCRAMBLE(68, "Rocks", new WorldPoint(2998, 3484, 0), ObjectID.ICE_MOUNTAIN_SHORTCUT_TOP, ObjectID.ICE_MOUNTAIN_SHORTCUT_BOTTOM),
	ELVEN_OVERPASS_MEDIUM_CLIFF_PRIFDDINAS(68, "Rocks", new WorldPoint(3361, 6040, 0), ObjectID.ELVES_OVERPASS_SC_ROCKS_TOP, ObjectID.ELVES_OVERPASS_SC_ROCKS_BOTTOM),
	WEISS_OBSTACLES(68, "Shortcut", null, ObjectID.MY2ARM_CLIFF_SHORTCUT_1, ObjectID.MY2ARM_CLIFF_SHORTCUT_2, ObjectID.MY2ARM_CLIFF_SHORTCUT_3, ObjectID.MY2ARM_CLIFF_SHORTCUT_3_ROPETRAIL_MULTI, ObjectID.MY2ARM_CLIFF_SHORTCUT_4, ObjectID.MY2ARM_CLIFF_SHORTCUT_5, ObjectID.MY2ARM_CLIFF_SHORTCUT_6),
	WEISS_FARMING_PATCH_BOULDER(0, "Shortcut", null, ObjectID.MY2ARM_HERBPATCH_ACCESS),
	ARCEUUS_ESSENSE_NORTH(69, "Rock Climb", new WorldPoint(1759, 3873, 0), ObjectID.ARCHEUUS_RUNESTONE_SHORTCUT_GREY_SHORTCUT_NORTH),
	TAVERLEY_DUNGEON_PIPE_BLUE_DRAGON(70, "Pipe Squeeze", new WorldPoint(2886, 9798, 0), ObjectID.TAVERLY_DUNGEON_PIPE_SC),
	TAVERLEY_DUNGEON_ROCKS_NORTH(70, "Rocks", new WorldPoint(2887, 9823, 0), ObjectID.TAVERLEY_DRAGON_JUMPUP, ObjectID.TAVERLEY_DRAGON_JUMPDOWN),
	TAVERLEY_DUNGEON_ROCKS_SOUTH(70, "Rocks", new WorldPoint(2887, 9631, 0), ObjectID.TAVERLEY_DRAGON_JUMPUP, ObjectID.TAVERLEY_DRAGON_JUMPDOWN),
	FOSSIL_ISLAND_HARDWOOD_NORTH(70, "Hole" , new WorldPoint(3712, 3828, 0), ObjectID.FOSSIL_SHORTCUT_BASECAMP_A, ObjectID.FOSSIL_SHORTCUT_BASECAMP_B),
	FOSSIL_ISLAND_HARDWOOD_SOUTH(70, "Hole" , new WorldPoint(3714, 3816, 0), ObjectID.FOSSIL_SHORTCUT_BASECAMP_A, ObjectID.FOSSIL_SHORTCUT_BASECAMP_B),
	AL_KHARID_WINDOW(70, "Window", new WorldPoint(3293, 3158, 0), ObjectID.KHARID_POSHWALL_TOPLESS, ObjectID.KHARID_BIGWINDOW)
	{
		@Override
		public boolean matches(Client client, TileObject object)
		{
			// there are two BIG_WINDOW objects right next to each other here, but only this one is valid
			return object.getId() != ObjectID.KHARID_BIGWINDOW || object.getWorldLocation().equals(new WorldPoint(3295, 3158, 0));
		}
	},
	GWD_SARADOMIN_ROPE_NORTH(70, "Rope Descent", new WorldPoint(2912, 5300, 0), ObjectID.GODWARS_ROCK_ROPE_MULTI1_BOTTOM, ObjectID.GODWARS_ROCK_ROPE_MULTI1),
	GWD_SARADOMIN_ROPE_SOUTH(70, "Rope Descent", new WorldPoint(2951, 5267, 0), ObjectID.GODWARS_ROCK_ROPE_MULTI2_BOTTOM, ObjectID.GODWARS_ROCK_ROPE_MULTI2),
	GU_TANOTH_CRUMBLING_WALL(71, "Rocks", new WorldPoint(2545, 3032, 0), ObjectID.GUTANOTH_WALL_SHORTCUT, ObjectID.GUTANOTH_ROCK_SHORTCUT),
	POLLNIVNEACH_STEPPING_STONE(71, "Stepping stone", new WorldPoint(3371, 2958, 0), ObjectID.POLLI_STEPPING_STONE),
	SLAYER_TOWER_ADVANCED_CHAIN_FIRST(71, "Spikey Chain (Floor 2)", new WorldPoint(3447, 3578, 0), ObjectID.SLAYERTOWER_SC_CHAINBOTTOM),
	SLAYER_TOWER_ADVANCED_CHAIN_SECOND(71, "Spikey Chain (Floor 3)", new WorldPoint(3446, 3576, 0), ObjectID.SLAYERTOWER_SC_CHAINTOP),
	PROUDSPIRE_UPPER_ROCKS(71, "Rocks", new WorldPoint(1576, 3251, 0), ObjectID.PROUDSPIRE_CLIMBING_ROCKS_GREY01_OP, ObjectID.PROUDSPIRE_CLIMBING_ROCKS_GREY03_OP),
	STRONGHOLD_SLAYER_CAVE_TUNNEL(72, "Tunnel", new WorldPoint(2431, 9806, 0), ObjectID.SLAYER_CAVE_TUNNEL_RIGHT, ObjectID.SLAYER_CAVE_TUNNEL_LEFT),
	ASGARNIA_ICE_DUNGEON_BASIC_NORTH(72, "Tunnel", new WorldPoint(3025, 9570, 0), ObjectID.CAVEWALL_SHORTCUT_WYVERN_NORTH),
	ASGARNIA_ICE_DUNGEON_BASIC_SOUTH(72, "Tunnel", new WorldPoint(3033, 9559, 0), ObjectID.CAVEWALL_SHORTCUT_WYVERN_SOUTH),
	CHAOS_TEMPLE_STEPPING_STONE(72, "Stepping stone", new WorldPoint(3267, 3628, 0), ObjectID.WILDERNESS_CHAOS_TEMPLE_SHORTCUT),
	BARROWS_WALL_JUMP(72, "Dry stone wall", new WorldPoint(3544, 3282, 0), ObjectID.BARROWS_DRYSTONEWALL_SHORTCUT),
	TROLL_STRONGHOLD_WALL_CLIMB(73, "Rocks", new WorldPoint(2841, 3694, 0), ObjectID.DIARY_TROLL_CLIMBINGROCKS),
	COF_PLATFORM_TOP(73, "Platform Edge", new WorldPoint(1309, 10099, 0), new WorldPoint(1438, 10098, 2), ObjectID.COF_AGILITY_LIP),
	COF_PLATFORM_MID(73, "Jump", new WorldPoint(1314, 10002, 0), new WorldPoint(1443, 10097, 1), ObjectID.COF_AGILITY_JUMP),
	ARCEUUS_ESSENSE_MINE_WEST(73, "Rock Climb", new WorldPoint(1742, 3853, 0), ObjectID.ARCHEUUS_RUNESTONE_SHORTCUT_GREY_TOP, ObjectID.ARCHEUUS_RUNESTONE_SHORTCUT_GREY_BOTTOM),
	LAVA_DRAGON_ISLE_JUMP(74, "Stepping Stone", new WorldPoint(3200, 3807, 0), ObjectID.WILDERNESS_LAVA_DRAGONS_SHORTCUT),
	MEIYERDITCH_LAB_TUNNELS_NORTH(74, "Cave", new WorldPoint(3623, 9747, 0), ObjectID.MYQ5_WALL_CAVE_SHORTCUT_1, ObjectID.MYQ5_WALL_CAVE_SHORTCUT_2),
	MEIYERDITCH_LAB_TUNNELS_SOUTH(74, "Cave", new WorldPoint(3618, 9722, 0), ObjectID.MYQ5_WALL_CAVE_SHORTCUT_3, ObjectID.MYQ5_WALL_CAVE_SHORTCUT_4),
	MOKHAIOTL_PIT_JUMP(75, "Jump", null, ObjectID.MOKI_AGIL_SHORTCUT_OP),
	REVENANT_CAVES_DEMONS_JUMP(75, "Jump", new WorldPoint(3199, 10135, 0), ObjectID.WILD_CAVE_AGILITY_JUMP),
	REVENANT_CAVES_ANKOU_EAST(75, "Jump", new WorldPoint(3201, 10195, 0), ObjectID.WILD_CAVE_AGILITY_JUMP),
	REVENANT_CAVES_ANKOU_NORTH(75, "Jump", new WorldPoint(3180, 10209, 0), ObjectID.WILD_CAVE_AGILITY_JUMP),
	DARKFROST_CLIFF_SCRAMBLE(76, "Rocks", new WorldPoint(1477, 3307, 0), ObjectID.DARKFROST_SHORTCUT_BOTTOM, ObjectID.DARKFROST_SHORTCUT_TOP),
	ZUL_ANDRA_ISLAND_CROSSING(76, "Stepping Stone", new WorldPoint(2156, 3073, 0), ObjectID.SNAKEBOSS_STEPPINGSTONE),
	WILDERNESS_SLAYER_CAVE_CREVICE_NORTH_EAST(77, "Crevice", new WorldPoint(3433, 10093, 0), ObjectID.WILDERNESS_SLAYER_CAVE_CREVICE),
	WILDERNESS_SLAYER_CAVE_CREVICE_SOUTH_EAST(77, "Crevice", new WorldPoint(3434, 10115, 0), ObjectID.WILDERNESS_SLAYER_CAVE_CREVICE),
	WILDERNESS_SLAYER_CAVE_CREVICE_NORTH_WEST(77, "Crevice", new WorldPoint(3341, 10149, 0), ObjectID.WILDERNESS_SLAYER_CAVE_CREVICE),
	WILDERNESS_SLAYER_CAVE_CREVICE_SOUTH_WEST(77, "Crevice", new WorldPoint(3333, 10119, 0), ObjectID.WILDERNESS_SLAYER_CAVE_CREVICE),
	IORWERTHS_DUNGEON_NORTHERN_SHORTCUT_EAST(78, "Tight Gap", new WorldPoint(3221, 12441, 0), ObjectID.PRIF_SLAYER_DUNGEON_SHORTCUT_1A),
	IORWERTHS_DUNGEON_NORTHERN_SHORTCUT_WEST(78, "Tight Gap", new WorldPoint(3215, 12441, 0), ObjectID.PRIF_SLAYER_DUNGEON_SHORTCUT_1B),
	SHILO_VILLAGE_ROCKS(79, "Rocks", new WorldPoint(2870, 3003, 0), ObjectID.SHORTCUT_SHILO_ROCKS_TOP, ObjectID.SHORTCUT_SHILO_ROCKS_BOTTOM),
	KHARAZI_JUNGLE_VINE_CLIMB(79, "Vine", new WorldPoint(2897, 2939, 0), ObjectID.KHARAZI_SHORTCUT_VINE_END, ObjectID.KHARAZI_SHORTCUT_VINE_DIAG1),
	TAVERLEY_DUNGEON_SPIKED_BLADES(80, "Strange Floor", new WorldPoint(2877, 9813, 0), ObjectID.TAVERLY_DUNGEON_FLOOR_SPIKES_SC),
	WATERBIRTH_DUNGEON_CREVICE(81, "Crevice", new WorldPoint(2604, 10070, 0), ObjectID.DAGANNOTH_CREVICE),
	LAVA_MAZE_NORTH_JUMP(82, "Stepping Stone", new WorldPoint(3092, 3880, 0), ObjectID.WILDERNESS_LAVA_MAZE_NORTHERN_SHORTCUT),
	ASGARNIA_ICE_DUNGEON_ADEPT_WEST(82, "Tunnel", new WorldPoint(3012, 9549, 0), ObjectID.CAVEWALL_SHORTCUT_WYVERN_WEST),
	ASGARNIA_ICE_DUNGEON_ADEPT_EAST(82, "Tunnel", new WorldPoint(3022, 9553, 0), ObjectID.CAVEWALL_SHORTCUT_WYVERN_EAST),
	COF_SHORTCUT_TOP(83, "Chain", new WorldPoint(1307, 10076, 0), new WorldPoint(1436, 10075, 2), ObjectID.COF_SHORTCUT_TOP),
	IORWERTHS_DUNGEON_SOUTHERN_SHORTCUT_EAST(84, "Tight Gap", new WorldPoint(3241, 12420, 0), ObjectID.PRIF_SLAYER_DUNGEON_SHORTCUT_2A),
	IORWERTHS_DUNGEON_SOUTHERN_SHORTCUT_WEST(84, "Tight Gap", new WorldPoint(3231, 12420, 0), ObjectID.PRIF_SLAYER_DUNGEON_SHORTCUT_2B),
	CRANDOR_ROCK_CLIMB(84, "Rocks", new WorldPoint(2831, 3252, 0), ObjectID.CRANDOR_SHORTCUT_TOP, ObjectID.CRANDOR_SHORTCUT_BOTTOM),
	ELVEN_ADVANCED_CLIFF_SCRAMBLE(85, "Rocks", new WorldPoint(2337, 3253, 0), ObjectID.ELVES_OVERPASS_SC_ROCKS_TOP, ObjectID.ELVES_OVERPASS_SC_ROCKS_BOTTOM),
	ELVEN_ADVANCED_CLIFF_SCRAMBLE_PRIFDDINAS(85, "Rocks", new WorldPoint(3361, 6005, 0), ObjectID.ELVES_OVERPASS_SC_ROCKS_TOP, ObjectID.ELVES_OVERPASS_SC_ROCKS_BOTTOM),
	WATERBIRTH_ISLAND_ROCKS(85, "Rocks", new WorldPoint(2546, 3750, 0), ObjectID.DAGANNOTH_WATERBIRTH_ROCK_CLIMB_AGILITY_SHORTCUT_BOTTOM, ObjectID.DAGANNOTH_WATERBIRTH_ROCK_CLIMB_AGILITY_SHORTCUT_TOP),
	KALPHITE_WALL(86, "Crevice", new WorldPoint(3214, 9508, 0), ObjectID.KALPHITE_WALL_SHORTCUT),
	BRIMHAVEN_DUNGEON_VINE_EAST(87, "Vine", new WorldPoint(2672, 9582, 0), ObjectID.KARAM_CAVEWALL_VINE_CLIMBABLE_BOTTOM, ObjectID.KARAM_CAVEWALL_VINE_CLIMBABLE_TOP),
	BRIMHAVEN_DUNGEON_VINE_WEST(87, "Vine", new WorldPoint(2606, 9584, 0), ObjectID.KARAM_CAVEWALL_VINE_CLIMBABLE_BOTTOM, ObjectID.KARAM_CAVEWALL_VINE_CLIMBABLE_TOP),
	MOUNT_KARUULM_PIPE_SOUTH(88, "Pipe", new WorldPoint(1316, 10214, 0), ObjectID.KARUULM_DUNGEON_SHORTCUT_PIPE_ACTIVE),
	MOUNT_KARUULM_PIPE_NORTH(88, "Pipe", new WorldPoint(1345, 10230, 0), ObjectID.KARUULM_DUNGEON_SHORTCUT_PIPE_ACTIVE),
	REVENANT_CAVES_CHAMBER_JUMP(89, "Jump", new WorldPoint(3240, 10144, 0), ObjectID.WILD_CAVE_AGILITY_JUMP),
	VIYELDI_ROCK_CLIMB(91, "Rocks", null, ObjectID.VIYELDI_SHORTCUT_ROCK_MULTI, ObjectID.VIYELDI_SHORTCUT_ROPE_BASE_MULTI),
	MEIYERDITCH_LAB_ADVANCED_TUNNELS_WEST(93, "Cave", new WorldPoint(3499, 9738, 0), ObjectID.MYQ5_WALL_CAVE_SHORTCUT_5),
	MEIYERDITCH_LAB_ADVANCED_TUNNELS_MIDDLE(93, "Cave", new WorldPoint(3597, 9704, 0), ObjectID.MYQ5_WALL_CAVE_SHORTCUT_6_PARENT),
	MEIYERDITCH_LAB_ADVANCED_TUNNELS_EAST(93, "Cave", new WorldPoint(3604, 9708, 0), ObjectID.MYQ5_WALL_CAVE_SHORTCUT_7, ObjectID.MYQ5_WALL_CAVE_SHORTCUT_8),
	VIYELDI_CAVES_CREVICE(96, "Crevice", null, ObjectID.LEGENDS_QUEST_CAVE_SHORTCUT);

	/**
	 * The agility level required to pass the shortcut
	 */
	@Getter
	private final int level;
	/**
	 * Brief description of the shortcut. (e.g. 'Rocks', 'Stepping Stones', 'Jump')
	 */
	@Getter
	private final String description;
	/**
	 * The location of the Shortcut icon on the world map (null if there is no icon)
	 */
	@Getter
	private final WorldPoint worldMapLocation;
	/**
	 * An optional location in case the location of the shortcut icon is either
	 * null or isn't close enough to the obstacle
	 */
	@Getter
	private final WorldPoint worldLocation;
	/**
	 * Array of obstacles, null objects, decorations etc. that this shortcut uses.
	 * Typically an ObjectID/NullObjectID
	 */
	@Getter
	private final int[] obstacleIds;

	AgilityShortcut(int level, String description, WorldPoint mapLocation, WorldPoint worldLocation, int... obstacleIds)
	{
		this.level = level;
		this.description = description;
		this.worldMapLocation = mapLocation;
		this.worldLocation = worldLocation;
		this.obstacleIds = obstacleIds;
	}

	AgilityShortcut(int level, String description, WorldPoint location, int... obstacleIds)
	{
		this(level, description, location, location, obstacleIds);
	}

	public String getTooltip()
	{
		return description + " - Level " + level;
	}

	public boolean matches(Client client, TileObject object)
	{
		return true;
	}
}
