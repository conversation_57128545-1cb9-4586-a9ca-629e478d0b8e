/*
 * Copyright (c) 2018, <PERSON> <<PERSON>@sigterm.info>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.ui.overlay.components;

import java.awt.Color;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InOrder;
import org.mockito.Mock;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TextComponentTest
{
	@Mock
	private Graphics2D graphics;

	@Before
	public void before()
	{
		when(graphics.getFontMetrics()).thenReturn(mock(FontMetrics.class));
	}

	@Test
	public void testRender()
	{
		TextComponent textComponent = new TextComponent();
		textComponent.setText("test");
		textComponent.setColor(Color.RED);
		textComponent.render(graphics);
		verify(graphics, times(2)).drawString(eq("test"), anyInt(), anyInt());
		verify(graphics).setColor(Color.RED);
	}

	@Test
	public void testRender2()
	{
		TextComponent textComponent = new TextComponent();
		textComponent.setText("<col=0000ff>test");
		textComponent.render(graphics);
		verify(graphics, times(2)).drawString(eq("test"), anyInt(), anyInt());
		verify(graphics).setColor(Color.BLUE);
	}

	@Test
	public void testRender3()
	{
		TextComponent textComponent = new TextComponent();
		textComponent.setText("<col=0000ff>test<col=00ff00> test");
		textComponent.render(graphics);
		verify(graphics, times(2)).drawString(eq("test"), anyInt(), anyInt());
		verify(graphics, times(2)).drawString(eq(" test"), anyInt(), anyInt());
		verify(graphics).setColor(Color.BLUE);
		verify(graphics).setColor(Color.GREEN);
	}

	@Test
	public void testRender4()
	{
		TextComponent textComponent = new TextComponent();
		textComponent.setText("test<col=0000ff>test2");
		textComponent.render(graphics);

		InOrder g = inOrder(graphics);
		g.verify(graphics).setColor(Color.BLACK);
		g.verify(graphics).drawString(eq("test"), anyInt(), anyInt());
		g.verify(graphics).setColor(Color.WHITE);
		g.verify(graphics).drawString(eq("test"), anyInt(), anyInt());

		g.verify(graphics).setColor(Color.BLACK);
		g.verify(graphics).drawString(eq("test2"), anyInt(), anyInt());
		g.verify(graphics).setColor(Color.BLUE);
		g.verify(graphics).drawString(eq("test2"), anyInt(), anyInt());
	}
}
