@echo off
setlocal enabledelayedexpansion

echo ========================================================
echo  COMPLETE SIDELOADED PLUGIN BUILD SCRIPT
echo  Compile + Build + Copy JARs - All in One!
echo ========================================================

REM Record start time
set start_time=%time%

REM Change to project directory
cd /d "C:\Users\<USER>\Documents\git\microbot"

echo [1/2] Compiling all changes...
mvn compile -pl runelite-client -DskipTests -q

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Compilation failed!
    goto :show_time_and_exit
)

echo [2/2] Building JARs and copying to all destinations...
echo       - x11-setup directory
echo       - All rscache\*\microbot-plugins directories

mvn -pl runelite-client exec:java@build-dynamic-sideloaded-plugins -q

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Plugin building/copying failed!
    goto :show_time_and_exit
)

echo.
echo [SUCCESS] ✅ Complete build finished!
echo.
echo 📁 JARs copied to:
echo    ✓ x11-setup directory
echo    ✓ All rscache profiles

:show_time_and_exit
REM Calculate elapsed time
set end_time=%time%

REM Convert times to seconds for calculation
for /f "tokens=1-4 delims=:.," %%a in ("%start_time%") do (
    set /a start_seconds=((%%a*3600) + (%%b*60) + %%c)
)
for /f "tokens=1-4 delims=:.," %%a in ("%end_time%") do (
    set /a end_seconds=((%%a*3600) + (%%b*60) + %%c)
)

set /a elapsed_seconds=end_seconds-start_seconds
if %elapsed_seconds% lss 0 set /a elapsed_seconds+=86400

echo.
echo ⏱️  Build completed in %elapsed_seconds% seconds
echo.
pause
